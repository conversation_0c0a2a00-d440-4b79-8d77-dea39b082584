{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["es2020", "dom"], "declaration": false, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "noImplicitThis": true, "alwaysStrict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": false, "inlineSourceMap": true, "inlineSources": true, "experimentalDecorators": true, "strictPropertyInitialization": false, "esModuleInterop": true, "typeRoots": ["./node_modules/@types"]}, "exclude": ["node_modules", "cdk.out"]}
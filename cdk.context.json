{"vpc-provider:account=************:filter.tag:Name=raleon-vpc:region=us-east-1:returnAsymmetricSubnets=true": {"vpcId": "vpc-0200adbdcb0c394ea", "vpcCidrBlock": "10.0.0.0/16", "availabilityZones": [], "subnetGroups": [{"name": "Public", "type": "Public", "subnets": [{"subnetId": "subnet-0d1ac95c3125fa16a", "cidr": "10.0.0.0/20", "availabilityZone": "us-east-1a", "routeTableId": "rtb-01dd9e17115dd1065"}, {"subnetId": "subnet-0d04b4a02ffa438bd", "cidr": "*********/20", "availabilityZone": "us-east-1b", "routeTableId": "rtb-01dd9e17115dd1065"}]}, {"name": "Private", "type": "Private", "subnets": [{"subnetId": "subnet-052abf7246ee809a7", "cidr": "**********/20", "availabilityZone": "us-east-1a", "routeTableId": "rtb-05212b8fed23bc798"}, {"subnetId": "subnet-088cc0c0404f481bb", "cidr": "**********/20", "availabilityZone": "us-east-1b", "routeTableId": "rtb-05212b8fed23bc798"}]}]}, "vpc-provider:account=************:filter.isDefault=true:region=eu-west-3:returnAsymmetricSubnets=true": {"vpcId": "vpc-024d6cb2769cf770d", "vpcCidrBlock": "172.31.0.0/16", "availabilityZones": [], "subnetGroups": [{"name": "Public", "type": "Public", "subnets": [{"subnetId": "subnet-01d82e121ee16f2cb", "cidr": "172.31.0.0/20", "availabilityZone": "eu-west-3a", "routeTableId": "rtb-0f76804d1bcf3e352"}, {"subnetId": "subnet-0d68eaa39d27bf1a6", "cidr": "172.31.16.0/20", "availabilityZone": "eu-west-3b", "routeTableId": "rtb-0f76804d1bcf3e352"}, {"subnetId": "subnet-0c39b6f59f248334e", "cidr": "172.31.32.0/20", "availabilityZone": "eu-west-3c", "routeTableId": "rtb-0f76804d1bcf3e352"}]}]}, "vpc-provider:account=************:filter.isDefault=false:region=us-east-1:returnAsymmetricSubnets=true": {"vpcId": "vpc-0200adbdcb0c394ea", "vpcCidrBlock": "10.0.0.0/16", "ownerAccountId": "************", "availabilityZones": [], "subnetGroups": [{"name": "Public", "type": "Public", "subnets": [{"subnetId": "subnet-07b87e2ff12904267", "cidr": "**********/24", "availabilityZone": "us-east-1a", "routeTableId": "rtb-0e8134289940b3e9a"}, {"subnetId": "subnet-0d1ac95c3125fa16a", "cidr": "10.0.0.0/20", "availabilityZone": "us-east-1a", "routeTableId": "rtb-01dd9e17115dd1065"}, {"subnetId": "subnet-052abf7246ee809a7", "cidr": "**********/20", "availabilityZone": "us-east-1a", "routeTableId": "rtb-0e8134289940b3e9a"}, {"subnetId": "subnet-088cc0c0404f481bb", "cidr": "**********/20", "availabilityZone": "us-east-1b", "routeTableId": "rtb-0e8134289940b3e9a"}, {"subnetId": "subnet-0938912d0330ab561", "cidr": "**********/24", "availabilityZone": "us-east-1b", "routeTableId": "rtb-0e8134289940b3e9a"}, {"subnetId": "subnet-0d04b4a02ffa438bd", "cidr": "*********/20", "availabilityZone": "us-east-1b", "routeTableId": "rtb-01dd9e17115dd1065"}]}]}, "hosted-zone:account=************:domainName=raleon.io:region=us-east-1": {"Id": "/hostedzone/Z01296133PMXNMB2DGDH7", "Name": "raleon.io."}, "security-group:account=************:region=us-east-1:securityGroupName=default:vpcId=vpc-0200adbdcb0c394ea": {"securityGroupId": "sg-058f591019f25a11a", "allowAllOutbound": false}, "vpc-provider:account=************:filter.vpc-id=vpc-0200adbdcb0c394ea:region=us-east-1:returnAsymmetricSubnets=true": {"vpcId": "vpc-0200adbdcb0c394ea", "vpcCidrBlock": "10.0.0.0/16", "ownerAccountId": "************", "availabilityZones": [], "subnetGroups": [{"name": "Public", "type": "Public", "subnets": [{"subnetId": "subnet-07b87e2ff12904267", "cidr": "**********/24", "availabilityZone": "us-east-1a", "routeTableId": "rtb-0e8134289940b3e9a"}, {"subnetId": "subnet-0d1ac95c3125fa16a", "cidr": "10.0.0.0/20", "availabilityZone": "us-east-1a", "routeTableId": "rtb-0e8134289940b3e9a"}, {"subnetId": "subnet-052abf7246ee809a7", "cidr": "**********/20", "availabilityZone": "us-east-1a", "routeTableId": "rtb-0e8134289940b3e9a"}, {"subnetId": "subnet-088cc0c0404f481bb", "cidr": "**********/20", "availabilityZone": "us-east-1b", "routeTableId": "rtb-0e8134289940b3e9a"}, {"subnetId": "subnet-0938912d0330ab561", "cidr": "**********/24", "availabilityZone": "us-east-1b", "routeTableId": "rtb-0e8134289940b3e9a"}, {"subnetId": "subnet-0d04b4a02ffa438bd", "cidr": "*********/20", "availabilityZone": "us-east-1b", "routeTableId": "rtb-0e8134289940b3e9a"}]}]}, "security-group:account=************:region=us-east-1:securityGroupName=raleon-webapp-test:vpcId=vpc-0200adbdcb0c394ea": {"securityGroupId": "sg-0114611684e346494", "allowAllOutbound": false}, "security-group:account=************:region=us-east-1:securityGroupName=raleon-load-balancer:vpcId=vpc-0200adbdcb0c394ea": {"securityGroupId": "sg-0070dbbbd04e52873", "allowAllOutbound": false}, "security-group:account=************:region=us-east-1:securityGroupName=raleon-webapp-development:vpcId=vpc-0200adbdcb0c394ea": {"securityGroupId": "sg-05593d2d72b08073d", "allowAllOutbound": false}}
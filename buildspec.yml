version: 0.2
phases:
  install:
    runtime-versions:
      nodejs: latest
    commands:
      - echo Installing
      - npm ci
      - npm i -g aws-cdk
  build:
    commands:
      - echo Building and Deploying Production Stacks
      - echo "${IS_DEV}"
      - |
        if [ "${IS_DEV}" = "true" ]; then
          cdk deploy '*' -c suffix=dev --require-approval never
        else
          cdk deploy '*' -c suffix=prod --require-approval never
        fi
artifacts:
  files:
    - '**/*'
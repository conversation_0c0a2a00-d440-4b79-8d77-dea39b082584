#!/usr/bin/env node
import "source-map-support/register";
import * as cdk from "aws-cdk-lib";
import { ShopifyEventStack } from "../lib/shopify-event-stack";
import { ShopifyPipelineStack } from "../lib/shopify-pipeline-stack";
import { ShopifyAppInstanceStack } from "../lib/shopify-app-instance-stack";
import { ShopifyWebSocketStack } from "../lib/shopify-websocket-stack";
import { hasPipeline, isDev } from "../lib/utils";
import { Stack } from "aws-cdk-lib";
import { ShopifyAppProxyStack } from "../lib/shopify-app-proxy-stack";
import { ShopifyMetricsStack } from "../lib/shopify-metrics-stack";
import { ShopifyOffersStack } from "../lib/shopify-offers-stack";
import { ShopifyGDPRStack } from "../lib/shopify-gdpr-stack";
import { ShopifyOnboardStack } from "../lib/shopify-app-onboard-stack";
import { ShopifyStreamingStack } from "../lib/shopify-streaming-stack";
import { ShopifyLoyaltyEmailStack } from "../lib/shopify-loyalty-email.stack";
import { ShopifyFreeTrialStack } from "../lib/shopify-free-trial-stack";
import { ShopifySharedStack } from "../lib/shopify-shared-resources.stack";
import { ShopifySendlaneSyncStack } from "../lib/shopify-sendlane-sync-stack";
import { ChatStack } from "../lib/chat-stack";
import { WebappInternalLoadBalancerStack } from "../lib/webapp-internal-lb-stack";


const app = new cdk.App();
const accountID: string = "************";
const accountRegion: string = "us-east-1";
const suffix: string = app.node.tryGetContext("suffix") ?? "prod";
const deploy_env: string = suffix == "prod" ? "production" : "development";

const sharedStack = new ShopifySharedStack(app, getFullName("ShopifySharedStack", suffix), {
	deploy_environment_name: deploy_env,
	env: { account: accountID, region: accountRegion }
});
const webSocketStack = new ShopifyWebSocketStack(app, getFullName("ShopifyWebSocketStack", suffix), {
	deploy_environment_name: deploy_env,
	env: { account: accountID, region: accountRegion },
});
const eventStack = new ShopifyEventStack(app, getFullName("ShopifyEventStack", suffix), {
	deploy_environment_name: deploy_env,
	env: { account: accountID, region: accountRegion },
	webSocketApi: webSocketStack.webSocketApi,
	chatConnectionsTable: webSocketStack.chatConnectionsTable,
	metricQueue: sharedStack.metricQueue
})

const stacks: Stack[] = [
	webSocketStack,
	eventStack,
	new ShopifyMetricsStack(app, getFullName('ShopifyMetricsStack', suffix), {
		deploy_environment_name: deploy_env,
		athenaOutput: eventStack.athenaOutput,
		curatedBucket: eventStack.curatedBucket,
		metricQueue: sharedStack.metricQueue,
		env: { account: accountID, region: accountRegion },

	}),
	new ShopifyOffersStack(app, getFullName('ShopifyOffersStack', suffix), {
		deploy_environment_name: deploy_env,
		athenaOutput: eventStack.athenaOutput,
		curatedBucket: eventStack.curatedBucket,
		env: { account: accountID, region: accountRegion },

	}),
	new ShopifyGDPRStack(app, getFullName('ShopifyGDPRStack', suffix), {
		deploy_environment_name: deploy_env,
		env: { account: accountID, region: accountRegion },
		curatedBucket: eventStack.curatedBucket,
	}),
	new ShopifyOnboardStack(app, getFullName('ShopifyOnboardStack', suffix), {
		deploy_environment_name: deploy_env,
		env: { account: accountID, region: accountRegion },
	}),
	new ShopifyLoyaltyEmailStack(app, getFullName('ShopifyLoyaltyEmailStack', suffix), {
		deploy_environment_name: deploy_env,
		env: { account: accountID, region: accountRegion },
	}),
	new ShopifyFreeTrialStack(app, getFullName('ShopifyFreeTrialStack', suffix), {
		deploy_environment_name: deploy_env,
		env: { account: accountID, region: accountRegion },
	}),
	new ShopifySendlaneSyncStack(app, getFullName('ShopifySendlaneSyncStack', suffix), {
		deploy_environment_name: deploy_env,
		env: { account: accountID, region: accountRegion },
	}),
	new ChatStack(app, getFullName('ChatStack', suffix), {
		deploy_environment_name: deploy_env,
		env: { account: accountID, region: accountRegion },
	}),
	
];

const streamingStack = new ShopifyStreamingStack(app, getFullName('ShopifyStreamingStack', suffix), {
	deploy_environment_name: deploy_env,
	env: { account: accountID, region: accountRegion }
});

const proxyStack = new ShopifyAppProxyStack(app, getFullName('ShopifyAppProxy', suffix), {
	deploy_environment_name: deploy_env,
	env: { account: accountID, region: accountRegion },
	curatedBucket: streamingStack.curatedBucket
});
stacks.push(proxyStack, streamingStack);

if (hasPipeline(suffix)) {
	stacks.push(
		new ShopifyAppInstanceStack(app, getFullName("ShopifyAppInstanceStack", suffix), {
			deploy_environment_name: deploy_env,
			env: { account: accountID, region: accountRegion },
		}),
		new ShopifyPipelineStack(app, getFullName("ShopifyPipelineStack", suffix), {
			deploy_environment_name: deploy_env,
			env: { account: accountID, region: accountRegion },
		}),
		new WebappInternalLoadBalancerStack(app, getFullName('WebappInternalLbStack', suffix), {
			deploy_environment_name: deploy_env,
			env: { account: accountID, region: accountRegion },
		}),
	);

	stacks.push(

	);

}


addTags();

function addTags() {
	stacks.forEach((stack) => {
		cdk.Tags.of(stack).add("stack-suffix", suffix);
	});
}
function getFullName(stackName: string, suffix: string): string {
	if (suffix == "prod") {
		return stackName;
	}
	return `${stackName}-${suffix}`;
}
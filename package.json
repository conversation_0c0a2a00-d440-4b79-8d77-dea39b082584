{"name": "shopify-backend", "version": "0.1.0", "bin": {"shopify-backend": "bin/shopify-backend.js"}, "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk", "deploy": "cdk deploy '*' -c suffix=$(whoami) --require-approval never"}, "devDependencies": {"@aws-cdk/aws-apigatewayv2": "^1.204.0", "@aws-cdk/aws-apigatewayv2-alpha": "^2.100.0-alpha.0", "@aws-cdk/aws-apigatewayv2-integrations": "^1.204.0", "@aws-cdk/aws-apigatewayv2-integrations-alpha": "^2.100.0-alpha.0", "@aws-cdk/aws-lambda-python-alpha": "^2.100.0-alpha.0", "@types/aws-lambda": "^8.10.119", "@types/jest": "^29.5.3", "@types/memcached": "^2.2.10", "@types/node": "20.4.2", "@types/parquetjs": "^0.10.3", "@types/pg": "^8.10.2", "aws-cdk": "^2.95.1", "aws-cdk-lib": "^2.96.2", "aws-sdk": "^2.1125.0", "esbuild": "^0.19.2", "jest": "^29.6.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "~5.1.6"}, "dependencies": {"@aws-sdk/client-apigatewaymanagementapi": "^3.414.0", "@aws-sdk/client-apigatewayv2": "^3.414.0", "@aws-sdk/client-athena": "^3.414.0", "@aws-sdk/client-dynamodb": "^3.409.0", "@aws-sdk/client-firehose": "^3.596.0", "@aws-sdk/client-lambda": "^3.414.0", "@aws-sdk/client-s3": "^3.414.0", "@aws-sdk/client-secrets-manager": "^3.398.0", "@aws-sdk/client-ses": "^3.637.0", "@aws-sdk/client-sfn": "^3.787.0", "@aws-sdk/client-sns": "^3.429.0", "@aws-sdk/lib-dynamodb": "^3.789.0", "@aws-sdk/lib-storage": "^3.0.0", "@aws-sdk/s3-request-presigner": "^3.484.0", "@aws-sdk/util-dynamodb": "^3.651.1", "@dsnp/parquetjs": "^1.8.6", "@sparticuz/chromium": "122.0.0", "@types/jsonwebtoken": "^9.0.3", "@types/node-fetch": "^2.6.12", "@types/xml2js": "^0.4.14", "constructs": "^10.0.0", "crypto": "^1.0.1", "graphql-tag": "^2.12.6", "https": "^1.0.0", "json5": "^2.2.3", "jsonwebtoken": "^9.0.2", "lzo": "^0.4.11", "memcached": "^2.2.2", "node-fetch": "^3.3.2", "openai": "^4.14.2", "parquetjs-lite": "^0.8.7", "pg": "^8.11.3", "postgres": "^3.4.3", "puppeteer-core": "^19.8.0", "robots-parser": "^3.0.1", "source-map-support": "^0.5.21", "unsplash-js": "^7.0.19", "xml2js": "^0.6.2"}}
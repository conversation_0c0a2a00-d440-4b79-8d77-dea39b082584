import { Duration, Stack, StackProps } from 'aws-cdk-lib';
import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { RestApi, LambdaIntegration } from 'aws-cdk-lib/aws-apigateway';
import { NodejsFunction } from 'aws-cdk-lib/aws-lambda-nodejs';
import { Runtime } from 'aws-cdk-lib/aws-lambda';
import * as path from "path";
import * as dynamodb from "aws-cdk-lib/aws-dynamodb";
import { getSuffix, isDev } from './utils';
import { RetentionDays } from 'aws-cdk-lib/aws-logs';
import { Vpc, SubnetType, Subnet } from 'aws-cdk-lib/aws-ec2';
import { Secret } from 'aws-cdk-lib/aws-secretsmanager';
import { PolicyStatement, Effect } from 'aws-cdk-lib/aws-iam';
import { Bucket } from 'aws-cdk-lib/aws-s3';

interface ShopifyGDPRStackProps extends StackProps {
	deploy_environment_name: string;
    curatedBucket: Bucket;
}

export class ShopifyGDPRStack extends Stack {
	private isDev: boolean;
    private apiGateway: RestApi;
    private gdprDB: dynamodb.Table;
    private gdprLambda: NodejsFunction;

	constructor(scope: Construct, id: string, private props?: ShopifyGDPRStackProps) {
		super(scope, id, props);
		this.isDev = isDev(props!.deploy_environment_name);
		this.createAPIGateway();
        this.createDynamoDB();
        this.createLambda();
        this.linkLambdaToAPIGateway();
	}

	private createAPIGateway() {
        this.apiGateway = new RestApi(this, `ShopifyGDPR-${getSuffix(this)}`, {
            restApiName: `shopify-gdpr-${getSuffix(this)}`,
            description: 'This service serves as GDPR endpoints for reporting.',
            deployOptions: {
                loggingLevel: cdk.aws_apigateway.MethodLoggingLevel.INFO,
                dataTraceEnabled: true,
                metricsEnabled: true,
                accessLogDestination: new cdk.aws_apigateway.LogGroupLogDestination(
                    new cdk.aws_logs.LogGroup(this, `APIGatewayAccessLogs-${getSuffix(this)}`, {
                        logGroupName: `/aws/apigateway/shopify-gdpr-${getSuffix(this)}`,
                        removalPolicy: cdk.RemovalPolicy.DESTROY,
                        retention: RetentionDays.TWO_WEEKS,
                    })
                ),
                accessLogFormat: cdk.aws_apigateway.AccessLogFormat.jsonWithStandardFields(),
            },
        });
    }

    private createDynamoDB() {
        this.gdprDB = new dynamodb.Table(this, 'GDPR', {
            tableName: `gdpr-data-requests-${getSuffix(this)}`,
            partitionKey: { name: 'shop_id', type: dynamodb.AttributeType.STRING },
            sortKey: { name: 'date_processed', type: dynamodb.AttributeType.STRING },
            billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
            removalPolicy: cdk.RemovalPolicy.DESTROY,
        });
    }

    private createLambda() {
		const dbSecretArn = this.node.tryGetContext(
			getSuffix(this) == 'prod' 
			? 'shopify-db-secret-arn' 
			: 'shopify-db-dev-secret-arn');
			
		const appSecretArn = this.node.tryGetContext(
			getSuffix(this) == 'prod' 
			? 'shopify-app-secret-arn'
			: 'shopify-app-dev-secret-arn'
		);
        const dbSecret = Secret.fromSecretCompleteArn(
			this,
			`ShopifyDBSecret-${getSuffix(this)}`,
			dbSecretArn,
		);
		const appSecret = Secret.fromSecretCompleteArn(
			this,
			`ShopifyAppSecret-${getSuffix(this)}`,
			appSecretArn,
		);
        const raleonAppDbSecret = Secret.fromSecretNameV2(
			this, 
			`raleonwebapp-id-${getSuffix(this)}`, 
			this.isDev ? 'raleonwebapp': 'raleonwebapp-prod'
		);

        const vpc = Vpc.fromLookup(
			this,
			`raleon-vpc-shopify-${getSuffix(this)}`,
			{ vpcName: "raleon-vpc" },
		);


        this.gdprLambda = new NodejsFunction(this, `shopify-gdpr-${getSuffix(this)}`, {
            functionName: `shopify-gdpr-${getSuffix(this)}`,
            memorySize: 256,
            timeout: Duration.minutes(10),
            runtime: Runtime.NODEJS_18_X,
            handler: 'main',
            logRetention: RetentionDays.TWO_WEEKS,
            logRetentionRetryOptions: {
                base: Duration.seconds(10),
                maxRetries: 100
            },
            entry: path.join(__dirname, '/../src/gdpr/gdpr_event.ts'),
            environment: {
                SECRET_ARN: dbSecret.secretArn,
                SHOPIFY_SECRET_KEY_ARN: appSecret.secretArn,
                TABLE_NAME: this.gdprDB.tableName,
                RALEON_DB: raleonAppDbSecret.secretArn,
                CURATED_BUCKET: this.props?.curatedBucket.bucketName || "",
            },
            vpc,
            vpcSubnets: {
                subnets: [
					Subnet.fromSubnetId(
						this,
						`raleon-subnet-shopify-gdpr-${getSuffix(this)}`,
						"subnet-04ef7beb8a56268d1"
					),
				],
            }
        });
        this.gdprDB.grantReadWriteData(this.gdprLambda);
        dbSecret.grantRead(this.gdprLambda);
        appSecret.grantRead(this.gdprLambda);
        raleonAppDbSecret.grantRead(this.gdprLambda);
        this.props?.curatedBucket.grantReadWrite(this.gdprLambda);

        const snsPublishPolicy = new PolicyStatement({
            effect: Effect.ALLOW,
            actions: ['sns:Publish'],
            resources: ['arn:aws:sns:us-east-1:831543322268:gdpr-request']
        });
        
        this.gdprLambda.addToRolePolicy(snsPublishPolicy);
    }

    private linkLambdaToAPIGateway() {
        const gdprLambdaIntegration = new LambdaIntegration(this.gdprLambda);
        const gdprResource = this.apiGateway.root.addResource('gdpr');
        const gdprResource_customers = gdprResource.addResource('customers');
        const gdprResource_shop = gdprResource.addResource('shop');
        const customer_data_request = gdprResource_customers.addResource('data_request');
        const customer_redact = gdprResource_customers.addResource('redact');
        const shop_redact = gdprResource_shop.addResource('redact');
        customer_data_request.addMethod('POST', gdprLambdaIntegration);
        customer_redact.addMethod('POST', gdprLambdaIntegration);
        shop_redact.addMethod('POST', gdprLambdaIntegration);
    }
}

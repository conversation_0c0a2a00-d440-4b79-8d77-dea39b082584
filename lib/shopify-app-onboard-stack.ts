import { Duration, Stack, StackProps, Aws, RemovalPolicy } from 'aws-cdk-lib';
import { Queue } from "aws-cdk-lib/aws-sqs";
import * as sns from 'aws-cdk-lib/aws-sns';
import * as subs from 'aws-cdk-lib/aws-sns-subscriptions';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import * as iam from 'aws-cdk-lib/aws-iam';
import { Construct } from 'constructs';
import { RestApi, LambdaIntegration, AuthorizationType } from 'aws-cdk-lib/aws-apigateway';
import { NodejsFunction } from 'aws-cdk-lib/aws-lambda-nodejs';
import { Function, Runtime, Code } from 'aws-cdk-lib/aws-lambda';
import * as path from "path";
import * as Utils from './utils';
import { RetentionDays } from 'aws-cdk-lib/aws-logs';
import { Vpc, SubnetType, IVpc, SecurityGroup, ISecurityGroup } from 'aws-cdk-lib/aws-ec2';
import * as sfn from 'aws-cdk-lib/aws-stepfunctions';
import { SqsEventSource } from 'aws-cdk-lib/aws-lambda-event-sources';
import s3 = require('aws-sdk/clients/s3');
import { Bucket, BucketPolicy } from 'aws-cdk-lib/aws-s3';
import { Secret } from 'aws-cdk-lib/aws-secretsmanager';
import { SfnStateMachine } from 'aws-cdk-lib/aws-events-targets';
import { json, dependencyJson, brandingJson, programJson, memberInsightsJson, knowledgeGatheringJson } from './shopify-ai-step-function';

interface ShopifyOnboardStackProps extends StackProps {
    deploy_environment_name: string;
}

export class ShopifyOnboardStack extends Stack {
    private isDev: boolean;
    private sg: ISecurityGroup;
    private onboardQueue: Queue;
    private onboardLambda: NodejsFunction;
    private screenshotBucket: Bucket;
    private scrapeBucket: Bucket;
    private catalogBucket: Bucket;
    private aiCampaignBucket: Bucket;
    private webScraperLambda: NodejsFunction;
    private postgresLambda: NodejsFunction;
    private gptLambda: NodejsFunction;
    private gptPromptLambda: NodejsFunction;
    private gptVisionPromptLambda: NodejsFunction;
    private shopifyCatalogSummarizerLambda: NodejsFunction;
    private gptToFullJsonFunction: NodejsFunction;
    private jsonToPostgresLambda: NodejsFunction;
    private jsonExtractorLambda: NodejsFunction;
    private jsonParserLambda: NodejsFunction;
    private jsonStringifierLambda: NodejsFunction;
    private shopifyTokenLambda: NodejsFunction;
    private shopifyApiInvokerLambda: NodejsFunction;
    private apiGateway: RestApi;
    private trubricsLambda: Function;
    private campaignPersistLambda: NodejsFunction;
    private onboardStepFunction: sfn.CfnStateMachine;
    private gatewayExecutionRole: iam.Role;
    private gatewayLambdaExecutionRole: iam.Role;
    private websiteQaLambda: NodejsFunction;
    private llmRouterLambda: NodejsFunction;

    constructor(scope: Construct, id: string, props?: ShopifyOnboardStackProps) {
        super(scope, id, props);
        this.isDev = Utils.isDev(props!.deploy_environment_name);

		
        const vpc = Vpc.fromLookup(
			this,
			`raleon-vpc-shopify_2-${Utils.getSuffix(this)}`,
			{ vpcName: "raleon-vpc" },
		);

        this.getSecurityGroup(vpc);
        this.createOnboardTaskQueue();
        this.createResultBuckets(vpc);
        this.setupStepFunction();
        this.createTaskWorkerLambda();
        this.createAPIGateway();
        this.pushMessageOnQueue();
        this.createWebScraperLambda(vpc);
		this.createBrandingPostProcessorLambda();
        this.createGptVisionPromptGeneratorLambda(vpc);
        this.createGptPromptGeneratorLambda(vpc);
        this.createGptLambda(vpc);
        this.createPostgresLambda(vpc);
        this.createShopifyTokenLambda(vpc);
        this.createShopifyApiInvokerLambda(vpc);
        this.createShopifyCatalogSummarizerLambda(vpc);
        this.createGptToFullJsonLambda(vpc);
        this.createJsonToPostgresLambda(vpc);
        this.createJsonExtractorLambda(vpc);
        this.createJsonParserLambda(vpc);
        this.createJsonStringifierLambda(vpc);
        this.createCampaignPersistLambda(vpc);
        this.campaignPersistPost(vpc);
        this.createTrubricsLambda(vpc);
        this.createWebsiteQaLambda(vpc);
        this.createLLMRouterLambda();
	}

    getSecurityGroup(vpc: IVpc) {
        this.sg = SecurityGroup.fromLookupByName(
            this,
            "default",
            "default",
            vpc
        );
    }

    createOnboardTaskQueue() {
        const deadletterqueue = new Queue(this, `onboard-deadletter-task-queue`, {
            queueName: `onboard-deadletter-task-queue-${Utils.getSuffix(this)}`,
            retentionPeriod: Duration.days(10)
        });
        this.onboardQueue = new Queue(this, `onboard-task-queue`, {
            queueName: `onboard-task-queue-${Utils.getSuffix(this)}`,
            deadLetterQueue: {
                queue: deadletterqueue,
                maxReceiveCount: 3
            },
            visibilityTimeout: Duration.seconds(900)
        });
    }

    private createAPIGateway() {
        this.apiGateway = new RestApi(this, `shopify-onboard-${Utils.getSuffix(this)}`, {
            restApiName: `shopify-onboard-${Utils.getSuffix(this)}`,
            description: 'This service serves as onboarding process.',
        });
    }

    createTaskWorkerLambda() {        
        const lambdaRole = new iam.Role(this, 'LambdaRole', {
            assumedBy: new iam.ServicePrincipal('lambda.amazonaws.com'),
            });
        
            // Grant permissions to invoke the Step Function
            lambdaRole.addToPolicy(new iam.PolicyStatement({
            actions: ['states:StartExecution'],
            resources: [this.onboardStepFunction.attrArn],
            }));

            
          
        // Inline policy to grant permission to start the Step Functions execution
        lambdaRole.attachInlinePolicy(new iam.Policy(this, `StateMachineExecutionPolicy-${Utils.getSuffix(this)}`, {
            statements: [
                new iam.PolicyStatement({
                actions: ['states:StartExecution'],
                resources: [this.onboardStepFunction.attrArn], // ARN of your Step Functions state machine
                }),
            ],
        }));

        const vpc = Vpc.fromLookup(
            this,
            `raleon-vpc-shopify-${Utils.getSuffix(this)}`,
            { vpcName: "raleon-vpc" },
        );
        this.onboardLambda = new NodejsFunction(this, `onboard-task-lambda-${Utils.getSuffix(this)}`, {
            functionName: `onboard-task-lambda-${Utils.getSuffix(this)}`,
            memorySize: 256,
            timeout: Duration.seconds(60),
            runtime: Runtime.NODEJS_18_X,
            handler: 'main',
            logRetention: RetentionDays.THREE_DAYS,
            role: lambdaRole,
            logRetentionRetryOptions: {
                base: Duration.seconds(10),
                maxRetries: 100
            },
            entry: path.join(__dirname, '/../src/onboard/onboard_task_start.ts'),
            environment: {
                QUEUE_URL: this.onboardQueue.queueUrl,
                ONBOARD_STEP_FUNCTION_ARN: this.onboardStepFunction.attrArn,
                PROGRAM_RESULT_S3_BUCKET_NAME: this.aiCampaignBucket.bucketName,
                SCREENSHOT_S3_BUCKET_NAME: this.screenshotBucket.bucketName,
                SCRAPE_RESULT_S3_BUCKET_NAME: this.scrapeBucket.bucketName,
                CATALOG_RESULT_S3_BUCKET_NAME: this.catalogBucket.bucketName,
            }
        });

        this.onboardLambda.addEventSource(
            new SqsEventSource(this.onboardQueue, {
                batchSize: 1,
                maxBatchingWindow: Duration.seconds(5)
            })
        )
    }
	
    createWebScraperLambda(vpc: IVpc) {

        // ...

        //this.screenshotBucket.grantPublicAccess();
        this.webScraperLambda = new NodejsFunction(this, `onboard-web-scraper-${Utils.getSuffix(this)}`, {
            functionName: `onboard-web-scraper-${Utils.getSuffix(this)}`,
            memorySize: 4096,
            timeout: Duration.seconds(90),
            runtime: Runtime.NODEJS_18_X,
            handler: 'handler',
            logRetention: RetentionDays.THREE_DAYS,
            logRetentionRetryOptions: {
                base: Duration.seconds(10),
                maxRetries: 100
            },
			bundling: {
			  externalModules: [
			  ],
			  nodeModules: ["@sparticuz/chromium"],
			},
            entry: path.join(__dirname, '/../src/onboard/onboard_web_scraper.ts'),
            environment: {
                QUEUE_URL: this.onboardQueue.queueUrl,
                OUTPUT_BUCKET: this.screenshotBucket.bucketName,
            }
        });
        
        this.screenshotBucket.grantReadWrite(this.webScraperLambda);
    }

	createBrandingPostProcessorLambda() {
		const unsplashApiSecretArn = this.node.tryGetContext('unsplash-api-secret-arn');
		const unsplashApiSecret = Secret.fromSecretCompleteArn(
			this,
			`UnsplashApiSecret-${Utils.getSuffix(this)}`,
			unsplashApiSecretArn,
		);
		const brandingPostProcessorLambda = new NodejsFunction(this, `onboard-branding-post-processor-${Utils.getSuffix(this)}`, {
			functionName: `onboard-branding-post-processor-${Utils.getSuffix(this)}`,
			memorySize: 512,
			runtime: Runtime.NODEJS_18_X,
			handler: 'handler',
			logRetention: RetentionDays.THREE_DAYS,
			logRetentionRetryOptions: {
				base: Duration.seconds(10),
				maxRetries: 100
			},
			entry: path.join(__dirname, '/../src/onboard/onboard_branding_post_processor.ts'),
			environment: {
				UNSPLASH_SECRET_ARN: unsplashApiSecret.secretArn,
			},
		});
		unsplashApiSecret.grantRead(brandingPostProcessorLambda);
	}

    createGptLambda(vpc: IVpc) {
      
        this.onboardLambda = new NodejsFunction(this, `onboard-gpt-${Utils.getSuffix(this)}`, {
            functionName: `onboard-gpt-${Utils.getSuffix(this)}`,
            memorySize: 256,
            timeout: Duration.minutes(5),
            runtime: Runtime.NODEJS_18_X,
            handler: 'handler',
            logRetention: RetentionDays.THREE_DAYS,
            logRetentionRetryOptions: {
                base: Duration.seconds(10),
                maxRetries: 100
            },
			bundling: {
			  externalModules: [
			  ],
			  nodeModules: ["openai"],
			},
            entry: path.join(__dirname, '/../src/onboard/onboard_gpt.ts'),
            environment: {
                QUEUE_URL: this.onboardQueue.queueUrl,
            }
        });
    }

    createPostgresLambda(vpc: IVpc) {
      
        this.postgresLambda = new NodejsFunction(this, `onboard-postgres-${Utils.getSuffix(this)}`, {
            functionName: `onboard-postgres-${Utils.getSuffix(this)}`,
            memorySize: 256,
            timeout: Duration.seconds(30),
            runtime: Runtime.NODEJS_18_X,
            handler: 'handler',
            logRetention: RetentionDays.THREE_DAYS,
            logRetentionRetryOptions: {
                base: Duration.seconds(10),
                maxRetries: 100
            },
			bundling: {
			  externalModules: [
			  ],
			  nodeModules: ["pg"],
			},
            entry: path.join(__dirname, '/../src/onboard/onboard_postgres.ts'),
            environment: {
                PGHOST: 'raleon-webapp-development.ctskkm7eoqqi.us-east-1.rds.amazonaws.com',
                PGUSER: 'postgres',
                PGPASSWORD: '%9w)6pKbjTfZA4u&',
                PGDATABASE: Utils.getSuffix(this) === 'prod'
                    ? 'raleon'
                    : 'raleon-test',
                PGPORT: '5432'
            },
            vpc,
            vpcSubnets: {
                subnetType: SubnetType.PRIVATE_WITH_EGRESS ,
            },
            securityGroups: [
                this.sg
            ],
        });
    }
    

    createJsonToPostgresLambda(vpc: IVpc) {
      
        this.jsonToPostgresLambda = new NodejsFunction(this, `onboard-json-to-postgres-${Utils.getSuffix(this)}`, {
            functionName: `onboard-json-to-postgres-${Utils.getSuffix(this)}`,
            memorySize: 256,
            timeout: Duration.seconds(30),
            runtime: Runtime.NODEJS_18_X,
            handler: 'handler',
            logRetention: RetentionDays.THREE_DAYS,
            logRetentionRetryOptions: {
                base: Duration.seconds(10),
                maxRetries: 100
            },
			bundling: {
			  externalModules: [
			  ],
			  nodeModules: ["pg"],
			},
            entry: path.join(__dirname, '/../src/onboard/onboard_json_to_postgres.ts'),
            environment: {},
            vpc,
            vpcSubnets: {
                subnetType: SubnetType.PRIVATE_WITH_EGRESS ,
            },
            securityGroups: [
                this.sg
            ],
        });
    }
    
    
    

    createGptToFullJsonLambda(vpc: IVpc) {
      
        this.gptToFullJsonFunction = new NodejsFunction(this, `onboard-gpt-to-json-${Utils.getSuffix(this)}`, {
            functionName: `onboard-gpt-to-json-${Utils.getSuffix(this)}`,
            memorySize: 256,
            timeout: Duration.seconds(30),
            runtime: Runtime.NODEJS_18_X,
            handler: 'handler',
            logRetention: RetentionDays.THREE_DAYS,
            logRetentionRetryOptions: {
                base: Duration.seconds(10),
                maxRetries: 100
            },
			bundling: {
			  externalModules: [
			  ],
			  nodeModules: ["pg"],
			},
            entry: path.join(__dirname, '/../src/onboard/onboard_gpt_to_json.ts'),
            environment: {},
            vpc,
            vpcSubnets: {
                subnetType: SubnetType.PRIVATE_WITH_EGRESS ,
            },
            securityGroups: [
                this.sg
            ],
        });
    }

    createResultBuckets(vpc: IVpc) {
        this.aiCampaignBucket = new Bucket(this, `onboard-ai-campaigns-${Utils.getSuffix(this)}`, {
			publicReadAccess: true,
			blockPublicAccess: {
				blockPublicAcls: false,
				blockPublicPolicy: false,
				ignorePublicAcls: false,
				restrictPublicBuckets: false,
			},
			lifecycleRules: [{
				id: `onboard-ai-campaigns-${Utils.getSuffix(this)}-ExpirationRule`,
				enabled: true,
				expiration: Duration.days(1)
			}],
			removalPolicy: RemovalPolicy.DESTROY,
			autoDeleteObjects: true,
		});

        const bucketPolicy = new BucketPolicy(this, `BucketPolicy-${Utils.getSuffix(this)}-ai-campaigns`, {
            bucket: this.aiCampaignBucket,
        });
    
        bucketPolicy.document.addStatements(new iam.PolicyStatement({
            actions: ['s3:GetObject'],
            resources: [this.aiCampaignBucket.arnForObjects('*')],
            principals: [new iam.AnyPrincipal()],
        }));
      
        
        this.scrapeBucket = new Bucket(this, `onboard-scrape-result-${Utils.getSuffix(this)}`, {
			publicReadAccess: true,
			blockPublicAccess: {
				blockPublicAcls: false,
				blockPublicPolicy: false,
				ignorePublicAcls: false,
				restrictPublicBuckets: false,
			},
			lifecycleRules: [{
				id: `onboard-scrape-${Utils.getSuffix(this)}-ExpirationRule`,
				enabled: true,
				expiration: Duration.days(30)
			}],
			removalPolicy: RemovalPolicy.DESTROY,
			autoDeleteObjects: true,
		});

        const bucketPolicy2 = new BucketPolicy(this, `BucketPolicy-${Utils.getSuffix(this)}-ai-scrape-result`, {
            bucket: this.scrapeBucket,
        });

        bucketPolicy2.document.addStatements(new iam.PolicyStatement({
            actions: ['s3:GetObject'],
            resources: [this.scrapeBucket.arnForObjects('*')],
            principals: [new iam.AnyPrincipal()],
        }));

        
        this.screenshotBucket = new Bucket(this, `onboard-screenshot-${Utils.getSuffix(this)}`, {
			publicReadAccess: true,
			blockPublicAccess: {
				blockPublicAcls: false,
				blockPublicPolicy: false,
				ignorePublicAcls: false,
				restrictPublicBuckets: false,
			},
		});

        const bucketPolicy3 = new BucketPolicy(this, `BucketPolicy-${Utils.getSuffix(this)}-screenshot`, {
            bucket: this.screenshotBucket,
        });
    
        bucketPolicy3.document.addStatements(new iam.PolicyStatement({
            actions: ['s3:GetObject'],
            resources: [this.screenshotBucket.arnForObjects('*')],
            principals: [new iam.AnyPrincipal()],
        }));
        
        
        
        this.catalogBucket = new Bucket(this, `onboard-catalog-${Utils.getSuffix(this)}`, {
			publicReadAccess: true,
			blockPublicAccess: {
				blockPublicAcls: false,
				blockPublicPolicy: false,
				ignorePublicAcls: false,
				restrictPublicBuckets: false,
			},
			lifecycleRules: [{
				id: `onboard-catalog-${Utils.getSuffix(this)}-ExpirationRule`,
				enabled: true,
				expiration: Duration.days(30)
			}],
			removalPolicy: RemovalPolicy.DESTROY,
			autoDeleteObjects: true,
		});

        const bucketPolicy4 = new BucketPolicy(this, `BucketPolicy-${Utils.getSuffix(this)}-catalog`, {
            bucket: this.catalogBucket,
        });
    
        bucketPolicy4.document.addStatements(new iam.PolicyStatement({
            actions: ['s3:GetObject'],
            resources: [this.catalogBucket.arnForObjects('*')],
            principals: [new iam.AnyPrincipal()],
        }));
    }

    
    createCampaignPersistLambda(vpc: IVpc) {
      
        this.campaignPersistLambda = new NodejsFunction(this, `onboard-campaign-persist-${Utils.getSuffix(this)}`, {
            functionName: `onboard-campaign-persist-${Utils.getSuffix(this)}`,
            memorySize: 256,
            timeout: Duration.minutes(5),
            runtime: Runtime.NODEJS_18_X,
            handler: 'handler',
            logRetention: RetentionDays.THREE_DAYS,
            logRetentionRetryOptions: {
                base: Duration.seconds(10),
                maxRetries: 100
            },
			bundling: {
			  externalModules: [
			  ],
			  nodeModules: ["pg"],
			},
            entry: path.join(__dirname, '/../src/onboard/gpt_campaign_persist.ts'),
            environment: {
                PGHOST: 'raleon-webapp-development.ctskkm7eoqqi.us-east-1.rds.amazonaws.com',
                PGUSER: 'postgres',
                PGPASSWORD: '%9w)6pKbjTfZA4u&',
                PGDATABASE: Utils.getSuffix(this) === 'prod'
                    ? 'raleon'
                    : 'raleon-test',
                PGPORT: '5432'
            },
            vpc,
            vpcSubnets: {
                subnetType: SubnetType.PRIVATE_WITH_EGRESS ,
            },
            securityGroups: [
                this.sg
            ],
        });
    }
    

    campaignPersistPost(vpc: IVpc) {
        const campaignPersistResource = this.apiGateway.root.addResource('campaign-persist');

		campaignPersistResource.addMethod('POST', new apigateway.LambdaIntegration(this.campaignPersistLambda), {
			authorizationType: AuthorizationType.IAM,
		  });
    }

    createJsonExtractorLambda(vpc: IVpc) {
      
        this.jsonExtractorLambda = new NodejsFunction(this, `onboard-json-extractor-${Utils.getSuffix(this)}`, {
            functionName: `onboard-json-extractor-${Utils.getSuffix(this)}`,
            memorySize: 256,
            timeout: Duration.seconds(30),
            runtime: Runtime.NODEJS_18_X,
            handler: 'handler',
            logRetention: RetentionDays.THREE_DAYS,
            logRetentionRetryOptions: {
                base: Duration.seconds(10),
                maxRetries: 100
            },
			bundling: {
			  externalModules: [
			  ],
			  nodeModules: ["pg"],
			},
            entry: path.join(__dirname, '/../src/onboard/onboard_json_extractor.ts'),
            environment: {},
            vpc,
            vpcSubnets: {
                subnetType: SubnetType.PRIVATE_WITH_EGRESS ,
            },
            securityGroups: [
                this.sg
            ],
        });
    }
    

    createJsonParserLambda(vpc: IVpc) {
      
        this.jsonParserLambda = new NodejsFunction(this, `onboard-json-parser-${Utils.getSuffix(this)}`, {
            functionName: `onboard-json-parser-${Utils.getSuffix(this)}`,
            memorySize: 256,
            timeout: Duration.seconds(30),
            runtime: Runtime.NODEJS_18_X,
            handler: 'handler',
            logRetention: RetentionDays.THREE_DAYS,
            logRetentionRetryOptions: {
                base: Duration.seconds(10),
                maxRetries: 100
            },
			bundling: {
			  externalModules: [
			  ],
			  nodeModules: ["pg"],
			},
            entry: path.join(__dirname, '/../src/onboard/onboard_json_parser.ts'),
            environment: {},
            vpc,
            vpcSubnets: {
                subnetType: SubnetType.PRIVATE_WITH_EGRESS ,
            },
            securityGroups: [
                this.sg
            ],
        });
    }
    

    createGptPromptGeneratorLambda(vpc: IVpc) {
      
        this.gptPromptLambda = new NodejsFunction(this, `onboard-gpt-prompt-generator-${Utils.getSuffix(this)}`, {
            functionName: `onboard-gpt-prompt-generator-${Utils.getSuffix(this)}`,
            memorySize: 256,
            timeout: Duration.seconds(30),
            runtime: Runtime.NODEJS_18_X,
            handler: 'handler',
            logRetention: RetentionDays.THREE_DAYS,
            logRetentionRetryOptions: {
                base: Duration.seconds(10),
                maxRetries: 100
            },
			bundling: {
			  externalModules: [
			  ],
			  nodeModules: ["pg"],
			},
            entry: path.join(__dirname, '/../src/onboard/onboard_gpt_prompt.ts'),
            environment: {
            },
            vpc,
            vpcSubnets: {
                subnetType: SubnetType.PRIVATE_WITH_EGRESS ,
            },
            securityGroups: [
                this.sg
            ],
        });
    }
    

    createGptVisionPromptGeneratorLambda(vpc: IVpc) {
      
        this.gptVisionPromptLambda = new NodejsFunction(this, `onboard-gpt-vision-prompt-generator-${Utils.getSuffix(this)}`, {
            functionName: `onboard-gpt-vision-prompt-generator-${Utils.getSuffix(this)}`,
            memorySize: 256,
            timeout: Duration.seconds(30),
            runtime: Runtime.NODEJS_18_X,
            handler: 'handler',
            logRetention: RetentionDays.THREE_DAYS,
            logRetentionRetryOptions: {
                base: Duration.seconds(10),
                maxRetries: 100
            },
			bundling: {
			  externalModules: [
			  ],
			  nodeModules: ["pg"],
			},
            entry: path.join(__dirname, '/../src/onboard/onboard_gpt_vision_prompt.ts'),
            environment: {
            },
            vpc,
            vpcSubnets: {
                subnetType: SubnetType.PRIVATE_WITH_EGRESS ,
            },
            securityGroups: [
                this.sg
            ],
        });
    }
    createJsonStringifierLambda(vpc: IVpc) {
      
        this.jsonStringifierLambda = new NodejsFunction(this, `onboard-json-stringifier-${Utils.getSuffix(this)}`, {
            functionName: `onboard-json-stringifier-${Utils.getSuffix(this)}`,
            memorySize: 256,
            timeout: Duration.seconds(30),
            runtime: Runtime.NODEJS_18_X,
            handler: 'handler',
            logRetention: RetentionDays.THREE_DAYS,
            logRetentionRetryOptions: {
                base: Duration.seconds(10),
                maxRetries: 100
            },
			bundling: {
			  externalModules: [
			  ],
			  nodeModules: ["pg"],
			},
            entry: path.join(__dirname, '/../src/onboard/onboard_json_stringifier.ts'),
            environment: {
            },
            vpc,
            vpcSubnets: {
                subnetType: SubnetType.PRIVATE_WITH_EGRESS ,
            },
            securityGroups: [
                this.sg
            ],
        });
    }
    
    createShopifyTokenLambda(vpc: IVpc) {
        const dbSecretArn = this.node.tryGetContext(
            Utils.getSuffix(this) == 'prod' 
            ? 'shopify-db-secret-arn' 
            : 'shopify-db-dev-secret-arn');
        const dbSecret = Secret.fromSecretCompleteArn(
            this,
            `ShopifyDBSecret-${Utils.getSuffix(this)}`,
            dbSecretArn,
        );
      
        this.shopifyTokenLambda = new NodejsFunction(this, `onboard-shopify-token-${Utils.getSuffix(this)}`, {
            functionName: `onboard-shopify-token-${Utils.getSuffix(this)}`,
            memorySize: 256,
            timeout: Duration.seconds(30),
            runtime: Runtime.NODEJS_18_X,
            handler: 'handler',
            logRetention: RetentionDays.THREE_DAYS,
            logRetentionRetryOptions: {
                base: Duration.seconds(10),
                maxRetries: 100
            },
			// bundling: {
			//   externalModules: [
			//   ],
			//   nodeModules: ["pg"],
			// },
            entry: path.join(__dirname, '/../src/onboard/onboard_shopify_token.ts'),
            environment: {
                SECRET_ARN: dbSecret.secretArn,
                // PGHOST: 'raleon-webapp-development.ctskkm7eoqqi.us-east-1.rds.amazonaws.com',
                // PGUSER: 'postgres',
                // PGPASSWORD: '%9w)6pKbjTfZA4u&',
                // PGDATABASE: 'raleon-test',
                // PGPORT: '5432'
            },
            vpc,
            vpcSubnets: {
                subnetType: SubnetType.PRIVATE_WITH_EGRESS ,
            },
            securityGroups: [
               this.sg
            ],
        });
		dbSecret.grantRead(this.shopifyTokenLambda);
    }

    createShopifyApiInvokerLambda(vpc: IVpc) {
      
        this.shopifyApiInvokerLambda = new NodejsFunction(this, `onboard-shopify-api-invoker-${Utils.getSuffix(this)}`, {
            functionName: `onboard-shopify-api-invoker-${Utils.getSuffix(this)}`,
            memorySize: 256,
            timeout: Duration.seconds(30),
            runtime: Runtime.NODEJS_18_X,
            handler: 'handler',
            logRetention: RetentionDays.THREE_DAYS,
            logRetentionRetryOptions: {
                base: Duration.seconds(10),
                maxRetries: 100
            },
            entry: path.join(__dirname, '/../src/onboard/onboard_shopify_api_invoker.ts'),
            environment: {
                QUEUE_URL: this.onboardQueue.queueUrl,
                // SECRET_ARN: dbSecret.secretArn,
            }
        });

		// dbSecret.grantRead(this.shopifyApiInvokerLambda);
    }
    
    createTrubricsLambda(vpc: IVpc) {
      
        this.trubricsLambda = new Function(this, `onboard-trubrics-${Utils.getSuffix(this)}`, {
            functionName: `onboard-trubrics-${Utils.getSuffix(this)}`,
            memorySize: 256,
            timeout: Duration.seconds(30),
            runtime: Runtime.PYTHON_3_11,
            handler: 'onboard_trubrics.handler',
            logRetention: RetentionDays.THREE_DAYS,
            logRetentionRetryOptions: {
                base: Duration.seconds(10),
                maxRetries: 100
            },
            code: Code.fromAsset(path.join(__dirname, '/../src/onboard/trubrics')),
            environment: {
                QUEUE_URL: this.onboardQueue.queueUrl,
                TRUBRICS_EMAIL: '<EMAIL>',
                TRUBRICS_PASSWORD: 'RW8@f32jpRj!b#g5GA',
                // SECRET_ARN: dbSecret.secretArn,
            }
        });

		// dbSecret.grantRead(this.shopifyApiInvokerLambda);
    }

    

    createShopifyCatalogSummarizerLambda(vpc: IVpc) {
      
        this.shopifyCatalogSummarizerLambda = new NodejsFunction(this, `onboard-shopify-catalog-summarizer-${Utils.getSuffix(this)}`, {
            functionName: `onboard-shopify-catalog-summarizer-${Utils.getSuffix(this)}`,
            memorySize: 256,
            timeout: Duration.seconds(30),
            runtime: Runtime.NODEJS_18_X,
            handler: 'handler',
            logRetention: RetentionDays.THREE_DAYS,
            logRetentionRetryOptions: {
                base: Duration.seconds(10),
                maxRetries: 100
            },
			bundling: {
			  externalModules: [
			  ],
			  nodeModules: ["pg"],
			},
            entry: path.join(__dirname, '/../src/onboard/onboard_shopify_catalog_summarizer.ts'),
            environment: {},
        });
    }
    

    private pushMessageOnQueue() {
        const topic = new sns.Topic(this, `onboard-start-topic`);
        const onboardResource = this.apiGateway.root.addResource('onboard');

        topic.addSubscription(new subs.SqsSubscription(this.onboardQueue));

        this.gatewayExecutionRole = new iam.Role(this, "onboard-gateway-execution-role", {
            assumedBy: new iam.ServicePrincipal("apigateway.amazonaws.com"),
            inlinePolicies: {
                "PublishMessagePolicy": new iam.PolicyDocument({
                    statements: [new iam.PolicyStatement({
                        actions: ["sns:Publish"],
                        resources: [topic.topicArn]
                    })]
                })
            }
        });

        onboardResource.addMethod('POST',
            new apigateway.AwsIntegration({
                service: 'sns',
                integrationHttpMethod: 'POST',
                path: `${Aws.ACCOUNT_ID}/${topic.topicName}`,
                options: {
                    credentialsRole: this.gatewayExecutionRole,
                    passthroughBehavior: apigateway.PassthroughBehavior.NEVER,
                    requestParameters: {
                        "integration.request.header.Content-Type": `'application/x-www-form-urlencoded'`,
                    },
                    requestTemplates: {
                        "application/json": `Action=Publish&TopicArn=$util.urlEncode('${topic.topicArn}')&Message=$util.urlEncode($input.body)`,
                    },
                    integrationResponses: [
                        {
                            statusCode: "200",
                            responseParameters: {
                                'method.response.header.Access-Control-Allow-Origin': "'*'",
                            },
                            responseTemplates: {
                                "application/json": `{"status": "message added to topic"}`,
                            },
                        },
                        {
                            statusCode: "400",
                            selectionPattern: "^\[Error\].*",
                            responseTemplates: {
                                "application/json": `{\"state\":\"error\",\"message\":\"$util.escapeJavaScript($input.path('$.errorMessage'))\"}`,
                            },
                        }
                    ],
                }
            }),
            {
                methodResponses: [{
                    statusCode: "200",
                    responseParameters: {
                        'method.response.header.Content-Type': true,
                        'method.response.header.Access-Control-Allow-Origin': true,
                        'method.response.header.Access-Control-Allow-Credentials': true
                    }
                }, {
                    statusCode: "400",
                    responseParameters: {
                        'method.response.header.Content-Type': true,
                        'method.response.header.Access-Control-Allow-Origin': true,
                        'method.response.header.Access-Control-Allow-Credentials': true
                    }
                }]
        }
        );
    }    private setupStepFunction() {
        // Create a role for step functions that allows them to invoke other step functions and Lambda functions
        const stepFunctionRole = new iam.Role(this, `StepFunctionRole-${Utils.getSuffix(this)}`, {
            assumedBy: new iam.ServicePrincipal('states.amazonaws.com'),
        });

        // Add permissions to invoke Lambda functions and Step Functions
        stepFunctionRole.attachInlinePolicy(new iam.Policy(this, `StepFunctionPolicy-${Utils.getSuffix(this)}`, {
            statements: [
                new iam.PolicyStatement({
                    actions: [
                        'lambda:InvokeFunction',
                        's3:GetObject',
                        's3:PutObject',
                        's3:ListBucket',
                        's3:HeadObject',
                        'states:StartExecution',
                        'glue:StartJobRun',
                        'glue:GetJobRun',
                        'glue:GetJobRuns'
                    ],
                    resources: ['*'],
                }),
            ],
        }));

        // Create all step functions with their definitions
        
        // 1. Knowledge Gathering Step Function
        const knowledgeGatheringStringified = JSON.stringify(knowledgeGatheringJson);
        if (!knowledgeGatheringStringified.includes('{{suffix}}')) {
            throw new Error('Suffix placeholders not found in Knowledge Gathering step function');
        }
        const knowledgeGatheringSuffixed = knowledgeGatheringStringified.replace(/{{suffix}}/g, Utils.getSuffix(this));
        
        const knowledgeGatheringStepFunction = new sfn.CfnStateMachine(this, `KnowledgeGatheringStepFunction-${Utils.getSuffix(this)}`, {
            definitionString: knowledgeGatheringSuffixed,
            roleArn: stepFunctionRole.roleArn,
            stateMachineName: `KnowledgeGatheringStepFunction-${Utils.getSuffix(this)}`,
        });
        
        // 2. Member Insights Step Function
        const memberInsightsStringified = JSON.stringify(memberInsightsJson);
        if (!memberInsightsStringified.includes('{{suffix}}')) {
            throw new Error('Suffix placeholders not found in Member Insights step function');
        }
        const memberInsightsSuffixed = memberInsightsStringified.replace(/{{suffix}}/g, Utils.getSuffix(this));
        
        const memberInsightsStepFunction = new sfn.CfnStateMachine(this, `MemberInsightsStepFunction-${Utils.getSuffix(this)}`, {
            definitionString: memberInsightsSuffixed,
            roleArn: stepFunctionRole.roleArn,
            stateMachineName: `MemberInsightsStepFunction-${Utils.getSuffix(this)}`,
        });
        
        // 3. Program Step Function
        const programStringified = JSON.stringify(programJson);
        if (!programStringified.includes('{{suffix}}')) {
            throw new Error('Suffix placeholders not found in Program step function');
        }
        const programSuffixed = programStringified.replace(/{{suffix}}/g, Utils.getSuffix(this));
        
        const programStepFunction = new sfn.CfnStateMachine(this, `ProgramStepFunction-${Utils.getSuffix(this)}`, {
            definitionString: programSuffixed,
            roleArn: stepFunctionRole.roleArn,
            stateMachineName: `ProgramStepFunction-${Utils.getSuffix(this)}`,
        });
        
        // 4. Branding Step Function
        const brandingStringified = JSON.stringify(brandingJson);
        if (!brandingStringified.includes('{{suffix}}')) {
            throw new Error('Suffix placeholders not found in Branding step function');
        }
        const brandingSuffixed = brandingStringified.replace(/{{suffix}}/g, Utils.getSuffix(this));
        
        const brandingStepFunction = new sfn.CfnStateMachine(this, `BrandingStepFunction-${Utils.getSuffix(this)}`, {
            definitionString: brandingSuffixed,
            roleArn: stepFunctionRole.roleArn,
            stateMachineName: `BrandingStepFunction-${Utils.getSuffix(this)}`,
        });
        
        // 5. Dependency Step Function
        const dependencyStringified = JSON.stringify(dependencyJson);
        if (!dependencyStringified.includes('{{suffix}}')) {
            throw new Error('Suffix placeholders not found in Dependency step function');
        }
        const dependencySuffixed = dependencyStringified.replace(/{{suffix}}/g, Utils.getSuffix(this));
        
        const dependencyStepFunction = new sfn.CfnStateMachine(this, `DependencyStepFunction-${Utils.getSuffix(this)}`, {
            definitionString: dependencySuffixed,
            roleArn: stepFunctionRole.roleArn,
            stateMachineName: `DependencyStepFunction-${Utils.getSuffix(this)}`,
        });
          // 6. Main Orchestrator Step Function (must be created last to reference others)
        const stringifiedJson = JSON.stringify(json);
        if (!stringifiedJson.includes('{{suffix}}')) {
            throw new Error('Suffix placeholders not found in Main Orchestrator step function');
        }
        
        // First, replace the storePasswordValue placeholder based on environment
        const storePasswordValue = Utils.getSuffix(this) === 'prod' ? '' : 'raychu';
        let processedJson = stringifiedJson.replace(/{{storePasswordValue}}/g, storePasswordValue);
        
        // Then replace the suffix placeholders
        processedJson = processedJson.replace(/{{suffix}}/g, Utils.getSuffix(this));
        
        this.onboardStepFunction = new sfn.CfnStateMachine(this, `LoyaltyOnboarding-${Utils.getSuffix(this)}`, {
            definitionString: processedJson,
            roleArn: stepFunctionRole.roleArn,
            stateMachineName: `LoyaltyOnboarding-${Utils.getSuffix(this)}`,
        });
        
        // Establish dependencies to ensure the referenced step functions are created before being referenced
        this.onboardStepFunction.addDependsOn(knowledgeGatheringStepFunction);
        this.onboardStepFunction.addDependsOn(memberInsightsStepFunction);
        this.onboardStepFunction.addDependsOn(dependencyStepFunction);
        dependencyStepFunction.addDependsOn(brandingStepFunction);
        dependencyStepFunction.addDependsOn(programStepFunction);
    }

    createWebsiteQaLambda(vpc: IVpc) {
        this.websiteQaLambda = new NodejsFunction(this, `website-qa-lambda-${Utils.getSuffix(this)}`, {
            functionName: `website-qa-lambda-${Utils.getSuffix(this)}`,
            memorySize: 10240,
            timeout: Duration.minutes(15),
            runtime: Runtime.NODEJS_18_X,
            handler: 'handler',
            logRetention: RetentionDays.THREE_DAYS,
            logRetentionRetryOptions: {
                base: Duration.seconds(10),
                maxRetries: 100
            },
            bundling: {
                externalModules: [],
                nodeModules: ["puppeteer-core", "@sparticuz/chromium"],
            },
            entry: path.join(__dirname, '/../src/onboard/website_qa_handler.ts'),
            environment: {},
        });
    }

    createLLMRouterLambda() {
        this.llmRouterLambda = new NodejsFunction(this, `llm-router-lambda-${Utils.getSuffix(this)}`, {
            functionName: `llm-router-lambda-${Utils.getSuffix(this)}`,
            memorySize: 512,
            timeout: Duration.minutes(5),
            runtime: Runtime.NODEJS_18_X,
            handler: 'handler',
            logRetention: RetentionDays.THREE_DAYS,
            logRetentionRetryOptions: {
                base: Duration.seconds(10),
                maxRetries: 100
            },
            entry: path.join(__dirname, '/../src/utils/llm-router/llm-router-lambda.ts'),
            environment: {},
        });
        
        // // Add API Gateway endpoint for the LLM Router
        // const llmRouterResource = this.apiGateway.root.addResource('llm-router');
        
        // // Add POST method to handle LLM Router requests
        // llmRouterResource.addMethod('POST', new apigateway.LambdaIntegration(this.llmRouterLambda), {
        //     authorizationType: AuthorizationType.NONE, // You might want to change this based on your security requirements
        //     apiKeyRequired: false,
        // });

        // // Add OPTIONS method for CORS support
        // llmRouterResource.addMethod('OPTIONS', new apigateway.MockIntegration({
        //     integrationResponses: [{
        //         statusCode: '200',
        //         responseParameters: {
        //             'method.response.header.Access-Control-Allow-Headers': "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
        //             'method.response.header.Access-Control-Allow-Origin': "'*'",
        //             'method.response.header.Access-Control-Allow-Methods': "'GET,POST,OPTIONS'"
        //         }
        //     }],
        //     passthroughBehavior: apigateway.PassthroughBehavior.NEVER,
        //     requestTemplates: {
        //         "application/json": "{\"statusCode\": 200}"
        //     }
        // }), {
        //     methodResponses: [{
        //         statusCode: '200',
        //         responseParameters: {
        //             'method.response.header.Access-Control-Allow-Headers': true,
        //             'method.response.header.Access-Control-Allow-Methods': true,
        //             'method.response.header.Access-Control-Allow-Origin': true
        //         }
        //     }]
        // });
    }
}

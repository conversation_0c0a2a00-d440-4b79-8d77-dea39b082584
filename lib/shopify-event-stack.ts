import * as cdk from "aws-cdk-lib";
import { Construct } from "constructs";
import * as sqs from "aws-cdk-lib/aws-sqs";
import {
	CfnRule,
	Duration,
	RemovalPolicy,
	Stack,
	StackProps,
} from "aws-cdk-lib";
import {
	getSuffix,
	isDev,
	MAX_CONCURRENCY_LAMBDAS,
	MAX_CONCURRENCY_LAMBDAS_DEV,
} from "./utils";
import { NodejsFunction } from "aws-cdk-lib/aws-lambda-nodejs";
import * as lambda from "aws-cdk-lib/aws-lambda";
import * as path from "path";
import * as logs from "aws-cdk-lib/aws-logs";
import { SqsEventSource } from "aws-cdk-lib/aws-lambda-event-sources";
import { Secret } from "aws-cdk-lib/aws-secretsmanager";
import { EventBus, Rule, Schedule } from "aws-cdk-lib/aws-events";
import { LambdaFunction, SqsQueue } from "aws-cdk-lib/aws-events-targets";
import { SecurityGroup, Subnet, Vpc, IVpc } from "aws-cdk-lib/aws-ec2";
import { Table, AttributeType } from "aws-cdk-lib/aws-dynamodb";
import { Policy, PolicyDocument, PolicyStatement, Role, ServicePrincipal } from "aws-cdk-lib/aws-iam";
import { Bucket } from "aws-cdk-lib/aws-s3";
import { WebSocketApi } from "@aws-cdk/aws-apigatewayv2-alpha";
import { Queue } from "aws-cdk-lib/aws-sqs";
import { LambdaIntegration, RestApi } from "aws-cdk-lib/aws-apigateway";
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import { CfnCacheCluster } from "aws-cdk-lib/aws-elasticache";
import { CfnDeliveryStream } from "aws-cdk-lib/aws-kinesisfirehose";
import { Function, Runtime, Code } from 'aws-cdk-lib/aws-lambda';
import * as sfn from 'aws-cdk-lib/aws-stepfunctions';
import * as tasks from 'aws-cdk-lib/aws-stepfunctions-tasks';

interface ShopifyEventStackProps extends StackProps {
	deploy_environment_name: string;
	webSocketApi: WebSocketApi;
	chatConnectionsTable: Table;
	metricQueue: sqs.Queue;
}

export class ShopifyEventStack extends cdk.Stack {
	isDev: boolean;
	athenaOutput: Bucket;
	athenaPolicy: Policy;
	curatedBucket: Bucket;
	errorBucket: Bucket;
	rawBucket: Bucket;
	integrationApi: RestApi;
	vpc: IVpc;
	orderDataFirehose: CfnDeliveryStream;
	memcachedCluster: CfnCacheCluster;

	constructor(
		scope: Construct,
		id: string,
		private props?: ShopifyEventStackProps
	) {
		super(scope, id, props);
		this.isDev = isDev(props!.deploy_environment_name);

		this.vpc = Vpc.fromLookup(
			this,
			`raleon-vpc-shopify-${getSuffix(this)}`,
			{ vpcName: "raleon-vpc" }
		);

		this.memcachedCluster = new CfnCacheCluster(this, 'MemcachedCluster', {
			clusterName: `memcached-cluster-${getSuffix(this)}`,
			cacheNodeType: 'cache.t3.micro',
			engine: 'memcached',
			numCacheNodes: 1,
			cacheSubnetGroupName: 'redis-cluster-subnet-group',
			vpcSecurityGroupIds: [
				'sg-02b66cd9f129e31fd'
			]
		});

		this.setupIntegrations();

		const raleonAppDbSecret = Secret.fromSecretNameV2(
			this, 
			`raleonwebapp-id-${getSuffix(this)}`, 
			this.isDev ? 'raleonwebapp': 'raleonwebapp-prod'
		);
		let athenaPermission = new PolicyStatement({
			actions: [
				"athena:*",
				"glue:*",
				"dynamodb:DescribeTable",
				"dynamodb:ListSchemas",
				"dynamodb:ListStreams",
				"dynamodb:ListTables",
				"dynamodb:Query",
				"dynamodb:Scan",
				"lambda:*",
				"s3:*",
			],
			resources: ["*"],
		});

		this.athenaPolicy = new Policy(
			this,
			`athena-access-policy-${getSuffix(this)}`,
			{
				statements: [athenaPermission],
			}
		);

		this.athenaOutput = new Bucket(this, `AthenaOutput-${getSuffix(this)}`, {
			removalPolicy: RemovalPolicy.DESTROY,
			autoDeleteObjects: true,
		});

		const webhookDeadletter = new sqs.Queue(
			this,
			`webhook-deadletter-${getSuffix(this)}`,
			{
				queueName: `webhook-deadletter-queue-${getSuffix(this)}`,
				retentionPeriod: Duration.days(4),
			}
		);
		const webhookQueue = new sqs.Queue(this, `webhook-${getSuffix(this)}`, {
			queueName: `webhook-queue-${getSuffix(this)}`,
			deadLetterQueue: {
				queue: webhookDeadletter,
				maxReceiveCount: 3,
			},
			visibilityTimeout: Duration.seconds(45),
			//fifo: true,
		});
		const bulkWebhookDeadletter = new sqs.Queue(
			this,
			`bulk-webhook-deadletter-${getSuffix(this)}`,
			{
				queueName: `bulk-webhook-deadletter-queue-${getSuffix(this)}`,
				retentionPeriod: Duration.days(4),
			}
		);
		const bulkWebhookQueue = new sqs.Queue(this, `bulk-webhook-queue-${getSuffix(this)}`, {
			queueName: `bulk-webhook-queue-${getSuffix(this)}`,
			deadLetterQueue: {
				queue: bulkWebhookDeadletter,
				maxReceiveCount: 3,
			},
			visibilityTimeout: Duration.seconds(120),
		});
		const subscriptionDeadletter = new sqs.Queue(
			this,
			`subscription-webhook-deadletter-${getSuffix(this)}`,
			{
				queueName: `subscription-webhook-deadletter-queue-${getSuffix(this)}`,
				retentionPeriod: Duration.days(4),
			}
		);
		const subscriptionQueue = new sqs.Queue(this, `subscription-webhook-queue-${getSuffix(this)}`, {
			queueName: `subscription-webhook-queue-${getSuffix(this)}`,
			deadLetterQueue: {
				queue: subscriptionDeadletter,
				maxReceiveCount: 3,
			},
			visibilityTimeout: Duration.seconds(120),
		});
		const uninstallWebhookDeadletter = new sqs.Queue(
			this,
			`uninstall-webhook-deadletter-${getSuffix(this)}`,
			{
				queueName: `uninstall-webhook-deadletter-queue-${getSuffix(this)}`,
				retentionPeriod: Duration.days(4),
			}
		);
		const uninstallWebhookQueue = new sqs.Queue(this, `uninstall-webhook-queue-${getSuffix(this)}`, {
			queueName: `uninstall-webhook-queue-${getSuffix(this)}`,
			deadLetterQueue: {
				queue: uninstallWebhookDeadletter,
				maxReceiveCount: 3,
			},
			visibilityTimeout: Duration.seconds(120),
		});
		const dataDeadletter = new sqs.Queue(
			this,
			`data-deadletter-${getSuffix(this)}`,
			{
				queueName: `data-deadletter-queue-${getSuffix(this)}`,
				retentionPeriod: Duration.days(4),
			}
		);
		const dataQueue = new sqs.Queue(this, `data-${getSuffix(this)}`, {
			queueName: `data-queue-${getSuffix(this)}`,
			deadLetterQueue: {
				queue: dataDeadletter,
				maxReceiveCount: 3,
			},
			visibilityTimeout: Duration.seconds(120),
		});
		const dbSecretArn = this.node.tryGetContext(
			getSuffix(this) == 'prod' 
			? 'shopify-db-secret-arn' 
			: 'shopify-db-dev-secret-arn');
		const dbSecret = Secret.fromSecretCompleteArn(
			this,
			`ShopifyDBSecret-integration-${getSuffix(this)}`,
			dbSecretArn
		);

		const appSecretArn = this.node.tryGetContext(
			getSuffix(this) == 'prod' 
			? 'shopify-app-secret-arn'
			: 'shopify-app-dev-secret-arn'
		);
		const appSecret = Secret.fromSecretCompleteArn(
			this,
			`ShopifyAppSecret-${getSuffix(this)}`,
			appSecretArn,
		);


		this.curatedBucket = new Bucket(this, `Curated-${getSuffix(this)}`, {
			bucketName: `shopify-curated-${getSuffix(this)}`,
			removalPolicy: cdk.RemovalPolicy.RETAIN,
			autoDeleteObjects: false,
		});

		let webhookProcessor = new NodejsFunction(
			this,
			`webhook-worker-${getSuffix(this)}`,
			{
				functionName: `webhook-worker-${getSuffix(this)}`,
				memorySize: 1024,
				timeout: Duration.seconds(40),
				runtime: lambda.Runtime.NODEJS_18_X,
				handler: "main",
				logRetention: logs.RetentionDays.TWO_MONTHS,
				entry: path.join(__dirname, "/../src/events/webhook-worker.ts"),
				environment: {
					SECRET_ARN: dbSecret.secretArn,
					WEBSOCKET_ENDPOINT_URL: `https://${this.props?.webSocketApi.apiId}.execute-api.${this.region}.amazonaws.com/prod/`,
					WEBAPP_API_URL: `http://${this.isDev ? this.node.tryGetContext('internal-dev-webapp-domain') : this.node.tryGetContext('internal-webapp-domain')}/api/v1`,
					STREAMING_API_URL: `https://${this.isDev ? 'c7z7eka815' : 's393yf9cql'}.execute-api.us-east-1.amazonaws.com/prod/event`,
					SHOPIFY_API_URL: `https://loyalty${this.isDev ? '-dev' : ''}.raleon.io/`,
					SHOPIFY_SECRET_KEY_ARN: appSecret.secretArn,
					QUEUE_URL: dataQueue.queueUrl,
					MEMCACHED_ENDPOINT: `${this.memcachedCluster.attrConfigurationEndpointAddress}:11211`,
					OUTPUT_BUCKET: this.curatedBucket.bucketName,
					ATHENA_DB: this.isDev ? 'ecommerce_dev' : 'ecommerce',
					RALEON_DB: raleonAppDbSecret.secretArn,
					ATHENA_OUTPUT_BUCKET: `s3://${this.athenaOutput.bucketName}`,
				},
				bundling: {
				  externalModules: [
				  ],
				  nodeModules: ["memcached"],
				},
				vpc: this.vpc,
				vpcSubnets: {
					subnets: [
						Subnet.fromSubnetId(
							this,
							"raleon-subnet-shopify",
							"subnet-04ef7beb8a56268d1"
						),
					],
				},
				securityGroups: [
					SecurityGroup.fromLookupByName(
						this,
						"raleon-sg-shopify",
						"default",
						this.vpc
					),
				],
			}
		);
		this.props?.chatConnectionsTable.grantReadWriteData(webhookProcessor);
		webhookProcessor.addToRolePolicy(
			new PolicyStatement({
				actions: ["execute-api:ManageConnections"],
				resources: [
					`arn:aws:execute-api:${this.region}:${this.account}:${this.props?.webSocketApi.apiId}/*/*/*`,
				],
			})
		);
		webhookProcessor.addToRolePolicy(new PolicyStatement({
			actions: ['elasticache:*'],
			resources: ['*'],
		}));
		dbSecret.grantRead(webhookProcessor);
		appSecret.grantRead(webhookProcessor);
		webhookQueue.grantSendMessages(webhookProcessor);
		dataQueue.grantSendMessages(webhookProcessor);
		raleonAppDbSecret.grantRead(webhookProcessor);
		webhookProcessor.addEventSource(
			new SqsEventSource(webhookQueue, {
				batchSize: 3,
				maxBatchingWindow: Duration.seconds(5),
			})
		);

		
		let eventBus: any;
		
		const eventBusArn = this.node.tryGetContext('shopify-event-bus-arns');
		const suffix = getSuffix(this);
		const arn = eventBusArn[suffix];

		if (arn) {
			eventBus = EventBus.fromEventBusArn(
				this, 
				`eventbus-${getSuffix(this)}`, 
				arn,
			);
		} else {
			eventBus = new EventBus(this, `Raleon-Shopify-${getSuffix(this)}`, {
				eventSourceName: this.getEventBusSourceName(),
			});
		}		

		if (!this.isDev) {
			eventBus.archive(`RaleonShopifyProd-${getSuffix(this)}`, {
				archiveName: `RaleonShopifyProdArchival-${getSuffix(this)}`,
				description: "Shopify App Archive",
				eventPattern: {
					account: [Stack.of(this).account],
				},
				retention: this.isDev ? Duration.days(1) : Duration.days(30),
			});
		}

		const rule = new Rule(this, `ShopifyWebhookRule-${getSuffix(this)}`, {
			ruleName: `AllEventsFromShopify-${getSuffix(this)}`,
			eventBus: eventBus,
			eventPattern: {
				source: [{ prefix: "aws.partner/shopify.com" }] as any[],
				detail: {
					metadata: {
						'X-Shopify-Topic': [
							{ prefix: 'orders/create' }, 
							{ prefix: 'refunds/create' },
							{ prefix: 'customers/create' },
							{ prefix: 'checkouts/create'}
						]
					}
				}
			}
		});

		const bulkRule = new Rule(this, `ShopifyBulkWebhookRule-${getSuffix(this)}`, {
			ruleName: `ShopifyBulkWebhookRule-${getSuffix(this)}`,
			eventBus: eventBus,
			eventPattern: {
				source: [{ prefix: "aws.partner/shopify.com" }] as any[],
				detail: {
					metadata: {
						'X-Shopify-Topic': [{ prefix: 'bulk_operations/finish' }]
					}
				}
			}
		});

		const subscriptionRule = new Rule(this, `ShopifySubscriptionWebhookRule-${getSuffix(this)}`, {
			ruleName: `ShopifySubscriptionWebhookRule-${getSuffix(this)}`,
			eventBus: eventBus,
			eventPattern: {
				source: [{ prefix: "aws.partner/shopify.com" }] as any[],
				detail: {
					metadata: {
						'X-Shopify-Topic': [{ prefix: 'app_subscriptions/update' }]
					}
				}
			}
		});

		const uninstallRule = new Rule(this, `ShopifyUninstallWebhookRule-${getSuffix(this)}`, {
			ruleName: `ShopifyUninstallWebhookRule-${getSuffix(this)}`,
			eventBus: eventBus,
			eventPattern: {
				source: [{ prefix: "aws.partner/shopify.com" }] as any[],
				detail: {
					metadata: {
						'X-Shopify-Topic': [{ prefix: 'app/uninstalled' }]
					}
				}
			}
		});

		rule.addTarget(
			new SqsQueue(webhookQueue, {
				deadLetterQueue: webhookDeadletter,
				maxEventAge: Duration.minutes(30),
			})
		);

		bulkRule.addTarget(
			new SqsQueue(bulkWebhookQueue, {
				deadLetterQueue: bulkWebhookDeadletter,
				maxEventAge: Duration.minutes(30),
			})
		);

		subscriptionRule.addTarget(
			new SqsQueue(subscriptionQueue, {
				deadLetterQueue: subscriptionDeadletter,
				maxEventAge: Duration.minutes(30),
			})
		);

		uninstallRule.addTarget(
			new SqsQueue(uninstallWebhookQueue, {
				deadLetterQueue: uninstallWebhookDeadletter,
				maxEventAge: Duration.minutes(30),
			})
		);

		this.errorBucket = new Bucket(this, `Error-${getSuffix(this)}`, {
			bucketName: `shopify-error-${getSuffix(this)}`,
			removalPolicy: cdk.RemovalPolicy.RETAIN,
			autoDeleteObjects: false,
		});

		this.rawBucket = new Bucket(this, `Raw-${getSuffix(this)}`, {
			bucketName: `shopify-raw-${getSuffix(this)}`,
			removalPolicy: cdk.RemovalPolicy.RETAIN,
			autoDeleteObjects: false,
		});

		// Add DynamoDB table for bulk operations
		const bulkOperationsTable = new Table(this, `bulk-operation-${getSuffix(this)}`, {
			tableName: `bulk-operation-${getSuffix(this)}`,
			partitionKey: { name: 'bulkOperationId', type: AttributeType.STRING },
			sortKey: { name: 'organizationId', type: AttributeType.STRING },
			timeToLiveAttribute: 'ttl',
			removalPolicy: RemovalPolicy.DESTROY,
		});

		let bulkWebhook = new NodejsFunction(
			this,
			`bulk-webhook-${getSuffix(this)}`,
			{
				functionName: `bulk-webhook-${getSuffix(this)}`,
				memorySize: 1024,
				timeout: Duration.seconds(120),
				runtime: lambda.Runtime.NODEJS_18_X,
				handler: "main",
				logRetention: logs.RetentionDays.THREE_DAYS,
				entry: path.join(__dirname, "/../src/events/bulk-webhook.ts"),
				reservedConcurrentExecutions: this.isDev
					? MAX_CONCURRENCY_LAMBDAS_DEV
					: MAX_CONCURRENCY_LAMBDAS,
				environment: {
					OUTPUT_BUCKET: this.errorBucket.bucketName,
					RAW_BUCKET: this.rawBucket.bucketName,
					SECRET_ARN: dbSecret.secretArn,
					BULK_OPERATIONS_TABLE: bulkOperationsTable.tableName,
				},
				vpc: this.vpc,
				vpcSubnets: {
					subnets: [
						Subnet.fromSubnetId(
							this,
							"raleon-subnet-bulk-webhook",
							"subnet-04ef7beb8a56268d1"
						),
					],
				},
				securityGroups: [
					SecurityGroup.fromLookupByName(
						this,
						"raleon-sg-shopify-bulk-webhook",
						"default",
						this.vpc
					),
				],
			}
		);

		bulkWebhookQueue.grantSendMessages(webhookProcessor);
		bulkWebhook.addEventSource(
			new SqsEventSource(bulkWebhookQueue, {
				batchSize: 1,
				maxBatchingWindow: Duration.seconds(1),
			})
		);

		let subscriptionWebhook = new NodejsFunction(
			this,
			`subscription-webhook-${getSuffix(this)}`,
			{
				functionName: `subscription-webhook-${getSuffix(this)}`,
				memorySize: 1024,
				timeout: Duration.seconds(120),
				runtime: lambda.Runtime.NODEJS_18_X,
				handler: "main",
				logRetention: logs.RetentionDays.THREE_DAYS,
				entry: path.join(__dirname, "/../src/events/subscription-webhook.ts"),
				reservedConcurrentExecutions: this.isDev
					? MAX_CONCURRENCY_LAMBDAS_DEV
					: MAX_CONCURRENCY_LAMBDAS,
				environment: {
					RALEON_DB: raleonAppDbSecret.secretArn,
				},
				vpc: this.vpc,
				vpcSubnets: {
					subnets: [
						Subnet.fromSubnetId(
							this,
							"raleon-subnet-subscription-webhook",
							"subnet-04ef7beb8a56268d1"
						),
					],
				},
				securityGroups: [
					SecurityGroup.fromLookupByName(
						this,
						"raleon-sg-shopify-subscription-webhook",
						"default",
						this.vpc
					),
				],
			}
		);

		subscriptionQueue.grantSendMessages(subscriptionWebhook);
		subscriptionWebhook.addEventSource(
			new SqsEventSource(subscriptionQueue, {
				batchSize: 5,
				maxBatchingWindow: Duration.seconds(1),
			})
		);


		let uninstallWebhook = new NodejsFunction(
			this,
			`uninstall-webhook-${getSuffix(this)}`,
			{
				functionName: `uninstall-webhook-${getSuffix(this)}`,
				memorySize: 1024,
				timeout: Duration.seconds(120),
				runtime: lambda.Runtime.NODEJS_18_X,
				handler: "main",
				logRetention: logs.RetentionDays.THREE_DAYS,
				entry: path.join(__dirname, "/../src/events/uninstall-webhook.ts"),
				reservedConcurrentExecutions: this.isDev
					? MAX_CONCURRENCY_LAMBDAS_DEV
					: MAX_CONCURRENCY_LAMBDAS,
				environment: {
					OUTPUT_BUCKET: this.errorBucket.bucketName,
					SECRET_ARN: dbSecret.secretArn,
					CUSTOMERIO_SITEID: '********************',
					CUSTOMERIO_APIKEY: '********************',
					RALEON_DB: raleonAppDbSecret.secretArn,
				},
				vpc: this.vpc,
				vpcSubnets: {
					subnets: [
						Subnet.fromSubnetId(
							this,
							"raleon-subnet-uninstall-webhook",
							"subnet-04ef7beb8a56268d1"
						),
					],
				},
				securityGroups: [
					SecurityGroup.fromLookupByName(
						this,
						"raleon-sg-shopify-uninstall-webhook",
						"default",
						this.vpc
					),
				],
			}
		);

		uninstallWebhookQueue.grantSendMessages(webhookProcessor);
		uninstallWebhook.addEventSource(
			new SqsEventSource(uninstallWebhookQueue, {
				batchSize: 1,
				maxBatchingWindow: Duration.seconds(1),
			})
		);

		this.createOrderDataFirehose();

		let dataProcessor = new NodejsFunction(
			this,
			`data-worker-${getSuffix(this)}`,
			{
				functionName: `data-worker-${getSuffix(this)}`,
				memorySize: 1024,
				timeout: Duration.seconds(120),
				runtime: lambda.Runtime.NODEJS_18_X,
				handler: "main",
				logRetention: logs.RetentionDays.THREE_DAYS,
				entry: path.join(__dirname, "/../src/ingestion/data-worker.ts"),
				reservedConcurrentExecutions: this.isDev
					? MAX_CONCURRENCY_LAMBDAS_DEV
					: MAX_CONCURRENCY_LAMBDAS,
				environment: {
					SECRET_ARN: dbSecret.secretArn,
					RALEON_DB: raleonAppDbSecret.secretArn,
					OUTPUT_BUCKET: this.curatedBucket.bucketName,
					ATHENA_DB: this.isDev ? 'ecommerce_dev' : 'ecommerce',
					ATHENA_OUTPUT_BUCKET: `s3://${this.athenaOutput.bucketName}`,
					WEBAPP_API_URL: `http://${this.isDev ? this.node.tryGetContext('internal-dev-webapp-domain') : this.node.tryGetContext('internal-webapp-domain')}/api/v1`,
					FIREHOSE_DELIVERY_STREAM: this.orderDataFirehose.deliveryStreamName!,
				},
				vpc: this.vpc,
				vpcSubnets: {
					subnets: [
						Subnet.fromSubnetId(
							this,
							"raleon-subnet-shopify-data",
							"subnet-04ef7beb8a56268d1"
						),
					],
				},
				securityGroups: [
					SecurityGroup.fromLookupByName(
						this,
						"raleon-sg-shopify-data",
						"default",
						this.vpc
					),
				],
			}
		);

		const historicalDataInvokerDLQ = new Queue(
			this,
			`historical-invoker-deadletter-${getSuffix(this)}`,
			{
				queueName: `historical-invoker-deadletter-${getSuffix(this)}`,
				retentionPeriod: Duration.days(4),
			}
		);

		const historicalDataInvokerQueue = new Queue(this, `historical-invoker-q-${getSuffix(this)}`, {
			queueName: `historical-invoker-q-${getSuffix(this)}`,
			deadLetterQueue: {
				queue: historicalDataInvokerDLQ,
				maxReceiveCount: 1,
			},
			visibilityTimeout: Duration.seconds(900),
		});

		let historicalDataInvoker = new NodejsFunction(
			this,
			`historical-data-invoker-${getSuffix(this)}`,
			{
				functionName: `historical-data-invoker-${getSuffix(this)}`,
				memorySize: 1024,
				timeout: Duration.seconds(120),
				runtime: lambda.Runtime.NODEJS_18_X,
				handler: "main",
				logRetention: logs.RetentionDays.THREE_DAYS,
				entry: path.join(
					__dirname,
					"/../src/ingestion/historical-data-invoker.ts"
				),
				environment: {
					SECRET_ARN: dbSecret.secretArn,
					OUTPUT_BUCKET: this.curatedBucket.bucketName,
					QUEUE_URL: historicalDataInvokerQueue.queueUrl,
					ATHENA_DB: this.isDev ? 'ecommerce_dev' : 'ecommerce',
					RALEON_DB: raleonAppDbSecret.secretArn,
				},
				vpc: this.vpc,
				vpcSubnets: {
					subnets: [
						Subnet.fromSubnetId(
							this,
							`raleon-subnet-shopify-historical-data-invoker-${getSuffix(this)}`,
							"subnet-04ef7beb8a56268d1"
						),
					],
				},
				securityGroups: [
					SecurityGroup.fromLookupByName(
						this,
						`raleon-security-grp-historical-data-invoker-${getSuffix(this)}`,
						"default",
						this.vpc
					),
				],
			}
		);

		historicalDataInvokerQueue.grantSendMessages(historicalDataInvoker);

		if (!this.isDev) {
			const rule = new Rule(this, `historical-data-schedule-${getSuffix(this)}`, {
				ruleName: `historical-data-schedule-${getSuffix(this)}`,
				schedule: Schedule.cron({ minute: '0', hour: '0'}),
			});
	
			rule.addTarget(new LambdaFunction(historicalDataInvoker));
		}

		let historicalDataProcessor = new NodejsFunction(
			this,
			`historical-data-worker-${getSuffix(this)}`,
			{
				functionName: `historical-data-worker-${getSuffix(this)}`,
				memorySize: 1024,
				timeout: Duration.seconds(900),
				runtime: lambda.Runtime.NODEJS_18_X,
				handler: "main",
				logRetention: logs.RetentionDays.THREE_DAYS,
				entry: path.join(
					__dirname,
					"/../src/ingestion/historical-data-worker.ts"
				),
				reservedConcurrentExecutions: this.isDev
					? MAX_CONCURRENCY_LAMBDAS_DEV
					: 50,
				environment: {
					SECRET_ARN: dbSecret.secretArn,
					OUTPUT_BUCKET: this.curatedBucket.bucketName,
					ATHENA_OUTPUT_BUCKET: `s3://${this.athenaOutput.bucketName}`,
					RALEON_DB: raleonAppDbSecret.secretArn,
					ATHENA_DB: this.isDev ? 'ecommerce_dev' : 'ecommerce',
					QUEUE_URL: historicalDataInvokerQueue.queueUrl,
					FIREHOSE_DELIVERY_STREAM: this.orderDataFirehose.deliveryStreamName!,
					WEBAPP_API_URL: `http://${this.isDev ? this.node.tryGetContext('internal-dev-webapp-domain') : this.node.tryGetContext('internal-webapp-domain')}/api/v1`,
					METRIC_QUEUE_URL: this.props!.metricQueue.queueUrl,
				},
				vpc: this.vpc,
				vpcSubnets: {
					subnets: [
						Subnet.fromSubnetId(
							this,
							"raleon-subnet-shopify-historical-data",
							"subnet-04ef7beb8a56268d1"
						),
					],
				},
				securityGroups: [
					SecurityGroup.fromLookupByName(
						this,
						"raleon-sg-shopify-historical-data",
						"default",
						this.vpc
					),
				],
			}
		);

		historicalDataProcessor.addEventSource(
			new SqsEventSource(historicalDataInvokerQueue, {
				batchSize: 1,
				maxBatchingWindow: Duration.seconds(1),
			})
		);
		historicalDataInvokerQueue.grantConsumeMessages(historicalDataProcessor);
		historicalDataInvokerQueue.grantSendMessages(historicalDataProcessor);
		this.props?.metricQueue.grantSendMessages(historicalDataProcessor);
		
		dataProcessor.addEventSource(
			new SqsEventSource(dataQueue, {
				batchSize: 1,
				maxBatchingWindow: Duration.seconds(1),
			})
		);
		const firehosePolicy = new PolicyStatement({
			actions: ['firehose:PutRecord', 'firehose:PutRecordBatch'],
			resources: [this.orderDataFirehose.attrArn]
		});
		dataProcessor.role?.attachInlinePolicy(this.athenaPolicy);
		historicalDataProcessor.role?.attachInlinePolicy(this.athenaPolicy);
		dataProcessor.role?.addToPrincipalPolicy(firehosePolicy);
		historicalDataProcessor.role?.addToPrincipalPolicy(firehosePolicy);
		historicalDataProcessor.addPermission(`data-worker-config-permission-${getSuffix(this)}`, {
			principal: new ServicePrincipal('firehose.amazonaws.com'),
			action: 'lambda:GetFunctionConfiguration'
		});

		// Historical Data Collector Lambda
		const historicalDataCollector = new NodejsFunction(this, `historical-data-collector-${getSuffix(this)}`, {
			functionName: `historical-data-collector-${getSuffix(this)}`,
			runtime: Runtime.NODEJS_18_X,
			handler: 'main',
			entry: path.join(
				__dirname,
				"/../src/ingestion/historical-data-collector.ts"
			),
			timeout: Duration.minutes(15),
			environment: {
				SECRET_ARN: dbSecret.secretArn,
				RALEON_DB: raleonAppDbSecret.secretArn,
				WEBAPP_API_URL: `http://${this.isDev ? this.node.tryGetContext('internal-dev-webapp-domain') : this.node.tryGetContext('internal-webapp-domain')}/api/v1`,
				METRIC_QUEUE_URL: this.props!.metricQueue.queueUrl,
				BULK_OPERATIONS_TABLE: bulkOperationsTable.tableName,
			},
			vpc: this.vpc,
			vpcSubnets: {
				subnets: [
					Subnet.fromSubnetId(
						this,
						"raleon-subnet-shopify-historical-data-collector",
						"subnet-04ef7beb8a56268d1"
					),
				],
			},
			securityGroups: [
				SecurityGroup.fromLookupByName(
					this,
					"raleon-sg-shopify-historical-data-collector",
					"default",
					this.vpc
				),
			],
			memorySize: 1024
		});

		// Grant permissions to the lambda

		historicalDataCollector.addToRolePolicy(new PolicyStatement({
			actions: ['states:SendTaskSuccess'],
			resources: ['*']
		}));
		bulkOperationsTable.grantWriteData(historicalDataCollector);
		bulkOperationsTable.grantWriteData(bulkWebhook);
		bulkOperationsTable.grantReadData(bulkWebhook);

		dbSecret.grantRead(historicalDataProcessor);
		dbSecret.grantRead(dataProcessor);
		dbSecret.grantRead(bulkWebhook);
		dbSecret.grantRead(uninstallWebhook);
		dbSecret.grantRead(historicalDataCollector);
		raleonAppDbSecret.grantRead(historicalDataProcessor);
		raleonAppDbSecret.grantRead(historicalDataInvoker);
		raleonAppDbSecret.grantRead(dataProcessor);
		raleonAppDbSecret.grantRead(uninstallWebhook);
		raleonAppDbSecret.grantRead(subscriptionWebhook);
		raleonAppDbSecret.grantRead(historicalDataCollector);
		this.curatedBucket.grantReadWrite(dataProcessor);
		this.curatedBucket.grantReadWrite(historicalDataProcessor);
		this.curatedBucket.grantReadWrite(historicalDataInvoker);
		this.errorBucket.grantReadWrite(bulkWebhook);
		this.errorBucket.grantReadWrite(uninstallWebhook);
		this.rawBucket.grantReadWrite(bulkWebhook);
		webhookProcessor.role?.attachInlinePolicy(this.athenaPolicy);
		dbSecret.grantRead(webhookProcessor);
		raleonAppDbSecret.grantRead(webhookProcessor);
		this.curatedBucket.grantReadWrite(webhookProcessor);
	}

	private createOrderDataFirehose() {
		const firehosePolicy = new PolicyStatement({
			actions: [
				'glue:*',
			],
			resources: [
				`arn:aws:glue:us-east-1:${this.account}:catalog`,
				`arn:aws:glue:us-east-1:${this.account}:database/${this.isDev ? 'ecommerce_dev' : 'ecommerce'}`,
				`arn:aws:glue:us-east-1:${this.account}:table/${this.isDev ? 'ecommerce_dev' : 'ecommerce'}/orders`,
			]
		})
		const doc = new PolicyDocument();
		doc.addStatements(firehosePolicy);
		const firehoseRole = new Role(this, `Data-FirehoseRole-${getSuffix(this)}`, {
			roleName: `data-firehose-role-${getSuffix(this)}`,
			assumedBy: new ServicePrincipal('firehose.amazonaws.com'),
			inlinePolicies: { 'firehose-inline-policy': doc }
		});

		this.curatedBucket.grantReadWrite(firehoseRole);

		this.orderDataFirehose = new CfnDeliveryStream(this, `OrderDataStream-${getSuffix(this)}`, {
			deliveryStreamType: 'DirectPut',
			deliveryStreamName: `order-data-stream-${getSuffix(this)}`,
			extendedS3DestinationConfiguration: {
				bucketArn: this.curatedBucket.bucketArn,
				bufferingHints: {
					intervalInSeconds: this.isDev ? 60 : 900,
					sizeInMBs: 128,
				},
				prefix: 'shopify_orders/organization=!{partitionKeyFromQuery:organization}/',
				errorOutputPrefix: 'errors/',
				roleArn: firehoseRole.roleArn,
				dataFormatConversionConfiguration: {
					enabled: true,
					inputFormatConfiguration: {
						deserializer: {
							openXJsonSerDe: {},
						},
					},
					outputFormatConfiguration: {
						serializer: {
							parquetSerDe: {},
						},
					},
					schemaConfiguration: {
						databaseName: `${this.isDev ? 'ecommerce_dev' : 'ecommerce'}`,
						tableName: 'orders',
						region: 'us-east-1',
						roleArn: firehoseRole.roleArn,
					},
				},
				processingConfiguration: {
					enabled: true,
					processors: [
						{
							type: 'MetadataExtraction',
							parameters: [
								{
									parameterName: 'MetadataExtractionQuery',
									parameterValue: '{organization: .organization}'
								},
								{
									parameterName: 'JsonParsingEngine',
									parameterValue: 'JQ-1.6'
								}
							]
						},
						{
							type: 'RecordDeAggregation',
							parameters: [
								{
									parameterName: 'SubRecordType',
									parameterValue: 'JSON'
								}
							]
						}
					],
				},
				dynamicPartitioningConfiguration: {
					enabled: true,
				},
			},
		});
	}
	
	private getEventBusSourceName() {
		const source = this.node.tryGetContext('shopify-event-bus-sources');
		const suffix = getSuffix(this);
		return source[suffix];
	}

	private setupIntegrations() {
		this.integrationApi = new RestApi(this, 'raleon-integration', {
			restApiName: `raleon-integration-${getSuffix(this)}`,
			deployOptions: {
				stageName: 'v1'
			},
			defaultCorsPreflightOptions: {
				allowHeaders: [
					'Content-Type',
					'X-Amz-Date',
					'Authorization',
					'X-Api-Key',
				],
				allowMethods: ['OPTIONS', 'GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
				allowCredentials: true,
				allowOrigins: ['*'],
			},
		});

		const dbSecretArn = this.node.tryGetContext(
			getSuffix(this) == 'prod' 
			? 'shopify-db-secret-arn' 
			: 'shopify-db-dev-secret-arn');
		const dbSecret = Secret.fromSecretCompleteArn(
			this,
			`ShopifyDBSecret-${getSuffix(this)}`,
			dbSecretArn
		);

		const raleonIntegrationSecret = Secret.fromSecretNameV2(
			this, 
			`integration-id-${getSuffix(this)}`, 
			this.isDev ? 'integrations_dev': 'integrations_prod'
		);

		const organizationKeysSecret = Secret.fromSecretNameV2(
			this, 
			`organization-id-${getSuffix(this)}`, 
			this.isDev ? 'organization_keys_dev': 'organization_keys_prod'
		);

		const raleonAppDbSecret = Secret.fromSecretNameV2(
			this, 
			`raleonwebappDb-id-${getSuffix(this)}`, 
			this.isDev ? 'raleonwebapp': 'raleonwebapp-prod'
		);

		const integrationWebHookFn = new NodejsFunction(this, 'integration-webhook', {
			functionName: `integration-webhook-${getSuffix(this)}`,
			memorySize: 1024,
			timeout: Duration.seconds(120),
			runtime: lambda.Runtime.NODEJS_18_X,
			handler: 'main',
			logRetention: logs.RetentionDays.THREE_DAYS,
			logRetentionRetryOptions: {
				maxRetries: 5,
			},
			bundling: {
				externalModules: [],
				nodeModules: ["memcached"],
			},
			environment: {
				SECRET_ARN: dbSecret.secretArn,
				WEBAPP_API_URL: `http://${this.isDev ? this.node.tryGetContext('internal-dev-webapp-domain') : this.node.tryGetContext('internal-webapp-domain')}/api/v1`,
				INTEGRAION_SECRET_ARN: raleonIntegrationSecret.secretArn,
				ORGANIZATION_KEYS_SECRET_ARN: organizationKeysSecret.secretArn,
				RALEON_DB: raleonAppDbSecret.secretArn,
				MEMCACHED_ENDPOINT: `${this.memcachedCluster.attrConfigurationEndpointAddress}:11211`,
			},
			vpc: this.vpc,
			vpcSubnets: {
				subnets: [
					Subnet.fromSubnetId(
						this,
						"raleon-subnet-shopify-integration",
						"subnet-04ef7beb8a56268d1"
					),
				],
			},
			securityGroups: [
				SecurityGroup.fromLookupByName(
					this,
					"raleon-sg-shopify-integrations",
					"default",
					this.vpc
				),
			],
			entry: path.join(__dirname, '/../src/events/webhook-integration.ts')
		});

		dbSecret.grantRead(integrationWebHookFn);
		raleonIntegrationSecret.grantRead(integrationWebHookFn);
		raleonAppDbSecret.grantRead(integrationWebHookFn);
		integrationWebHookFn.addToRolePolicy(new PolicyStatement({
			actions: ['elasticache:*'],
			resources: ['*'],
		}));

		this.integrationApi
			.root
			.addResource('integrations')
			.addMethod(
				'POST', 
				new LambdaIntegration(integrationWebHookFn),
				{ authorizationType: apigateway.AuthorizationType.NONE,}
			);
		
		let birthdayRewardLambda = new NodejsFunction(
			this,
			`birthday-reward-${getSuffix(this)}`,
			{
				functionName: `birthday-reward-${getSuffix(this)}`,
				memorySize: 256,
				timeout: Duration.seconds(60),
				runtime: lambda.Runtime.NODEJS_18_X,
				handler: "main",
				logRetention: logs.RetentionDays.THREE_DAYS,
				entry: path.join(__dirname, "/../src/events/birthday-reward.ts"),
				reservedConcurrentExecutions: this.isDev
					? MAX_CONCURRENCY_LAMBDAS_DEV
					: MAX_CONCURRENCY_LAMBDAS,
				environment: {
					RALEON_DB: raleonAppDbSecret.secretArn,
					WEBAPP_API_URL: `http://${this.isDev ? this.node.tryGetContext('internal-dev-webapp-domain') : this.node.tryGetContext('internal-webapp-domain')}/api/v1`,
					EMAIL_QUEUE_URL: `arn:aws:sqs:${this.region}:${this.account}:loyalty-email-queue-${getSuffix(this)}`,
				},
				vpc: this.vpc,
				vpcSubnets: {
					subnets: [
						Subnet.fromSubnetId(
							this,
							"raleon-subnet-birthday-reward",
							"subnet-04ef7beb8a56268d1"
						),
					],
				},
				securityGroups: [
					SecurityGroup.fromLookupByName(
						this,
						"raleon-sg-shopify-birthday-reward",
						"default",
						this.vpc
					),
				],
			}
		);

		birthdayRewardLambda.addToRolePolicy(new PolicyStatement({
			actions: ['sqs:SendMessage'],
			resources: [`arn:aws:sqs:${this.region}:${this.account}:loyalty-email-queue-${getSuffix(this)}`],
		}));

		birthdayRewardLambda.addToRolePolicy(new PolicyStatement({
			actions: ['secretsManager:GetSecretValue'],
			resources: ['*'],
		}));

		if (!this.isDev) {
			const rule = new Rule(this, `birthday-reward-schedule-${getSuffix(this)}`, {
				ruleName: `birthday-reward-schedule-${getSuffix(this)}`,
				schedule: Schedule.cron({ minute: '0', hour: '0', day: '1'}),
			});
	
			rule.addTarget(new LambdaFunction(birthdayRewardLambda));
		}

	}
}

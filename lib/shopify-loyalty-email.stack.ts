import { Stack, StackProps, Duration, RemovalPolicy, } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { PolicyStatement } from "aws-cdk-lib/aws-iam";
import { NodejsFunction } from 'aws-cdk-lib/aws-lambda-nodejs';
import { Runtime } from 'aws-cdk-lib/aws-lambda';
import { RetentionDays } from 'aws-cdk-lib/aws-logs';
import path = require('path');
import { getSuffix } from './utils';
import { Queue } from 'aws-cdk-lib/aws-sqs';
import { SqsEventSource } from 'aws-cdk-lib/aws-lambda-event-sources';
import { IVpc, Subnet, SecurityGroup, Vpc } from 'aws-cdk-lib/aws-ec2';
import * as Utils from './utils';
import { AttributeType, BillingMode, Table } from 'aws-cdk-lib/aws-dynamodb';
import { EventBridge } from 'aws-sdk';
import { Rule, Schedule } from 'aws-cdk-lib/aws-events';
import { LambdaFunction } from 'aws-cdk-lib/aws-events-targets';

interface ShopifyLoyaltyEmailStackProps extends StackProps {
	deploy_environment_name: string;
}

export class ShopifyLoyaltyEmailStack extends Stack {

	private vpc: IVpc;
	private isDev: boolean;
	private stagingTable: Table;

	constructor(scope: Construct, id: string, private props?: ShopifyLoyaltyEmailStackProps) {
		super(scope, id, props);
		this.isDev = Utils.isDev(props!.deploy_environment_name);

		this.vpc = Vpc.fromLookup(
			this,
			`raleon-vpc-loyalty-email-${getSuffix(this)}`,
			{ vpcName: "raleon-vpc" }
		);

		const shopifyDbName = this.isDev ? 'ShopifyAppInstanceStackdevS-h9f6Wf8OwA2h-ErSpDJ' : 'ShopifyAppInstanceStackShop-ay9oN9ABj3Fr-37mgtP';
		const raleonDbName = this.isDev ? 'raleonwebapp-4J2F3H' : 'raleonwebapp-prod-e4wis3';

		this.stagingTable = new Table(this, `Loyalty-Email-Staging-${getSuffix(this)}`, {
			tableName: `loyalty-email-staging-${getSuffix(this)}`,
			partitionKey: { name: 'customerId', type: AttributeType.STRING },
			sortKey: { name: 'timestamp', type: AttributeType.STRING },
			timeToLiveAttribute: 'ttl',
			removalPolicy: this.isDev ? RemovalPolicy.DESTROY : RemovalPolicy.RETAIN,
			billingMode: BillingMode.PAY_PER_REQUEST,
		});

		const stagedEmailSchedule = Duration.minutes(15);

		const stagedEmailLambda = new NodejsFunction(this, `LoyaltyStagedEmailLambda-${getSuffix(this)}`, {
			functionName: `loyalty-staged-email-sender-${getSuffix(this)}`,
			runtime: Runtime.NODEJS_18_X,
			handler: 'main',
			entry: path.join(__dirname, `../src/notifications/loyalty-staged-email.ts`),
			environment: {
				SECRET_ARN: `arn:aws:secretsmanager:${this.props!.env!.region}:${this.props!.env!.account}:secret:${shopifyDbName}`,
				RALEON_DB: `arn:aws:secretsmanager:${this.props!.env!.region}:${this.props!.env!.account}:secret:${raleonDbName}`,
				STAGING_TABLE: this.stagingTable.tableName,
				SCHEDULE_TIME: stagedEmailSchedule.toString(),
			},
			logRetention: RetentionDays.THREE_DAYS,
			memorySize: 1024,
			timeout: Duration.minutes(15),
			vpc: this.vpc,
				vpcSubnets: {
					subnets: [
						Subnet.fromSubnetId(
							this,
							`raleon-subnet-loyalty-staged-email-${getSuffix(this)}`,
							"subnet-04ef7beb8a56268d1"
						),
					],
				},
				securityGroups: [
					SecurityGroup.fromLookupByName(
						this,
						`raleon-sg-loyalty-staged-email-${getSuffix(this)}`,
						"default",
						this.vpc
					),
				],
		});

		this.stagingTable.grantReadWriteData(stagedEmailLambda);

		stagedEmailLambda.addToRolePolicy(new PolicyStatement({
			actions: ['ses:SendEmail', 'ses:SendRawEmail', 'secretsmanager:GetSecretValue'],
			resources: ['*'],
		}));
		
		let eventRule = new Rule(this, `staged-email-schedule-${getSuffix(this)}`, {
			schedule: Schedule.rate(stagedEmailSchedule),
		});

		if (!this.isDev) {
			eventRule.addTarget(new LambdaFunction(stagedEmailLambda));
		}

		const loyaltyEmailDeadletterQueue = new Queue(this, `LoyaltyEmailDeadletterQueue-${getSuffix(this)}`, {
			queueName: `loyalty-email-deadletter-queue-${getSuffix(this)}`,
			retentionPeriod: Duration.days(2),
		});

		const loyaltyEmailQueue = new Queue(this, `LoyaltyEmailQueue-${getSuffix(this)}`, {
			queueName: `loyalty-email-queue-${getSuffix(this)}`,
			visibilityTimeout: Duration.seconds(300),
			retentionPeriod: Duration.days(4),
			deliveryDelay: Duration.seconds(0),
			deadLetterQueue: {
				queue: loyaltyEmailDeadletterQueue,
				maxReceiveCount: 3,
			}
		});

		const loyaltyEmailLambda = new NodejsFunction(this, `LoyaltyEmailLambda-${getSuffix(this)}`, {
			functionName: `loyalty-email-sender-${getSuffix(this)}`,
			runtime: Runtime.NODEJS_18_X,
			handler: 'main',
			entry: path.join(__dirname, `../src/notifications/loyalty-email.ts`),
			environment: {
				QUEUE_URL: loyaltyEmailQueue.queueUrl,
				SECRET_ARN: `arn:aws:secretsmanager:${this.props!.env!.region}:${this.props!.env!.account}:secret:${shopifyDbName}`,
				RALEON_DB: `arn:aws:secretsmanager:${this.props!.env!.region}:${this.props!.env!.account}:secret:${raleonDbName}`,
				STAGING_TABLE: this.stagingTable.tableName,
			},
			logRetention: RetentionDays.THREE_DAYS,
			memorySize: 512,
			timeout: Duration.seconds(60),
			vpc: this.vpc,
				vpcSubnets: {
					subnets: [
						Subnet.fromSubnetId(
							this,
							`raleon-subnet-loyalty-email-${getSuffix(this)}`,
							"subnet-04ef7beb8a56268d1"
						),
					],
				},
				securityGroups: [
					SecurityGroup.fromLookupByName(
						this,
						`raleon-sg-loyalty-email-${getSuffix(this)}`,
						"default",
						this.vpc
					),
				],
		});

		this.stagingTable.grantReadWriteData(loyaltyEmailLambda);

		loyaltyEmailLambda.addEventSource(new SqsEventSource(loyaltyEmailQueue, {
			batchSize: 5,
			maxBatchingWindow: Duration.seconds(5),
		}));

		loyaltyEmailLambda.addToRolePolicy(new PolicyStatement({
			actions: ['ses:SendEmail', 'ses:SendRawEmail', 'secretsmanager:GetSecretValue'],
			resources: ['*'],
		}));

		const rewardExpiringEmailLambda = new NodejsFunction(this, `LoyaltyEmail-Reward-Expiring-Lambda-${getSuffix(this)}`, {
			functionName: `loyalty-email-reward-expire-sender-${getSuffix(this)}`,
			runtime: Runtime.NODEJS_18_X,
			handler: 'main',
			entry: path.join(__dirname, `../src/notifications/reward-expiring.ts`),
			environment: {
				QUEUE_URL: loyaltyEmailQueue.queueUrl,
				SECRET_ARN: `arn:aws:secretsmanager:${this.props!.env!.region}:${this.props!.env!.account}:secret:${shopifyDbName}`,
				RALEON_DB: `arn:aws:secretsmanager:${this.props!.env!.region}:${this.props!.env!.account}:secret:${raleonDbName}`,
				STAGING_TABLE: this.stagingTable.tableName,
				WEBAPP_API_URL: `http://${this.isDev ? this.node.tryGetContext('internal-dev-webapp-domain') : this.node.tryGetContext('internal-webapp-domain')}/api/v1`,
			},
			logRetention: RetentionDays.THREE_DAYS,
			memorySize: 512,
			timeout: Duration.seconds(60),
			vpc: this.vpc,
				vpcSubnets: {
					subnets: [
						Subnet.fromSubnetId(
							this,
							`raleon-subnet-loyalty-email-rw-${getSuffix(this)}`,
							"subnet-04ef7beb8a56268d1"
						),
					],
				},
				securityGroups: [
					SecurityGroup.fromLookupByName(
						this,
						`raleon-sg-loyalty-email-rw-${getSuffix(this)}`,
						"default",
						this.vpc
					),
				],
		});

		rewardExpiringEmailLambda.addToRolePolicy(new PolicyStatement({
			actions: ['ses:SendEmail', 'ses:SendRawEmail', 'secretsmanager:GetSecretValue'],
			resources: ['*'],
		}));

		loyaltyEmailQueue.grantSendMessages(rewardExpiringEmailLambda);
		
		let rewardExpiringEmailLambdaSchedule = new Rule(this, `reward-expiring-schedule-${getSuffix(this)}`, {
			schedule: Schedule.cron({
				day: '*',
				hour: '1',
				minute: '15',
			}),
		});

		if (!this.isDev) {
			rewardExpiringEmailLambdaSchedule.addTarget(new LambdaFunction(stagedEmailLambda));
		}
	}
}

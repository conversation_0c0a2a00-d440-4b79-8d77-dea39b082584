// Program step function that handles loyalty program-specific tasks
export const programStepFunctionJson = {
  "Comment": "Step function to handle loyalty program tasks",
  "StartAt": "Program - Detect Existing",
  "States": {
    "Program - Detect Existing": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "Payload": {
          "query": "SELECT COUNT(id) FROM loyaltyprogram WHERE orgid=$1",
          "values": {
            "0.$": "$.organization_id"
          }
        },
        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST"
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        }
      ],
      "Next": "Choice (3)",
      "ResultPath": "$.existingProgram"
    },
    "Choice (3)": {
      "Type": "Choice",
      "Choices": [
        {
          "Or": [
            {
              "Variable": "$.skipProgram",
              "StringEquals": "true"
            },
            {
              "And": [
                {
                  "Not": {
                    "Variable": "$.existingProgram.Payload[0].count",
                    "StringEquals": "0"
                  }
                },
                {
                  "Not": {
                    "Variable": "$.ignoreExistingProgram",
                    "StringEquals": "true"
                  }
                }
              ]
            }
          ],
          "Next": "Program - Status - Mark Complete (2)"
        }
      ],
      "Default": "Program - Generate GPT Prompt"
    },
    "Program - Status - Mark Complete (2)": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST",
        "Payload": {
          "query": "INSERT INTO onboardingstate(state,timestamp,orgid,taskid) VALUES ($1,$2,$3,$4) ON CONFLICT ON CONSTRAINT unique_orgid_taskid DO UPDATE SET state=$1,timestamp=$2",
          "values": {
            "0": "\"Complete\"",
            "1": "NOW()",
            "3": "3",
            "2.$": "$.organization_id"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        }
      ],
      "ResultPath": null,
      "End": true
    },
    "Program - Generate GPT Prompt": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "Payload.$": "$",
        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-gpt-prompt-generator-{{suffix}}:$LATEST"
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        }
      ],
      "Next": "Program - GPT",
      "OutputPath": "$.Payload"
    },
    "Program - GPT": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-gpt-{{suffix}}:$LATEST",
        "Payload.$": "$"
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        },
        {
          "ErrorEquals": [
            "States.TaskFailed"
          ],
          "BackoffRate": 2,
          "Comment": "GPT Error",
          "IntervalSeconds": 10,
          "MaxAttempts": 12
        }
      ],
      "ResultPath": "$.gptResult2",
      "Next": "Trubrics - Report prompt/response"
    },
    "Trubrics - Report prompt/response": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-trubrics-{{suffix}}:$LATEST",
        "Payload": {
          "model.$": "$.prompt.model",
          "prompt.$": "$.prompt.messages[5].content",
          "generation.$": "$.gptResult2.Payload.output.choices[0].message.content",
          "user_id.$": "$.organization_id_string",
          "session_id.$": "$$.Execution.Id"
        }
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 1,
          "BackoffRate": 2
        }
      ],
      "Next": "Program - GPT Suggestions to JSON",
      "ResultPath": null,
      "Catch": [
        {
          "ErrorEquals": [
            "States.ALL"
          ],
          "Next": "Program - GPT Suggestions to JSON",
          "ResultPath": null
        }
      ]
    },
    "Program - GPT Suggestions to JSON": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-gpt-to-json-{{suffix}}:$LATEST",
        "Payload.$": "$"
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        }
      ],
      "Next": "Choice (6)",
      "ResultPath": "$.json"
    },
    "Choice (6)": {
      "Type": "Choice",
      "Choices": [
        {
          "Variable": "$.skipPersist",
          "StringEquals": "true",
          "Next": "Program/Campaign - Save to S3"
        }
      ],
      "Default": "Program - Translate JSON to SQL"
    },
    "Program/Campaign - Save to S3": {
      "Type": "Task",
      "Parameters": {
        "Body.$": "$.json.Payload",
        "Bucket.$": "$.programResultS3Bucket",
        "Key.$": "$.s3ResultKey"
      },
      "Resource": "arn:aws:states:::aws-sdk:s3:putObject",
      "End": true,
    },
    "Program - Translate JSON to SQL": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-json-to-postgres-{{suffix}}:$LATEST",
        "Payload": {
          "url.$": "$.url",
          "organization_id.$": "$.organization_id",
          "json.$": "$.json.Payload",
          "topLevelTableName": "loyaltyprogram",
          "dbFields.$": "$.scrapeResults.dbFields",
          "jsonOverrides": {
            "loyaltyCampaign": {
              "name": "Copilot Recommended Campaign"
            }
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        }
      ],
      "Next": "Program - Detect Existing (1)",
      "ResultPath": "$.query"
    },
    "Program - Detect Existing (1)": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "Payload": {
          "query": "SELECT COUNT(id) FROM loyaltyprogram WHERE orgid=$1",
          "values": {
            "0.$": "$.organization_id"
          }
        },
        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST"
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        }
      ],
      "Next": "Choice (4)",
      "ResultPath": "$.existingProgram"
    },
    "Choice (4)": {
      "Type": "Choice",
      "Choices": [
        {
          "And": [
            {
              "Not": {
                "Variable": "$.existingProgram.Payload[0].count",
                "StringEquals": "0"
              }
            },
            {
              "Not": {
                "Variable": "$.ignoreExistingProgram",
                "StringEquals": "true"
              }
            }
          ],
          "Next": "Program - Status - Mark Complete (1)"
        }
      ],
      "Default": "Program - Persist Program/Campaign"
    },
    "Program - Status - Mark Complete (1)": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST",
        "Payload": {
          "query": "INSERT INTO onboardingstate(state,timestamp,orgid,taskid) VALUES ($1,$2,$3,$4) ON CONFLICT ON CONSTRAINT unique_orgid_taskid DO UPDATE SET state=$1,timestamp=$2",
          "values": {
            "0": "\"Complete\"",
            "1": "NOW()",
            "3": "3",
            "2.$": "$.organization_id"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        }
      ],
      "ResultPath": null,
      "End": true
    },
    "Program - Persist Program/Campaign": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST",
        "Payload": {
          "url.$": "$.url",
          "organization_id.$": "$.organization_id",
          "query.$": "$.query.Payload",
          "values": []
        }
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        }
      ],
      "ResultPath": null,
      "Next": "Program - Status - Mark Complete"
    },
    "Program - Status - Mark Complete": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST",
        "Payload": {
          "query": "INSERT INTO onboardingstate(state,timestamp,orgid,taskid) VALUES ($1,$2,$3,$4) ON CONFLICT ON CONSTRAINT unique_orgid_taskid DO UPDATE SET state=$1,timestamp=$2",
          "values": {
            "0": "\"Complete\"",
            "1": "NOW()",
            "3": "3",
            "2.$": "$.organization_id"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        }
      ],
      "ResultPath": null,
      "End": true
    }
  }
};

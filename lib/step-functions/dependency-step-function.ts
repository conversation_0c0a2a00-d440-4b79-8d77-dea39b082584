// Dependency step function that handles initial web scraping and Shopify data gathering
export const dependencyStepFunctionJson = {
  "Comment": "Step function to handle initial dependency data gathering",
  "StartAt": "Dependency - Parallel Processes",
  "States": {
    "Dependency - Parallel Processes": {
      "Type": "Parallel",
      "Branches": [
        {
          "StartAt": "Branding - Status - Mark Start",
          "States": {
            "Branding - Status - Mark Start": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST",
                "Payload": {
                  "query": "INSERT INTO onboardingstate(state,timestamp,orgid,taskid) VALUES ($1,$2,$3,$4) ON CONFLICT ON CONSTRAINT unique_orgid_taskid DO UPDATE SET state=$1,timestamp=$2",
                  "values": {
                    "0": "\"Started\"",
                    "1": "NOW()",
                    "3": "1",
                    "2.$": "$.organization_id"
                  }
                }
              },
              "Retry": [
                {
                  "ErrorEquals": [
                    "Lambda.ServiceException",
                    "Lambda.AWSLambdaException",
                    "Lambda.SdkClientException",
                    "Lambda.TooManyRequestsException"
                  ],
                  "IntervalSeconds": 1,
                  "MaxAttempts": 3,
                  "BackoffRate": 2
                }
              ],
              "Next": "Branding - Retrieve previous scrape",
              "ResultPath": null
            },
            "Branding - Retrieve previous scrape": {
              "Type": "Task",
              "Next": "Branding - JSON Parse",
              "Parameters": {
                "Bucket.$": "$.scrapeResultS3Bucket",
                "Key.$": "$.organization_id_string"
              },
              "Resource": "arn:aws:states:::aws-sdk:s3:getObject",
              "Catch": [
                {
                  "ErrorEquals": [
                    "States.ALL"
                  ],
                  "Next": "Branding - Web Scrape (text, screenshot)",
                  "ResultPath": null
                }
              ],
              "ResultSelector": {
                "jsonContainer.$": "$.Body"
              }
            },
            "Branding - JSON Parse": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "Payload.$": "$",
                "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-json-parser-{{suffix}}:$LATEST"
              },
              "Retry": [
                {
                  "ErrorEquals": [
                    "Lambda.ServiceException",
                    "Lambda.AWSLambdaException",
                    "Lambda.SdkClientException",
                    "Lambda.TooManyRequestsException"
                  ],
                  "IntervalSeconds": 1,
                  "MaxAttempts": 3,
                  "BackoffRate": 2
                }
              ],
              "OutputPath": "$.Payload.Payload",
              "End": true
            },
            "Branding - Web Scrape (text, screenshot)": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "Payload.$": "$",
                "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-web-scraper-{{suffix}}:$LATEST"
              },
              "Retry": [
                {
                  "ErrorEquals": [
                    "Lambda.ServiceException",
                    "Lambda.AWSLambdaException",
                    "Lambda.SdkClientException",
                    "Lambda.TooManyRequestsException"
                  ],
                  "IntervalSeconds": 1,
                  "MaxAttempts": 3,
                  "BackoffRate": 2
                },
                {
                  "ErrorEquals": [
                    "States.ALL"
                  ],
                  "BackoffRate": 3,
                  "IntervalSeconds": 1,
                  "MaxAttempts": 5,
                  "Comment": "Timeout retrier"
                }
              ],
              "Next": "Branding - Persist scrape result",
              "ResultPath": "$.scrape"
            },
            "Branding - Persist scrape result": {
              "Type": "Task",
              "Parameters": {
                "Body.$": "$.scrape",
                "Bucket.$": "$.scrapeResultS3Bucket",
                "Key.$": "$.organization_id_string"
              },
              "Resource": "arn:aws:states:::aws-sdk:s3:putObject",
              "ResultPath": null,
              "OutputPath": "$.scrape.Payload",
              "End": true
            }
          }
        },
        {
          "StartAt": "Program - Status - Mark Start",
          "States": {
            "Program - Status - Mark Start": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST",
                "Payload": {
                  "query": "INSERT INTO onboardingstate(state,timestamp,orgid,taskid) VALUES ($1,$2,$3,$4) ON CONFLICT ON CONSTRAINT unique_orgid_taskid DO UPDATE SET state=$1,timestamp=$2",
                  "values": {
                    "0": "\"Started\"",
                    "1": "NOW()",
                    "3": "3",
                    "2.$": "$.organization_id"
                  }
                }
              },
              "Retry": [
                {
                  "ErrorEquals": [
                    "Lambda.ServiceException",
                    "Lambda.AWSLambdaException",
                    "Lambda.SdkClientException",
                    "Lambda.TooManyRequestsException"
                  ],
                  "IntervalSeconds": 1,
                  "MaxAttempts": 3,
                  "BackoffRate": 2
                }
              ],
              "ResultPath": null,
              "Next": "Catalog - Retrieve previous"
            },
            "Catalog - Retrieve previous": {
              "Type": "Task",
              "Next": "Catalog - JSON Parse",
              "Parameters": {
                "Bucket.$": "$.catalogResultS3Bucket",
                "Key.$": "$.organization_id_string"
              },
              "Resource": "arn:aws:states:::aws-sdk:s3:getObject",
              "Catch": [
                {
                  "ErrorEquals": [
                    "States.ALL"
                  ],
                  "ResultPath": null,
                  "Next": "Choice (7)"
                }
              ],
              "ResultSelector": {
                "jsonContainer.$": "$.Body"
              }
            },
            "Catalog - JSON Parse": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "Payload.$": "$",
                "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-json-parser-{{suffix}}:$LATEST"
              },
              "Retry": [
                {
                  "ErrorEquals": [
                    "Lambda.ServiceException",
                    "Lambda.AWSLambdaException",
                    "Lambda.SdkClientException",
                    "Lambda.TooManyRequestsException"
                  ],
                  "IntervalSeconds": 1,
                  "MaxAttempts": 3,
                  "BackoffRate": 2
                }
              ],
              "OutputPath": "$.Payload.Payload",
              "End": true
            },
            "Choice (7)": {
              "Type": "Choice",
              "Choices": [
                {
                  "Variable": "$.skipShopify",
                  "StringEquals": "true",
                  "Next": "Pass"
                }
              ],
              "Default": "Program - Get Shopify Token"
            },
            "Pass": {
              "Type": "Pass",
              "End": true,
              "Result": {
                "data": {
                  "products": {
                    "edges": [
                      {
                        "node": {
                          "id": "gid://shopify/Product/8438965600546",
                          "createdAt": "2023-08-01T14:02:25Z",
                          "updatedAt": "2023-08-25T13:35:28Z",
                          "title": "The Multi-location Snowboard",
                          "handle": "the-multi-location-snowboard",
                          "productType": "",
                          "priceRangeV2": {
                            "maxVariantPrice": {
                              "amount": "729.95",
                              "currencyCode": "USD"
                            },
                            "minVariantPrice": {
                              "amount": "729.95",
                              "currencyCode": "USD"
                            }
                          },
                          "description": "",
                          "totalInventory": 95,
                          "status": "ACTIVE"
                        }
                      },
                      {
                        "node": {
                          "id": "gid://shopify/Product/8438965797154",
                          "createdAt": "2023-08-01T14:02:28Z",
                          "updatedAt": "2023-08-23T20:49:20Z",
                          "title": "The Collection Snowboard: Liquid",
                          "handle": "the-collection-snowboard-liquid",
                          "productType": "",
                          "priceRangeV2": {
                            "maxVariantPrice": {
                              "amount": "749.95",
                              "currencyCode": "USD"
                            },
                            "minVariantPrice": {
                              "amount": "749.95",
                              "currencyCode": "USD"
                            }
                          },
                          "description": "",
                          "totalInventory": 49,
                          "status": "ACTIVE"
                        }
                      },
                      {
                        "node": {
                          "id": "gid://shopify/Product/8438965305634",
                          "createdAt": "2023-08-01T14:02:24Z",
                          "updatedAt": "2023-08-23T20:49:20Z",
                          "title": "The Complete Snowboard",
                          "handle": "the-complete-snowboard",
                          "productType": "snowboard",
                          "priceRangeV2": {
                            "maxVariantPrice": {
                              "amount": "699.95",
                              "currencyCode": "USD"
                            },
                            "minVariantPrice": {
                              "amount": "699.95",
                              "currencyCode": "USD"
                            }
                          },
                          "description": "This PREMIUM snowboard is so SUPERDUPER awesome!",
                          "totalInventory": 34,
                          "status": "ACTIVE"
                        }
                      },
                      {
                        "node": {
                          "id": "gid://shopify/Product/8438965731618",
                          "createdAt": "2023-08-01T14:02:27Z",
                          "updatedAt": "2023-08-23T20:49:16Z",
                          "title": "Selling Plans Ski Wax",
                          "handle": "selling-plans-ski-wax",
                          "productType": "",
                          "priceRangeV2": {
                            "maxVariantPrice": {
                              "amount": "49.95",
                              "currencyCode": "USD"
                            },
                            "minVariantPrice": {
                              "amount": "9.95",
                              "currencyCode": "USD"
                            }
                          },
                          "description": "",
                          "totalInventory": 29,
                          "status": "ACTIVE"
                        }
                      },
                      {
                        "node": {
                          "id": "gid://shopify/Product/8438965666082",
                          "createdAt": "2023-08-01T14:02:25Z",
                          "updatedAt": "2023-08-01T14:05:19Z",
                          "title": "The 3p Fulfilled Snowboard",
                          "handle": "the-3p-fulfilled-snowboard",
                          "productType": "",
                          "priceRangeV2": {
                            "maxVariantPrice": {
                              "amount": "2629.95",
                              "currencyCode": "USD"
                            },
                            "minVariantPrice": {
                              "amount": "2629.95",
                              "currencyCode": "USD"
                            }
                          },
                          "description": "",
                          "totalInventory": 19,
                          "status": "ACTIVE"
                        }
                      },
                      {
                        "node": {
                          "id": "gid://shopify/Product/8438965207330",
                          "createdAt": "2023-08-01T14:02:23Z",
                          "updatedAt": "2023-08-01T14:05:19Z",
                          "title": "The Minimal Snowboard",
                          "handle": "the-minimal-snowboard",
                          "productType": "",
                          "priceRangeV2": {
                            "maxVariantPrice": {
                              "amount": "885.95",
                              "currencyCode": "USD"
                            },
                            "minVariantPrice": {
                              "amount": "885.95",
                              "currencyCode": "USD"
                            }
                          },
                          "description": "",
                          "totalInventory": 39,
                          "status": "ACTIVE"
                        }
                      },
                      {
                        "node": {
                          "id": "gid://shopify/Product/8438965633314",
                          "createdAt": "2023-08-01T14:02:25Z",
                          "updatedAt": "2023-08-01T14:02:48Z",
                          "title": "The Multi-managed Snowboard",
                          "handle": "the-multi-managed-snowboard",
                          "productType": "",
                          "priceRangeV2": {
                            "maxVariantPrice": {
                              "amount": "629.95",
                              "currencyCode": "USD"
                            },
                            "minVariantPrice": {
                              "amount": "629.95",
                              "currencyCode": "USD"
                            }
                          },
                          "description": "",
                          "totalInventory": 100,
                          "status": "ACTIVE"
                        }
                      },
                      {
                        "node": {
                          "id": "gid://shopify/Product/8438965698850",
                          "createdAt": "2023-08-01T14:02:26Z",
                          "updatedAt": "2023-08-01T14:02:44Z",
                          "title": "The Collection Snowboard: Oxygen",
                          "handle": "the-collection-snowboard-oxygen",
                          "productType": "",
                          "priceRangeV2": {
                            "maxVariantPrice": {
                              "amount": "1025.0",
                              "currencyCode": "USD"
                            },
                            "minVariantPrice": {
                              "amount": "1025.0",
                              "currencyCode": "USD"
                            }
                          },
                          "description": "",
                          "totalInventory": 50,
                          "status": "ACTIVE"
                        }
                      },
                      {
                        "node": {
                          "id": "gid://shopify/Product/8438965436706",
                          "createdAt": "2023-08-01T14:02:24Z",
                          "updatedAt": "2023-08-01T14:02:35Z",
                          "title": "The Archived Snowboard",
                          "handle": "the-archived-snowboard",
                          "productType": "",
                          "priceRangeV2": {
                            "maxVariantPrice": {
                              "amount": "629.95",
                              "currencyCode": "USD"
                            },
                            "minVariantPrice": {
                              "amount": "629.95",
                              "currencyCode": "USD"
                            }
                          },
                          "description": "",
                          "totalInventory": 50,
                          "status": "ARCHIVED"
                        }
                      },
                      {
                        "node": {
                          "id": "gid://shopify/Product/8438965272866",
                          "createdAt": "2023-08-01T14:02:24Z",
                          "updatedAt": "2023-08-01T14:02:35Z",
                          "title": "The Hidden Snowboard",
                          "handle": "the-hidden-snowboard",
                          "productType": "",
                          "priceRangeV2": {
                            "maxVariantPrice": {
                              "amount": "749.95",
                              "currencyCode": "USD"
                            },
                            "minVariantPrice": {
                              "amount": "749.95",
                              "currencyCode": "USD"
                            }
                          },
                          "description": "",
                          "totalInventory": 50,
                          "status": "ACTIVE"
                        }
                      },
                      {
                        "node": {
                          "id": "gid://shopify/Product/8438965469474",
                          "createdAt": "2023-08-01T14:02:24Z",
                          "updatedAt": "2023-08-01T14:02:32Z",
                          "title": "The Collection Snowboard: Hydrogen",
                          "handle": "the-collection-snowboard-hydrogen",
                          "productType": "",
                          "priceRangeV2": {
                            "maxVariantPrice": {
                              "amount": "600.0",
                              "currencyCode": "USD"
                            },
                            "minVariantPrice": {
                              "amount": "600.0",
                              "currencyCode": "USD"
                            }
                          },
                          "description": "",
                          "totalInventory": 50,
                          "status": "ACTIVE"
                        }
                      },
                      {
                        "node": {
                          "id": "gid://shopify/Product/8438965567778",
                          "createdAt": "2023-08-01T14:02:24Z",
                          "updatedAt": "2023-08-01T14:02:27Z",
                          "title": "The Compare at Price Snowboard",
                          "handle": "the-compare-at-price-snowboard",
                          "productType": "",
                          "priceRangeV2": {
                            "maxVariantPrice": {
                              "amount": "785.95",
                              "currencyCode": "USD"
                            },
                            "minVariantPrice": {
                              "amount": "785.95",
                              "currencyCode": "USD"
                            }
                          },
                          "description": "",
                          "totalInventory": 10,
                          "status": "ACTIVE"
                        }
                      },
                      {
                        "node": {
                          "id": "gid://shopify/Product/8438965403938",
                          "createdAt": "2023-08-01T14:02:24Z",
                          "updatedAt": "2023-08-01T14:02:27Z",
                          "title": "The Draft Snowboard",
                          "handle": "the-draft-snowboard",
                          "productType": "",
                          "priceRangeV2": {
                            "maxVariantPrice": {
                              "amount": "2629.95",
                              "currencyCode": "USD"
                            },
                            "minVariantPrice": {
                              "amount": "2629.95",
                              "currencyCode": "USD"
                            }
                          },
                          "description": "",
                          "totalInventory": 20,
                          "status": "DRAFT"
                        }
                      },
                      {
                        "node": {
                          "id": "gid://shopify/Product/8438965535010",
                          "createdAt": "2023-08-01T14:02:24Z",
                          "updatedAt": "2023-08-01T14:02:26Z",
                          "title": "Gift Card",
                          "handle": "gift-card",
                          "productType": "",
                          "priceRangeV2": {
                            "maxVariantPrice": {
                              "amount": "100.0",
                              "currencyCode": "USD"
                            },
                            "minVariantPrice": {
                              "amount": "10.0",
                              "currencyCode": "USD"
                            }
                          },
                          "description": "This is a gift card for the store",
                          "totalInventory": 0,
                          "status": "ACTIVE"
                        }
                      },
                      {
                        "node": {
                          "id": "gid://shopify/Product/8438965502242",
                          "createdAt": "2023-08-01T14:02:24Z",
                          "updatedAt": "2023-08-01T14:02:26Z",
                          "title": "The Inventory Not Tracked Snowboard",
                          "handle": "the-inventory-not-tracked-snowboard",
                          "productType": "",
                          "priceRangeV2": {
                            "maxVariantPrice": {
                              "amount": "949.95",
                              "currencyCode": "USD"
                            },
                            "minVariantPrice": {
                              "amount": "949.95",
                              "currencyCode": "USD"
                            }
                          },
                          "description": "",
                          "totalInventory": 0,
                          "status": "ACTIVE"
                        }
                      },
                      {
                        "node": {
                          "id": "gid://shopify/Product/8438965240098",
                          "createdAt": "2023-08-01T14:02:23Z",
                          "updatedAt": "2023-08-01T14:02:26Z",
                          "title": "The Videographer Snowboard",
                          "handle": "the-videographer-snowboard",
                          "productType": "",
                          "priceRangeV2": {
                            "maxVariantPrice": {
                              "amount": "885.95",
                              "currencyCode": "USD"
                            },
                            "minVariantPrice": {
                              "amount": "885.95",
                              "currencyCode": "USD"
                            }
                          },
                          "description": "",
                          "totalInventory": 50,
                          "status": "ACTIVE"
                        }
                      },
                      {
                        "node": {
                          "id": "gid://shopify/Product/8438965371170",
                          "createdAt": "2023-08-01T14:02:24Z",
                          "updatedAt": "2023-08-01T14:02:25Z",
                          "title": "The Out of Stock Snowboard",
                          "handle": "the-out-of-stock-snowboard",
                          "productType": "",
                          "priceRangeV2": {
                            "maxVariantPrice": {
                              "amount": "885.95",
                              "currencyCode": "USD"
                            },
                            "minVariantPrice": {
                              "amount": "885.95",
                              "currencyCode": "USD"
                            }
                          },
                          "description": "",
                          "totalInventory": 0,
                          "status": "ACTIVE"
                        }
                      }
                    ]
                  }
                },
                "extensions": {
                  "cost": {
                    "requestedQueryCost": 502,
                    "actualQueryCost": 36,
                    "throttleStatus": {
                      "maximumAvailable": 1000,
                      "currentlyAvailable": 964,
                      "restoreRate": 50
                    }
                  }
                }
              }
            },
            "Program - Get Shopify Token": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "Payload.$": "$",
                "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-shopify-token-{{suffix}}:$LATEST"
              },
              "Retry": [
                {
                  "ErrorEquals": [
                    "Lambda.ServiceException",
                    "Lambda.AWSLambdaException",
                    "Lambda.SdkClientException",
                    "Lambda.TooManyRequestsException"
                  ],
                  "IntervalSeconds": 1,
                  "MaxAttempts": 3,
                  "BackoffRate": 2
                }
              ],
              "ResultPath": "$.token",
              "Next": "Choice"
            },
            "Choice": {
              "Type": "Choice",
              "Choices": [
                {
                  "Next": "Pass (1)",
                  "Variable": "$.token.Payload",
                  "IsNull": true
                }
              ],
              "Default": "Program - Get Shopify Catalog"
            },
            "Pass (1)": {
              "Type": "Pass",
              "End": true
            },
            "Program - Get Shopify Catalog": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-shopify-catalog-summarizer-{{suffix}}:$LATEST",
                "Payload.$": "$"
              },
              "Retry": [
                {
                  "ErrorEquals": [
                    "Lambda.ServiceException",
                    "Lambda.AWSLambdaException",
                    "Lambda.SdkClientException",
                    "Lambda.TooManyRequestsException"
                  ],
                  "IntervalSeconds": 1,
                  "MaxAttempts": 3,
                  "BackoffRate": 2
                }
              ],
              "Next": "Catalog - Persist result",
              "ResultPath": "$.catalog"
            },
            "Catalog - Persist result": {
              "Type": "Task",
              "Parameters": {
                "Body.$": "$.catalog",
                "Bucket.$": "$.catalogResultS3Bucket",
                "Key.$": "$.organization_id_string"
              },
              "Resource": "arn:aws:states:::aws-sdk:s3:putObject",
              "ResultPath": null,
              "OutputPath": "$.catalog.Payload",
              "End": true
            }
          }
        },
        {
          "StartAt": "Program - Query UICustomerActions",
          "States": {
            "Program - Query UICustomerActions": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "Payload": {
                  "query": "SELECT * FROM uicustomeraction WHERE enabled=true",
                  "values": {}
                },
                "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST"
              },
              "Retry": [
                {
                  "ErrorEquals": [
                    "Lambda.ServiceException",
                    "Lambda.AWSLambdaException",
                    "Lambda.SdkClientException",
                    "Lambda.TooManyRequestsException"
                  ],
                  "IntervalSeconds": 1,
                  "MaxAttempts": 3,
                  "BackoffRate": 2
                }
              ],
              "End": true,
              "OutputPath": "$.Payload"
            }
          }
        },
        {
          "StartAt": "Program - Query UICustomerActionConditions",
          "States": {
            "Program - Query UICustomerActionConditions": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "Payload": {
                  "query": "SELECT * FROM uicustomeractioncondition",
                  "values": {}
                },
                "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST"
              },
              "Retry": [
                {
                  "ErrorEquals": [
                    "Lambda.ServiceException",
                    "Lambda.AWSLambdaException",
                    "Lambda.SdkClientException",
                    "Lambda.TooManyRequestsException"
                  ],
                  "IntervalSeconds": 1,
                  "MaxAttempts": 3,
                  "BackoffRate": 2
                }
              ],
              "End": true,
              "OutputPath": "$.Payload"
            }
          }
        },
        {
          "StartAt": "Program - Query UICustomerAction Rewards",
          "States": {
            "Program - Query UICustomerAction Rewards": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "Payload": {
                  "query": "SELECT uicr.* FROM uiactionrewardjunction AS uiarj LEFT JOIN uicustomerreward uicr ON uiarj.uicustomerrewardid = uicr.id",
                  "values": {}
                },
                "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST"
              },
              "Retry": [
                {
                  "ErrorEquals": [
                    "Lambda.ServiceException",
                    "Lambda.AWSLambdaException",
                    "Lambda.SdkClientException",
                    "Lambda.TooManyRequestsException"
                  ],
                  "IntervalSeconds": 1,
                  "MaxAttempts": 3,
                  "BackoffRate": 2
                }
              ],
              "End": true,
              "OutputPath": "$.Payload"
            }
          }
        },
        {
          "StartAt": "Program - Query Reward Restrictions",
          "States": {
            "Program - Query Reward Restrictions": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "Payload": {
                  "query": "SELECT * FROM uirewardrestriction",
                  "values": {}
                },
                "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST"
              },
              "Retry": [
                {
                  "ErrorEquals": [
                    "Lambda.ServiceException",
                    "Lambda.AWSLambdaException",
                    "Lambda.SdkClientException",
                    "Lambda.TooManyRequestsException"
                  ],
                  "IntervalSeconds": 1,
                  "MaxAttempts": 3,
                  "BackoffRate": 2
                }
              ],
              "End": true,
              "OutputPath": "$.Payload"
            }
          }
        },
        {
          "StartAt": "Program - Query Reward Limits",
          "States": {
            "Program - Query Reward Limits": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "Payload": {
                  "query": "SELECT * FROM uirewardlimit",
                  "values": {}
                },
                "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST"
              },
              "Retry": [
                {
                  "ErrorEquals": [
                    "Lambda.ServiceException",
                    "Lambda.AWSLambdaException",
                    "Lambda.SdkClientException",
                    "Lambda.TooManyRequestsException"
                  ],
                  "IntervalSeconds": 1,
                  "MaxAttempts": 3,
                  "BackoffRate": 2
                }
              ],
              "End": true,
              "OutputPath": "$.Payload"
            }
          }
        },
        {
          "StartAt": "Program - Query Rewards",
          "States": {
            "Program - Query Rewards": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "Payload": {
                  "query": "SELECT * FROM uicustomerreward WHERE enabled=true AND canshowinshop=true",
                  "values": {}
                },
                "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST"
              },
              "Retry": [
                {
                  "ErrorEquals": [
                    "Lambda.ServiceException",
                    "Lambda.AWSLambdaException",
                    "Lambda.SdkClientException",
                    "Lambda.TooManyRequestsException"
                  ],
                  "IntervalSeconds": 1,
                  "MaxAttempts": 3,
                  "BackoffRate": 2
                }
              ],
              "End": true,
              "OutputPath": "$.Payload"
            }
          }
        },
        {
          "StartAt": "Program - Query Shop Item Conditions",
          "States": {
            "Program - Query Shop Item Conditions": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "Payload": {
                  "query": "SELECT * FROM uishopitemcondition",
                  "values": {}
                },
                "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST"
              },
              "Retry": [
                {
                  "ErrorEquals": [
                    "Lambda.ServiceException",
                    "Lambda.AWSLambdaException",
                    "Lambda.SdkClientException",
                    "Lambda.TooManyRequestsException"
                  ],
                  "IntervalSeconds": 1,
                  "MaxAttempts": 3,
                  "BackoffRate": 2
                }
              ],
              "End": true,
              "OutputPath": "$.Payload"
            }
          }
        },
        {
          "StartAt": "Program - Query All Fields",
          "States": {
            "Program - Query All Fields": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST",
                "Payload": {
                  "url.$": "$.url",
                  "organization_id.$": "$.organization_id",
                  "query": "SELECT table_name, column_name, data_type, column_default, is_nullable FROM information_schema.columns WHERE table_schema = 'public';",
                  "values": []
                }
              },
              "Retry": [
                {
                  "ErrorEquals": [
                    "Lambda.ServiceException",
                    "Lambda.AWSLambdaException",
                    "Lambda.SdkClientException",
                    "Lambda.TooManyRequestsException"
                  ],
                  "IntervalSeconds": 1,
                  "MaxAttempts": 3,
                  "BackoffRate": 2
                }
              ],
              "End": true,
              "OutputPath": "$.Payload"
            }
          }
        }
      ],
      "Next": "Trigger Next Step Functions",
      "ResultSelector": {
        "web.$": "$[0]",
        "shopify.$": "$[1]",
        "wte.$": "$[2]",
        "wteConditions.$": "$[3]",
        "wteRewards.$": "$[4]",
        "rewardRestrictions.$": "$[5]",
        "rewardLimits.$": "$[6]",
        "rewards.$": "$[7]",
        "rewardConditions.$": "$[8]",
        "dbFields.$": "$[9]"
      },
      "ResultPath": "$.scrapeResults"
    },
    "Trigger Next Step Functions": {
      "Type": "Parallel",
      "Branches": [
        {
          "StartAt": "Start Branding Step Function",
          "States": {
            "Start Branding Step Function": {
              "Type": "Task",
              "Resource": "arn:aws:states:::states:startExecution",
              "Parameters": {
                "StateMachineArn": "arn:aws:states:us-east-1:831543322268:stateMachine:BrandingStepFunction-{{suffix}}",
                "Input": {
                  "url.$": "$.url",
                  "organization_id.$": "$.organization_id",
                  "organization_id_string.$": "$.organization_id_string",
                  "storePassword.$": "$.storePassword",
                  "metrics.$": "$.metrics",
                  "useGptVision.$": "$.useGptVision",
                  "campaignId.$": "$.campaignId",
                  "skipPersist.$": "$.skipPersist",
                  "programResultS3Bucket.$": "$.programResultS3Bucket",
                  "catalogResultS3Bucket.$": "$.catalogResultS3Bucket",
                  "scrapeResultS3Bucket.$": "$.scrapeResultS3Bucket",
                  "s3ResultKey.$": "$.s3ResultKey",
                  "allowGptTemplateSuggestion.$": "$.allowGptTemplateSuggestion",
                  "includeCampaignSummary.$": "$.includeCampaignSummary",
                  "skipShopify.$": "$.skipShopify",
                  "topLevelTableName.$": "$.topLevelTableName",
                  "skipBranding.$": "$.skipBranding",
                  "overwriteBranding.$": "$.overwriteBranding",
                  "skipProgram.$": "$.skipProgram",
                  "ignoreExistingProgram.$": "$.ignoreExistingProgram",
                  "skipInsights.$": "$.skipInsights",
                  "ignoreExistingInsights.$": "$.ignoreExistingInsights",
                  "programSetupPrompt.$": "$.programSetupPrompt",
                  "programResponsePrompt.$": "$.programResponsePrompt",
                  "programUserPrompt.$": "$.programUserPrompt",
                  "scrapeResults.$": "$.scrapeResults",
                  "AWS_STEP_FUNCTIONS_STARTED_BY_EXECUTION_ID.$": "$$.Execution.Id"
                }
              },
              "End": true
            }
          }
        },
        {
          "StartAt": "Start Program Step Function",
          "States": {
            "Start Program Step Function": {
              "Type": "Task",
              "Resource": "arn:aws:states:::states:startExecution",
              "Parameters": {
                "StateMachineArn": "arn:aws:states:us-east-1:831543322268:stateMachine:ProgramStepFunction-{{suffix}}",
                "Input": {
                  "url.$": "$.url",
                  "organization_id.$": "$.organization_id",
                  "organization_id_string.$": "$.organization_id_string",
                  "storePassword.$": "$.storePassword",
                  "metrics.$": "$.metrics",
                  "useGptVision.$": "$.useGptVision",
                  "campaignId.$": "$.campaignId",
                  "skipPersist.$": "$.skipPersist",
                  "programResultS3Bucket.$": "$.programResultS3Bucket",
                  "catalogResultS3Bucket.$": "$.catalogResultS3Bucket",
                  "scrapeResultS3Bucket.$": "$.scrapeResultS3Bucket",
                  "s3ResultKey.$": "$.s3ResultKey",
                  "allowGptTemplateSuggestion.$": "$.allowGptTemplateSuggestion",
                  "includeCampaignSummary.$": "$.includeCampaignSummary",
                  "skipShopify.$": "$.skipShopify",
                  "topLevelTableName.$": "$.topLevelTableName",
                  "skipBranding.$": "$.skipBranding",
                  "overwriteBranding.$": "$.overwriteBranding",
                  "skipProgram.$": "$.skipProgram",
                  "ignoreExistingProgram.$": "$.ignoreExistingProgram",
                  "skipInsights.$": "$.skipInsights",
                  "ignoreExistingInsights.$": "$.ignoreExistingInsights",
                  "programSetupPrompt.$": "$.programSetupPrompt",
                  "programResponsePrompt.$": "$.programResponsePrompt",
                  "programUserPrompt.$": "$.programUserPrompt",
                  "scrapeResults.$": "$.scrapeResults",
                  "AWS_STEP_FUNCTIONS_STARTED_BY_EXECUTION_ID.$": "$$.Execution.Id"
                }
              },
              "End": true
            }
          }
        }
      ],
      "End": true
    }
  }
};
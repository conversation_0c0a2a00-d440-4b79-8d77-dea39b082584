// Member Insights step function that handles member insights-specific tasks
export const memberInsightsStepFunctionJson = {
  "Comment": "Step function to handle member insights tasks",
  "StartAt": "Member Insights - Status - Mark Start",
  "States": {
    "Member Insights - Status - Mark Start": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "onboard-postgres-{{suffix}}:$LATEST",
        "Payload": {
          "query": "INSERT INTO onboardingstate(state,timestamp,orgid,taskid) VALUES ($1,$2,$3,$4) ON CONFLICT ON CONSTRAINT unique_orgid_taskid DO UPDATE SET state=$1,timestamp=$2",
          "values": {
            "0": "\"Started\"",
            "1": "NOW()",
            "3": "2",
            "2.$": "$.organization_id"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        }
      ],
      "Next": "Get Bulk Shopify Orders",
      "ResultPath": null
    },
    "Get Bulk Shopify Orders": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke.waitForTaskToken",
      "Parameters": {
        "FunctionName": "historical-data-collector-{{suffix}}:$LATEST",
        "Payload": {
          "orgId.$": "$.organization_id",
          "taskToken.$": "$$.Task.Token"
        }
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2,
          "JitterStrategy": "FULL"
        }
      ],
      "Next": "Choice"
    },
    "Choice": {
      "Type": "Choice",
      "Choices": [
        {
          "Variable": "$.s3Location",
          "IsNull": true,
          "Next": "Historical Data - Mark Complete (1)"
        },
        {
          "Variable": "$.errorMessage",
          "StringEquals": "Shop info not found",
          "Next": "Member Insights - Status - Delete"
        }
      ],
      "Default": "Glue Process Bulk Order Data"
    },
    "Member Insights - Status - Delete": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "onboard-postgres-{{suffix}}:$LATEST",
        "Payload": {
          "query": "DELETE FROM onboardingstate WHERE orgid = $1 AND taskid = $2",
          "values": {
            "1": "2",
            "0.$": "$.organizationId"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        }
      ],
      "ResultPath": null,
      "End": true
    },
    "Historical Data - Mark Complete (1)": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "onboard-postgres-{{suffix}}:$LATEST",
        "Payload": {
          "query": "UPDATE Organization set historicaldatacomplete = true WHERE id = $1",
          "values": {
            "0.$": "$.organizationId"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        }
      ],
      "ResultPath": null,
      "Next": "Member Insights - Status - Mark Complete (1)"
    },
    "Member Insights - Status - Mark Complete (1)": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "onboard-postgres-{{suffix}}:$LATEST",
        "Payload": {
          "query": "INSERT INTO onboardingstate(state,timestamp,orgid,taskid) VALUES ($1,$2,$3,$4) ON CONFLICT ON CONSTRAINT unique_orgid_taskid DO UPDATE SET state=$1,timestamp=$2",
          "values": {
            "0": "\"Complete\"",
            "1": "NOW()",
            "3": "2",
            "2.$": "$.organizationId"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        }
      ],
      "ResultPath": null,
      "End": true
    },
    "Glue Process Bulk Order Data": {
      "Type": "Task",
      "Resource": "arn:aws:states:::glue:startJobRun.sync",
      "Parameters": {
        "JobName": "Historical-Data-{{suffix}}",
        "Arguments": {
          "--status.$": "$.status",
          "--s3Location.$": "$.s3Location",
          "--bulkOperationId.$": "$.bulkOperationId",
          "--organizationId.$": "$.organizationId"
        }
      },
      "Next": "Member Insights - Metrics",
      "ResultPath": null
    },
    "Member Insights - Metrics": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "ecommerce-metric-worker-{{suffix}}",
        "Payload": {
          "id": "event-id",
          "detail-type": "Scheduled Event",
          "source": "aws.events",
          "account": "************",
          "time": "2023-11-15T00:00:00Z",
          "region": "us-east-1",
          "resources": [
            "arn:aws:events:us-west-2:************:rule/my-schedule"
          ],
          "detail": {
            "orgId.$": "$.organizationId"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        }
      ],
      "ResultPath": null,
      "Next": "Historical Data - Mark Complete"
    },
    "Historical Data - Mark Complete": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "onboard-postgres-{{suffix}}:$LATEST",
        "Payload": {
          "query": "UPDATE Organization set historicaldatacomplete = true WHERE id = $1",
          "values": {
            "0.$": "$.organizationId"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        }
      ],
      "ResultPath": null,
      "Next": "Member Insights - Status - Mark Complete"
    },
    "Member Insights - Status - Mark Complete": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "onboard-postgres-{{suffix}}:$LATEST",
        "Payload": {
          "query": "INSERT INTO onboardingstate(state,timestamp,orgid,taskid) VALUES ($1,$2,$3,$4) ON CONFLICT ON CONSTRAINT unique_orgid_taskid DO UPDATE SET state=$1,timestamp=$2",
          "values": {
            "0": "\"Complete\"",
            "1": "NOW()",
            "3": "2",
            "2.$": "$.organizationId"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        }
      ],
      "ResultPath": null,
      "End": true
    }
  },
  "TimeoutSeconds": 86400
}
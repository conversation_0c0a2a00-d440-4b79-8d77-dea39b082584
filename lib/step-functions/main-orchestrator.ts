// Main orchestrator step function that calls other step functions
export const mainOrchestratorJson = {
  "Comment": "A description of my state machine",
  "StartAt": "Pass (1)",
  "States": {
    "Pass (1)": {
      "Type": "Pass",
      "Next": "Parallel Step Functions",
      "Parameters": {
        "url.$": "$.url",
        "organization_id.$": "$.organization_id",
        "organization_id_string.$": "$.organization_id_string",
        "storePassword": "{{storePasswordValue}}",
        "metrics": [],
        "useGptVision": true,
        "campaignId.$": "$.campaignId",
        "skipPersist.$": "$.skipPersist",
        "programResultS3Bucket.$": "$.programResultS3Bucket",
        "catalogResultS3Bucket.$": "$.catalogResultS3Bucket",
        "scrapeResultS3Bucket.$": "$.scrapeResultS3Bucket",
        "s3ResultKey.$": "$.s3ResultKey",
        "allowGptTemplateSuggestion.$": "$.allowGptTemplateSuggestion",
        "includeCampaignSummary.$": "$.includeCampaignSummary",
        "skipShopify.$": "$.skipShopify",
        "topLevelTableName.$": "$.topLevelTableName",
        "skipBranding.$": "$.skipBranding",
        "overwriteBranding.$": "$.overwriteBranding",
        "skipProgram.$": "$.skipProgram",
        "ignoreExistingProgram.$": "$.ignoreExistingProgram",
        "skipInsights.$": "$.skipInsights",
        "ignoreExistingInsights.$": "$.ignoreExistingInsights",
        "programSetupPrompt.$": "$.programSetupPrompt",
        "programResponsePrompt.$": "$.programResponsePrompt",
        "programUserPrompt.$": "$.programUserPrompt"
      }
    },
    "Parallel Step Functions": {
      "Type": "Parallel",
      "Branches": [
        {
          "StartAt": "Start Dependency Step Function",
          "States": {
            "Start Dependency Step Function": {
              "Type": "Task",
              "Resource": "arn:aws:states:::states:startExecution",
              "Parameters": {
                "StateMachineArn": "arn:aws:states:us-east-1:831543322268:stateMachine:DependencyStepFunction-{{suffix}}",
                "Input": {
                  "url.$": "$.url",
                  "organization_id.$": "$.organization_id",
                  "organization_id_string.$": "$.organization_id_string",
                  "storePassword.$": "$.storePassword",
                  "metrics.$": "$.metrics",
                  "useGptVision.$": "$.useGptVision",
                  "campaignId.$": "$.campaignId",
                  "skipPersist.$": "$.skipPersist",
                  "programResultS3Bucket.$": "$.programResultS3Bucket",
                  "catalogResultS3Bucket.$": "$.catalogResultS3Bucket",
                  "scrapeResultS3Bucket.$": "$.scrapeResultS3Bucket",
                  "s3ResultKey.$": "$.s3ResultKey",
                  "allowGptTemplateSuggestion.$": "$.allowGptTemplateSuggestion",
                  "includeCampaignSummary.$": "$.includeCampaignSummary",
                  "skipShopify.$": "$.skipShopify",
                  "topLevelTableName.$": "$.topLevelTableName",
                  "skipBranding.$": "$.skipBranding",
                  "overwriteBranding.$": "$.overwriteBranding",
                  "skipProgram.$": "$.skipProgram",
                  "ignoreExistingProgram.$": "$.ignoreExistingProgram",
                  "skipInsights.$": "$.skipInsights",
                  "ignoreExistingInsights.$": "$.ignoreExistingInsights",
                  "programSetupPrompt.$": "$.programSetupPrompt",
                  "programResponsePrompt.$": "$.programResponsePrompt",
                  "programUserPrompt.$": "$.programUserPrompt",
                  "AWS_STEP_FUNCTIONS_STARTED_BY_EXECUTION_ID.$": "$$.Execution.Id"
                }
              },
              "End": true
            }
          }
        },
        {
          "StartAt": "Start Member Insights Step Function",
          "States": {
            "Start Member Insights Step Function": {
              "Type": "Task",
              "Resource": "arn:aws:states:::states:startExecution",
              "Parameters": {
                "StateMachineArn": "arn:aws:states:us-east-1:831543322268:stateMachine:MemberInsightsStepFunction-{{suffix}}",
                "Input": {
                  "url.$": "$.url",
                  "organization_id.$": "$.organization_id",
                  "organization_id_string.$": "$.organization_id_string",
                  "storePassword.$": "$.storePassword",
                  "metrics.$": "$.metrics",
                  "useGptVision.$": "$.useGptVision",
                  "campaignId.$": "$.campaignId",
                  "skipPersist.$": "$.skipPersist",
                  "programResultS3Bucket.$": "$.programResultS3Bucket",
                  "catalogResultS3Bucket.$": "$.catalogResultS3Bucket",
                  "scrapeResultS3Bucket.$": "$.scrapeResultS3Bucket",
                  "s3ResultKey.$": "$.s3ResultKey",
                  "allowGptTemplateSuggestion.$": "$.allowGptTemplateSuggestion",
                  "includeCampaignSummary.$": "$.includeCampaignSummary",
                  "skipShopify.$": "$.skipShopify",
                  "topLevelTableName.$": "$.topLevelTableName",
                  "skipBranding.$": "$.skipBranding",
                  "overwriteBranding.$": "$.overwriteBranding",
                  "skipProgram.$": "$.skipProgram",
                  "ignoreExistingProgram.$": "$.ignoreExistingProgram",
                  "skipInsights.$": "$.skipInsights",
                  "ignoreExistingInsights.$": "$.ignoreExistingInsights",
                  "programSetupPrompt.$": "$.programSetupPrompt",
                  "programResponsePrompt.$": "$.programResponsePrompt",
                  "programUserPrompt.$": "$.programUserPrompt",
                  "AWS_STEP_FUNCTIONS_STARTED_BY_EXECUTION_ID.$": "$$.Execution.Id"
                }
              },
              "End": true
            }
          }
        },
        {
          "StartAt": "Start Knowledge Gathering Step Function",
          "States": {
            "Start Knowledge Gathering Step Function": {
              "Type": "Task",
              "Resource": "arn:aws:states:::states:startExecution",
              "Parameters": {
                "StateMachineArn": "arn:aws:states:us-east-1:831543322268:stateMachine:KnowledgeGatheringStepFunction-{{suffix}}",
                "Input": {
                  "url.$": "$.url",
                  "organization_id.$": "$.organization_id",
                  "organization_id_string.$": "$.organization_id_string",
                  "storePassword.$": "$.storePassword",
                  "metrics.$": "$.metrics",
                  "useGptVision.$": "$.useGptVision",
                  "campaignId.$": "$.campaignId",
                  "skipPersist.$": "$.skipPersist",
                  "programResultS3Bucket.$": "$.programResultS3Bucket",
                  "catalogResultS3Bucket.$": "$.catalogResultS3Bucket",
                  "scrapeResultS3Bucket.$": "$.scrapeResultS3Bucket",
                  "s3ResultKey.$": "$.s3ResultKey",
                  "allowGptTemplateSuggestion.$": "$.allowGptTemplateSuggestion",
                  "includeCampaignSummary.$": "$.includeCampaignSummary",
                  "skipShopify.$": "$.skipShopify",
                  "topLevelTableName.$": "$.topLevelTableName",
                  "skipBranding.$": "$.skipBranding",
                  "overwriteBranding.$": "$.overwriteBranding",
                  "skipProgram.$": "$.skipProgram",
                  "ignoreExistingProgram.$": "$.ignoreExistingProgram",
                  "skipInsights.$": "$.skipInsights",
                  "ignoreExistingInsights.$": "$.ignoreExistingInsights",
                  "programSetupPrompt.$": "$.programSetupPrompt",
                  "programResponsePrompt.$": "$.programResponsePrompt",
                  "programUserPrompt.$": "$.programUserPrompt",
                  "AWS_STEP_FUNCTIONS_STARTED_BY_EXECUTION_ID.$": "$$.Execution.Id"
                }
              },
              "End": true
            }
          }
        }
      ],
      "End": true
    }
  }
};

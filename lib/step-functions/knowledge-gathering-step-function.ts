// Knowledge Gathering step function that handles website QA tasks
export const knowledgeGatheringStepFunctionJson = {
  "Comment": "Step function to handle website knowledge gathering tasks",
  "StartAt": "Check If Task Is Already Complete",
  "States": {
    "Check If Task Is Already Complete": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST",
        "Payload": {
          "query": "SELECT state FROM onboardingstate WHERE orgid = $1 AND taskid = $2",
          "values": {
            "1": "4",
            "0.$": "$.organization_id"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        }
      ],
      "Next": "Is Task Complete?",
      "ResultPath": "$.checkState"
    },
    "Is Task Complete?": {
      "Type": "Choice",
      "Choices": [
        {
          "Next": "Skip - Already Complete",
          "And": [
            {
              "Variable": "$.checkState.Payload[0].state",
              "IsPresent": true
            },
            {
              "Variable": "$.checkState.Payload[0].state",
              "StringEquals": "\"Complete\""
            }
          ]
        }
      ],
      "Default": "Knowledge Gathering - Mark Start"
    },
    "Skip - Already Complete": {
      "Type": "Pass",
      "Comment": "Task already marked complete. Skipping all steps.",
      "End": true
    },
    "Knowledge Gathering - Mark Start": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST",
        "Payload": {
          "query": "INSERT INTO onboardingstate(state,timestamp,orgid,taskid) VALUES ($1,$2,$3,$4) ON CONFLICT ON CONSTRAINT unique_orgid_taskid DO UPDATE SET state=$1,timestamp=$2",
          "values": {
            "0": "\"Started\"",
            "1": "NOW()",
            "3": "4",
            "2.$": "$.organization_id"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        }
      ],
      "Next": "Fast Track and Enhancement Parallel",
      "ResultPath": null
    },
    "Fast Track and Enhancement Parallel": {
      "Type": "Parallel",
      "Branches": [
        {
          "StartAt": "Fast Track - Stringify QA Parameters",
          "States": {
            "Fast Track - Stringify QA Parameters": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-json-stringifier-{{suffix}}:$LATEST",
                "Payload": {
                  "json": {
                    "url.$": "$.url",
                    "organization_id.$": "$.organization_id",
                    "storePassword.$": "$.storePassword",
                    "phase": "fast",
                    "question": "Examine this website and evaluate its brand voice, positioning, products and customers. Write me a succinct 3-5 sentence summary that I can use for future marketing and copywriting for this brand in a LLM prompt.",
                    "followUpQuestions": [
                      "What problems do the products solve for their customers? Why would a customer buy a product from this brand? Answer in paragraph form, no formatting.",
                      "Describe their brand voice specifically? What's the writing style, common phrases or language, things to avoid, etc.? Again, respond in paragraph, no formatting."
                    ]
                  }
                }
              },
              "Retry": [
                {
                  "ErrorEquals": [
                    "Lambda.ServiceException",
                    "Lambda.AWSLambdaException",
                    "Lambda.SdkClientException",
                    "Lambda.TooManyRequestsException"
                  ],
                  "IntervalSeconds": 1,
                  "MaxAttempts": 3,
                  "BackoffRate": 2
                }
              ],
              "Next": "Fast Track - Website QA",
              "ResultPath": "$.stringifiedBody"
            },
            "Fast Track - Website QA": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:website-qa-lambda-{{suffix}}:$LATEST",
                "Payload": {
                  "body.$": "$.stringifiedBody.Payload",
                  "organization_id.$": "$.organization_id"
                }
              },
              "Retry": [
                {
                  "ErrorEquals": [
                    "Lambda.ServiceException",
                    "Lambda.AWSLambdaException",
                    "Lambda.SdkClientException",
                    "Lambda.TooManyRequestsException"
                  ],
                  "IntervalSeconds": 1,
                  "MaxAttempts": 3,
                  "BackoffRate": 2
                }
              ],
              "Next": "Fast Track - Parse QA Results",
              "ResultPath": "$.websiteQaResult"
            },
            "Fast Track - Parse QA Results": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-json-parser-{{suffix}}:$LATEST",
                "Payload": {
                  "jsonContainer.$": "$.websiteQaResult.Payload",
                  "jsonContainerKey": "body"
                }
              },
              "Retry": [
                {
                  "ErrorEquals": [
                    "Lambda.ServiceException",
                    "Lambda.AWSLambdaException",
                    "Lambda.SdkClientException",
                    "Lambda.TooManyRequestsException"
                  ],
                  "IntervalSeconds": 1,
                  "MaxAttempts": 3,
                  "BackoffRate": 2
                }
              ],
              "Next": "Fast Track - Persist to Organization Table",
              "ResultPath": "$.parsedQaResult"
            },
            "Fast Track - Persist to Organization Table": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST",
                "Payload": {
                  "query": "UPDATE organization SET description=$1, samplelanguage=$2 WHERE id=$3",
                  "values": {
                    "0.$": "$.parsedQaResult.Payload.answer",
                    "1.$": "$.parsedQaResult.Payload.followUpResults[1].answer",
                    "2.$": "$.organization_id"
                  }
                }
              },
              "Retry": [
                {
                  "ErrorEquals": [
                    "Lambda.ServiceException",
                    "Lambda.AWSLambdaException",
                    "Lambda.SdkClientException",
                    "Lambda.TooManyRequestsException"
                  ],
                  "IntervalSeconds": 1,
                  "MaxAttempts": 3,
                  "BackoffRate": 2
                }
              ],
              "Next": "Fast Track - Persist Customer Problems",
              "ResultPath": null
            },
            "Fast Track - Persist Customer Problems": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST",
                "Payload": {
                  "query": "INSERT INTO organizationsettings(organizationid, key, value) VALUES ($1, $2, $3) ON CONFLICT (organizationid, key) DO UPDATE SET value=EXCLUDED.value",
                  "values": {
                    "0.$": "$.organization_id",
                    "1": "customerProblems",
                    "2.$": "$.parsedQaResult.Payload.followUpResults[0].answer"
                  }
                }
              },
              "Retry": [
                {
                  "ErrorEquals": [
                    "Lambda.ServiceException",
                    "Lambda.AWSLambdaException",
                    "Lambda.SdkClientException",
                    "Lambda.TooManyRequestsException"
                  ],
                  "IntervalSeconds": 1,
                  "MaxAttempts": 3,
                  "BackoffRate": 2
                }
              ],
              "Next": "Fast Track - Mark Fast Complete",
              "ResultPath": null
            },
            "Fast Track - Mark Fast Complete": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST",
                "Payload": {
                  "query": "INSERT INTO onboardingstate(state,timestamp,orgid,taskid) VALUES ($1,$2,$3,$4) ON CONFLICT ON CONSTRAINT unique_orgid_taskid DO UPDATE SET state=$1,timestamp=$2",
                  "values": {
                    "0": "\"FastComplete\"",
                    "1": "NOW()",
                    "3": "4",
                    "2.$": "$.organization_id"
                  }
                }
              },
              "Retry": [
                {
                  "ErrorEquals": [
                    "Lambda.ServiceException",
                    "Lambda.AWSLambdaException",
                    "Lambda.SdkClientException",
                    "Lambda.TooManyRequestsException"
                  ],
                  "IntervalSeconds": 1,
                  "MaxAttempts": 3,
                  "BackoffRate": 2
                }
              ],
              "End": true,
              "ResultPath": null
            }
          }
        },
        {
          "StartAt": "Enhancement - Wait for Fast Track",
          "States": {
            "Enhancement - Wait for Fast Track": {
              "Type": "Wait",
              "Seconds": 3,
              "Comment": "Brief delay to let fast track complete first",
              "Next": "Enhancement - Stringify QA Parameters"
            },
            "Enhancement - Stringify QA Parameters": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-json-stringifier-{{suffix}}:$LATEST",
                "Payload": {
                  "json": {
                    "url.$": "$.url",
                    "organization_id.$": "$.organization_id",
                    "storePassword.$": "$.storePassword",
                    "phase": "enhanced",
                    "question": "Examine this website and evaluate its brand voice, positioning, products and customers. Write me a succinct 3-5 sentence summary that I can use for future marketing and copywriting for this brand in a LLM prompt.",
                    "followUpQuestions": [
                      "What problems do the products solve for their customers? Why would a customer buy a product from this brand? Answer in paragraph form, no formatting.",
                      "Describe their brand voice specifically? What's the writing style, common phrases or language, things to avoid, etc.? Again, respond in paragraph, no formatting."
                    ]
                  }
                }
              },
              "Retry": [
                {
                  "ErrorEquals": [
                    "Lambda.ServiceException",
                    "Lambda.AWSLambdaException",
                    "Lambda.SdkClientException",
                    "Lambda.TooManyRequestsException"
                  ],
                  "IntervalSeconds": 1,
                  "MaxAttempts": 3,
                  "BackoffRate": 2
                }
              ],
              "Next": "Enhancement - Website QA",
              "ResultPath": "$.stringifiedBody"
            },
            "Enhancement - Website QA": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:website-qa-lambda-{{suffix}}:$LATEST",
                "Payload": {
                  "body.$": "$.stringifiedBody.Payload",
                  "organization_id.$": "$.organization_id"
                }
              },
              "Retry": [
                {
                  "ErrorEquals": [
                    "Lambda.ServiceException",
                    "Lambda.AWSLambdaException",
                    "Lambda.SdkClientException",
                    "Lambda.TooManyRequestsException"
                  ],
                  "IntervalSeconds": 1,
                  "MaxAttempts": 3,
                  "BackoffRate": 2
                }
              ],
              "Next": "Enhancement - Parse QA Results",
              "ResultPath": "$.websiteQaResult"
            },
            "Enhancement - Parse QA Results": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-json-parser-{{suffix}}:$LATEST",
                "Payload": {
                  "jsonContainer.$": "$.websiteQaResult.Payload",
                  "jsonContainerKey": "body"
                }
              },
              "Retry": [
                {
                  "ErrorEquals": [
                    "Lambda.ServiceException",
                    "Lambda.AWSLambdaException",
                    "Lambda.SdkClientException",
                    "Lambda.TooManyRequestsException"
                  ],
                  "IntervalSeconds": 1,
                  "MaxAttempts": 3,
                  "BackoffRate": 2
                }
              ],
              "Next": "Enhancement - Update Organization Table",
              "ResultPath": "$.parsedQaResult"
            },
            "Enhancement - Update Organization Table": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST",
                "Payload": {
                  "query": "UPDATE organization SET description=$1, samplelanguage=$2 WHERE id=$3",
                  "values": {
                    "0.$": "$.parsedQaResult.Payload.answer",
                    "1.$": "$.parsedQaResult.Payload.followUpResults[1].answer",
                    "2.$": "$.organization_id"
                  }
                }
              },
              "Retry": [
                {
                  "ErrorEquals": [
                    "Lambda.ServiceException",
                    "Lambda.AWSLambdaException",
                    "Lambda.SdkClientException",
                    "Lambda.TooManyRequestsException"
                  ],
                  "IntervalSeconds": 1,
                  "MaxAttempts": 3,
                  "BackoffRate": 2
                }
              ],
              "Next": "Enhancement - Update Customer Problems",
              "ResultPath": null
            },
            "Enhancement - Update Customer Problems": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST",
                "Payload": {
                  "query": "UPDATE organizationsettings SET value=$1 WHERE organizationid=$2 AND key=$3",
                  "values": {
                    "0.$": "$.parsedQaResult.Payload.followUpResults[0].answer",
                    "1.$": "$.organization_id",
                    "2": "customerProblems"
                  }
                }
              },
              "Retry": [
                {
                  "ErrorEquals": [
                    "Lambda.ServiceException",
                    "Lambda.AWSLambdaException",
                    "Lambda.SdkClientException",
                    "Lambda.TooManyRequestsException"
                  ],
                  "IntervalSeconds": 1,
                  "MaxAttempts": 3,
                  "BackoffRate": 2
                }
              ],
              "End": true,
              "ResultPath": null
            }
          }
        }
      ],
      "Next": "Knowledge Gathering - Mark Complete"
    },
    "Knowledge Gathering - Mark Complete": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST",
        "Payload": {
          "query": "INSERT INTO onboardingstate(state,timestamp,orgid,taskid) VALUES ($1,$2,$3,$4) ON CONFLICT ON CONSTRAINT unique_orgid_taskid DO UPDATE SET state=$1,timestamp=$2",
          "values": {
            "0": "\"Complete\"",
            "1": "NOW()",
            "3": "4",
            "2.$": "$[0].organization_id"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        }
      ],
      "End": true,
      "ResultPath": null
    }
  }
};

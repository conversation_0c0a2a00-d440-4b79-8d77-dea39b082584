// Branding step function that handles branding-specific tasks
export const brandingStepFunctionJson = {
  "Comment": "Step function to handle branding tasks",
  "StartAt": "Branding - Detect Existing (1)",
  "States": {
    "Branding - Detect Existing (1)": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST",
        "Payload": {
          "query": "SELECT COUNT(branding) FROM organization WHERE id=$1 AND branding IS NOT NULL AND branding NOT LIKE '%isDefaultTemplate\": true%' AND branding NOT LIKE '%isDefaultTemplate\":true%'",
          "values": {
            "0.$": "$.organization_id"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        }
      ],
      "Next": "Choice (2)",
      "ResultPath": "$.existingBranding"
    },
    "Choice (2)": {
      "Type": "Choice",
      "Choices": [
        {
          "Or": [
            {
              "Variable": "$.skipBranding",
              "StringEquals": "true"
            },
            {
              "And": [
                {
                  "Not": {
                    "Variable": "$.existingBranding.Payload[0].count",
                    "StringEquals": "0"
                  }
                },
                {
                  "Not": {
                    "Variable": "$.overwriteBranding",
                    "StringEquals": "true"
                  }
                }
              ]
            }
          ],
          "Next": "Branding - Status - Mark Complete (2)"
        }
      ],
      "Default": "Branding - Generate GPT Prompt"
    },
    "Branding - Status - Mark Complete (2)": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST",
        "Payload": {
          "query": "INSERT INTO onboardingstate(state,timestamp,orgid,taskid) VALUES ($1,$2,$3,$4) ON CONFLICT ON CONSTRAINT unique_orgid_taskid DO UPDATE SET state=$1,timestamp=$2",
          "values": {
            "0": "\"Complete\"",
            "1": "NOW()",
            "3": "1",
            "2.$": "$.organization_id"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        }
      ],
      "ResultPath": null,
      "End": true
    },
    "Branding - Generate GPT Prompt": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "OutputPath": "$.Payload",
      "Parameters": {
        "Payload.$": "$",
        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-gpt-vision-prompt-generator-{{suffix}}:$LATEST"
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        }
      ],
      "Next": "Pass (3)"
    },
    "Pass (3)": {
      "Type": "Pass",
      "Next": "Branding - GPT Vision",
      "Parameters": {
        "url.$": "$.url",
        "organization_id.$": "$.organization_id",
        "metrics": [],
        "useGptVision": true,
        "overwriteBranding.$": "$.overwriteBranding",
        "skipShopify.$": "$.skipShopify",
        "prompt.$": "$.prompt",
        "sanitizeJson": true
      }
    },
    "Branding - GPT Vision": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-gpt-{{suffix}}:$LATEST",
        "Payload.$": "$"
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        },
        {
          "ErrorEquals": [
            "States.TaskFailed"
          ],
          "BackoffRate": 2,
          "IntervalSeconds": 10,
          "MaxAttempts": 12,
          "Comment": "GPT Failure"
        }
      ],
      "ResultPath": "$.gpt",
      "Next": "Branding - Detect Existing"
    },
    "Branding - Detect Existing": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST",
        "Payload": {
          "query": "SELECT COUNT(branding) FROM organization WHERE id=$1 AND branding IS NOT NULL AND branding NOT LIKE '%isDefaultTemplate\": true%' AND branding NOT LIKE '%isDefaultTemplate\":true%'",
          "values": {
            "0.$": "$.organization_id"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        }
      ],
      "Next": "Choice (1)",
      "ResultPath": "$.existingBranding2"
    },
    "Choice (1)": {
      "Type": "Choice",
      "Choices": [
        {
          "And": [
            {
              "Not": {
                "Variable": "$.existingBranding2.Payload[0].count",
                "StringEquals": "0"
              }
            },
            {
              "Not": {
                "Variable": "$.overwriteBranding",
                "StringEquals": "true"
              }
            }
          ],
          "Next": "Branding - Status - Mark Complete"
        }
      ],
      "Default": "Pass (4)"
    },
    "Branding - Status - Mark Complete": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST",
        "Payload": {
          "query": "INSERT INTO onboardingstate(state,timestamp,orgid,taskid) VALUES ($1,$2,$3,$4) ON CONFLICT ON CONSTRAINT unique_orgid_taskid DO UPDATE SET state=$1,timestamp=$2",
          "values": {
            "0": "\"Complete\"",
            "1": "NOW()",
            "3": "1",
            "2.$": "$.organization_id"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        }
      ],
      "ResultPath": null,
      "End": true
    },
    "Pass (4)": {
      "Type": "Pass",
      "Next": "Branding - Extract JSON",
      "Parameters": {
        "jsonContainer.$": "$.gpt.Payload",
        "organization_id.$": "$.organization_id"
      }
    },
    "Branding - Extract JSON": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "Payload.$": "$",
        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-json-extractor-{{suffix}}:$LATEST"
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        }
      ],
      "Next": "Update Branding Hero Image",
      "ResultPath": "$.json"
    },
    "Update Branding Hero Image": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "Payload.$": "$",
        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-branding-post-processor-{{suffix}}:$LATEST"
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        }
      ],
      "Next": "Branding - Persist Results",
      "ResultPath": "$.json"
    },
    "Branding - Persist Results": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST",
        "Payload": {
          "query": "UPDATE organization SET branding=$1 WHERE id=$2",
          "values": {
            "0.$": "$.json.Payload",
            "1.$": "$.organization_id"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        }
      ],
      "ResultPath": null,
      "Next": "Branding - Status - Mark Complete (1)"
    },
    "Branding - Status - Mark Complete (1)": {
      "Type": "Task",
      "Resource": "arn:aws:states:::lambda:invoke",
      "Parameters": {
        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-{{suffix}}:$LATEST",
        "Payload": {
          "query": "INSERT INTO onboardingstate(state,timestamp,orgid,taskid) VALUES ($1,$2,$3,$4) ON CONFLICT ON CONSTRAINT unique_orgid_taskid DO UPDATE SET state=$1,timestamp=$2",
          "values": {
            "0": "\"Complete\"",
            "1": "NOW()",
            "3": "1",
            "2.$": "$.organization_id"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": [
            "Lambda.ServiceException",
            "Lambda.AWSLambdaException",
            "Lambda.SdkClientException",
            "Lambda.TooManyRequestsException"
          ],
          "IntervalSeconds": 1,
          "MaxAttempts": 3,
          "BackoffRate": 2
        }
      ],
      "ResultPath": null,
      "End": true
    }
  }
};

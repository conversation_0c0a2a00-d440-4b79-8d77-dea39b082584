// Updated step function import file that replaces the existing one
import { mainOrchestratorJson } from './step-functions/main-orchestrator';
import { dependencyStepFunctionJson } from './step-functions/dependency-step-function';
import { brandingStepFunctionJson } from './step-functions/branding-step-function';
import { programStepFunctionJson } from './step-functions/program-step-function';
import { memberInsightsStepFunctionJson } from './step-functions/member-insights-step-function';
import { knowledgeGatheringStepFunctionJson } from './step-functions/knowledge-gathering-step-function';

// Export all step functions for use in the infrastructure stack
export const json = mainOrchestratorJson;
export const dependencyJson = dependencyStepFunctionJson;
export const brandingJson = brandingStepFunctionJson;
export const programJson = programStepFunctionJson;
export const memberInsightsJson = memberInsightsStepFunctionJson;
export const knowledgeGatheringJson = knowledgeGatheringStepFunctionJson;

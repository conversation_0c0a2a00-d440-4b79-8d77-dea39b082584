import { Stack, StackProps, Duration, RemovalPolicy, } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { PolicyStatement } from "aws-cdk-lib/aws-iam";
import { NodejsFunction } from 'aws-cdk-lib/aws-lambda-nodejs';
import { Runtime } from 'aws-cdk-lib/aws-lambda';
import { RetentionDays } from 'aws-cdk-lib/aws-logs';
import path = require('path');
import { getSuffix } from './utils';
import { Queue } from 'aws-cdk-lib/aws-sqs';
import { SqsEventSource } from 'aws-cdk-lib/aws-lambda-event-sources';
import { IVpc, Subnet, SecurityGroup, Vpc, SubnetType } from 'aws-cdk-lib/aws-ec2';
import * as Utils from './utils';
import { AttributeType, BillingMode, Table } from 'aws-cdk-lib/aws-dynamodb';
import { EventBridge } from 'aws-sdk';
import { Rule, Schedule } from 'aws-cdk-lib/aws-events';
import { LambdaFunction } from 'aws-cdk-lib/aws-events-targets';
import { WEBAPP_API_URL } from '../src/events/webhook-worker';

interface ShopifySendlaneSyncStackProps extends StackProps {
	deploy_environment_name: string;
}

export class ShopifySendlaneSyncStack extends Stack {

	private vpc: IVpc;
	private isDev: boolean;

	constructor(scope: Construct, id: string, private props?: ShopifySendlaneSyncStackProps) {
		super(scope, id, props);
		this.isDev = Utils.isDev(props!.deploy_environment_name);

		this.vpc = Vpc.fromLookup(
			this,
			`raleon-vpc-shopify-${getSuffix(this)}`,
			{ vpcName: "raleon-vpc" }
		);

		const sendlaneSyncLambda = new NodejsFunction(this, `LoyaltySendlaneSyncLambda-${getSuffix(this)}`, {
			functionName: `loyalty-sendlane-sync-${getSuffix(this)}`,
			runtime: Runtime.NODEJS_18_X,
			handler: 'main',
			entry: path.join(__dirname, `../src/notifications/loyalty-sendlane-sync.ts`),
			environment: {
				WEBAPP_API_URL: `http://${this.isDev ? this.node.tryGetContext('internal-dev-webapp-domain') : this.node.tryGetContext('internal-webapp-domain')}/api/v1`,
			},
			logRetention: RetentionDays.THREE_DAYS,
			memorySize: 1024,
			timeout: Duration.minutes(15),
			vpc: this.vpc,
			vpcSubnets: { 
				subnets: [Subnet.fromSubnetId(this, "raleon-subnet-shopify", "subnet-04ef7beb8a56268d1"),], 
			}, 
			securityGroups: [SecurityGroup.fromLookupByName(this, "raleon-sg-shopify", "default", this.vpc),],
		});


		// sendlaneSyncLambda.addToRolePolicy(new PolicyStatement({
		// 	actions: ['secretsmanager:GetSecretValue'],
		// 	resources: ['*'],
		// }));

		let sendlaneSyncSchedule = new Rule(this, `sendlane-sync-schedule-${getSuffix(this)}`, {
			schedule: Schedule.cron({
				day: '*',
				hour: '6',
				minute: '0',
			}),
		});

		sendlaneSyncSchedule.addTarget(new LambdaFunction(sendlaneSyncLambda));
	}
}

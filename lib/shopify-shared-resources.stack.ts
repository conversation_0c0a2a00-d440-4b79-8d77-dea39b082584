import { Duration, Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { getSuffix, isDev } from './utils';
import path = require('path');
import { Bucket } from 'aws-cdk-lib/aws-s3';
import { ISecret} from "aws-cdk-lib/aws-secretsmanager";
import * as sqs from "aws-cdk-lib/aws-sqs";

export interface ShopifySharedStackProps extends StackProps {
    deploy_environment_name: string;
}

export class ShopifySharedStack extends Stack {
    raleonAppDbSecret: ISecret;
    customerioSecret: ISecret;
    hubspotSecret: ISecret;
    metricQueue: sqs.Queue;
    isDev: boolean;
    segmentWriterQueue: sqs.Queue;

    constructor(scope: Construct, id: string, private props?: ShopifySharedStackProps) {
        super(scope, id, props);
        this.isDev = isDev(props!.deploy_environment_name);
        const dataDeadletter = new sqs.Queue(
            this,
            `data-deadletter2-${getSuffix(this)}`,
            {
                queueName: `metric-deadletter2-queue-${getSuffix(this)}`,
                retentionPeriod: Duration.days(4),
            }
        );
        this.metricQueue = new sqs.Queue(this, `data2-${getSuffix(this)}`, {
            queueName: `metric-queue2-${getSuffix(this)}`,
            deadLetterQueue: {
                queue: dataDeadletter,
                maxReceiveCount: 3,
            },
            visibilityTimeout: Duration.seconds(900),
        });
    }

}
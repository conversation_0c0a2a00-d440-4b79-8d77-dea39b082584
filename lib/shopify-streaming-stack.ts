import { Duration, Stack, StackProps } from 'aws-cdk-lib';
import * as cdk from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { NodejsFunction } from 'aws-cdk-lib/aws-lambda-nodejs';
import { Runtime } from 'aws-cdk-lib/aws-lambda';
import * as path from "path";
import { getSuffix, isDev } from './utils';
import { RetentionDays } from 'aws-cdk-lib/aws-logs';
import { Vpc, Subnet, SecurityGroup } from 'aws-cdk-lib/aws-ec2';
import { Secret } from 'aws-cdk-lib/aws-secretsmanager';
import { PolicyStatement, Policy } from 'aws-cdk-lib/aws-iam';
import { Bucket } from 'aws-cdk-lib/aws-s3';
import { Queue } from 'aws-cdk-lib/aws-sqs';
import * as iam from 'aws-cdk-lib/aws-iam';
import { SqsEventSource } from 'aws-cdk-lib/aws-lambda-event-sources';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';

interface ShopifyStreamingStackProps extends StackProps {
	deploy_environment_name: string;
}

export class ShopifyStreamingStack extends Stack {
	curatedBucket: Bucket;
	private isDev: boolean;
	private streamingGateway: apigateway.RestApi;
	private streamingLambda: NodejsFunction;
	private eventQueue: Queue;

	constructor(scope: Construct, id: string, private props?: ShopifyStreamingStackProps) {
		super(scope, id, props);
		this.isDev = isDev(props!.deploy_environment_name);

		const eventDeadletterQueue = new Queue(this, `shopify-streaming-deadletter-queue-${getSuffix(this)}`, {
			queueName: `shopify-streaming-deadletter-queue-${getSuffix(this)}`,
			retentionPeriod: Duration.days(4),
		});

		this.eventQueue = new Queue(this, 'StreamingQueue', {
			deadLetterQueue: {
				queue: eventDeadletterQueue,
				maxReceiveCount: 3,
			},
			queueName: `shopify-streaming-queue-${getSuffix(this)}`,
			visibilityTimeout: Duration.seconds(600) // Set visibility timeout to match or exceed Lambda timeout
		});


		this.curatedBucket = new Bucket(this, `Curated-loyalty-events-${getSuffix(this)}`, {
			bucketName: `loyalty-events-curated-${getSuffix(this)}`,
			removalPolicy: cdk.RemovalPolicy.RETAIN,
			autoDeleteObjects: false,
		});

		this.createAPIGateway();
		this.createLambda();
	}

	private createAPIGateway() {
		this.streamingGateway = new apigateway.RestApi(this, `ShopifyStreaming-${getSuffix(this)}`, {
			restApiName: `shopify-streaming-${getSuffix(this)}`,
			description: 'This service serves as a streaming events endpoint.',
		});

		const eventResource = this.streamingGateway.root.addResource('event');

		const credentialsRole = new iam.Role(this, "Role", {
			assumedBy: new iam.ServicePrincipal("apigateway.amazonaws.com"),
		});

		credentialsRole.attachInlinePolicy(
			new iam.Policy(this, "SendMessagePolicy", {
				statements: [
					new iam.PolicyStatement({
						actions: ["sqs:SendMessage"],
						effect: iam.Effect.ALLOW,
						resources: [this.eventQueue.queueArn],
					}),
				],
			})
		);

		eventResource.addMethod(
			"POST",
			new apigateway.AwsIntegration({
				service: "sqs",
				path: `${cdk.Aws.ACCOUNT_ID}/${this.eventQueue.queueName}`,
				integrationHttpMethod: "POST",
				options: {
					credentialsRole,
					passthroughBehavior: apigateway.PassthroughBehavior.NEVER,
					requestParameters: {
						"integration.request.header.Content-Type": `'application/x-www-form-urlencoded'`,
					},
					requestTemplates: {
						"application/json": `Action=SendMessage&MessageBody=$util.urlEncode($input.body)`,
					},
					integrationResponses: [
						{
							statusCode: "200",
							responseTemplates: {
								"application/json": `{"done": true}`,
							},
						},
					],
				},
			}),
			{ methodResponses: [{ statusCode: "200" }] }
		);
	}

	private createLambda() {

		let athenaPermission = new PolicyStatement({
			actions: [
				"athena:*",
				"glue:*",
				"dynamodb:DescribeTable",
				"dynamodb:ListSchemas",
				"dynamodb:ListStreams",
				"dynamodb:ListTables",
				"dynamodb:Query",
				"dynamodb:Scan",
				"lambda:*",
				"s3:*",
			],
			resources: ["*"],
		});

		let athenaPolicy = new Policy(
			this,
			`metrics-athena-access-policy-${getSuffix(this)}`,
			{
				statements: [athenaPermission],
			}
		);
		const dbSecretArn = this.node.tryGetContext(
			getSuffix(this) == 'prod'
				? 'shopify-db-secret-arn'
				: 'shopify-db-dev-secret-arn');

		const appSecretArn = this.node.tryGetContext(
			getSuffix(this) == 'prod'
				? 'shopify-app-secret-arn'
				: 'shopify-app-dev-secret-arn'
		);
		const dbSecret = Secret.fromSecretCompleteArn(
			this,
			`ShopifyDBSecret-${getSuffix(this)}`,
			dbSecretArn,
		);
		const appSecret = Secret.fromSecretCompleteArn(
			this,
			`ShopifyAppSecret-${getSuffix(this)}`,
			appSecretArn,
		);
		const raleonAppDbSecret = Secret.fromSecretNameV2(
			this,
			`raleonwebapp-id-${getSuffix(this)}`,
			this.isDev ? 'raleonwebapp' : 'raleonwebapp-prod'
		);
		const orgIntegrationKeysSecret = Secret.fromSecretNameV2(
			this,
			`org-integration-keys-${getSuffix(this)}`,
			this.isDev ? 'organization_keys_dev' : 'organization_keys_prod',
		);

		const vpc = Vpc.fromLookup(
			this,
			`raleon-vpc-shopify-${getSuffix(this)}`,
			{ vpcName: "raleon-vpc" },
		);

		this.streamingLambda = new NodejsFunction(this, `shopify-streaming-${getSuffix(this)}`, {
			functionName: `shopify-streaming-${getSuffix(this)}`,
			memorySize: 512,
			timeout: Duration.minutes(10),
			runtime: Runtime.NODEJS_18_X,
			handler: 'main',
			logRetention: RetentionDays.THREE_DAYS,
			logRetentionRetryOptions: {
				base: Duration.seconds(10),
				maxRetries: 100
			},
			entry: path.join(__dirname, '/../src/streaming/streaming-event-handler.ts'),
			environment: {
				SECRET_ARN: dbSecret.secretArn,
				SHOPIFY_SECRET_KEY_ARN: appSecret.secretArn,
				RALEON_DB: raleonAppDbSecret.secretArn,
				ORG_INTEGRATION_KEYS_SECRET_ARN: orgIntegrationKeysSecret.secretArn,
				CURATED_BUCKET: this.curatedBucket.bucketName || "",
				ATHENA_DB: this.isDev ? 'loyalty_events_dev' : 'loyalty_events',
				LOYALTY_EMAIL_QUEUE_URL: `https://sqs.${this.props!.env!.region}.amazonaws.com/${this.props!.env!.account}/loyalty-email-queue-${getSuffix(this)}`,
			},
			vpc,
			vpcSubnets: {
				subnets: [
					Subnet.fromSubnetId(
						this,
						`raleon-subnet-shopify-streaming-${getSuffix(this)}`,
						"subnet-04ef7beb8a56268d1"
					),
				],
			},
			securityGroups: [
				SecurityGroup.fromLookupByName(
					this,
					"raleon-sg-metric-worker",
					"default",
					vpc
				),
			],
		});
		if (this.streamingLambda.role && this.props && athenaPolicy) {
			athenaPolicy.addStatements(new PolicyStatement({
				//allow publishing message to queue
				actions: ["sqs:SendMessage"],
				resources: [
					`arn:aws:sqs:${this.props.env!.region}:${this.props.env!.account}:loyalty-email-queue-${getSuffix(this)}`,
				],
			}))
			this.streamingLambda.role.attachInlinePolicy(athenaPolicy)
		}
		dbSecret.grantRead(this.streamingLambda);
		appSecret.grantRead(this.streamingLambda);
		raleonAppDbSecret.grantRead(this.streamingLambda);
		orgIntegrationKeysSecret.grantRead(this.streamingLambda);
		this.curatedBucket.grantReadWrite(this.streamingLambda);
		const sqsEventSource = new SqsEventSource(this.eventQueue, {
			batchSize: 10,
			maxBatchingWindow: Duration.seconds(5)
		});

		this.streamingLambda.addEventSource(sqsEventSource);
	}
}

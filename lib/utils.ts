import { Construct } from "constructs";
export const MAX_CONCURRENCY_LAMBDAS: number = 9;
export const MAX_CONCURRENCY_LAMBDAS_DEV: number = 2;

export function getSuffix(stack: Construct): string {
	return stack.node.tryGetContext("suffix");
}

export function isDev(deployEnv: string) {
	return deployEnv !== "production";
}

export function hasPipeline(suffix: string) {
	return suffix == "prod" || suffix == "dev";
}

import { Duration, RemovalPolicy, Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { getSuffix, isDev,MAX_CONCURRENCY_LAMBDAS,MAX_CONCURRENCY_LAMBDAS_DEV, } from './utils';
import { DockerImageCode, DockerImageFunction } from 'aws-cdk-lib/aws-lambda';
import path = require('path');
import { Bucket } from 'aws-cdk-lib/aws-s3';
import * as iam from 'aws-cdk-lib/aws-iam';
import { Policy, PolicyStatement } from 'aws-cdk-lib/aws-iam';
import { IVpc, SecurityGroup, Subnet, Vpc } from "aws-cdk-lib/aws-ec2";
import { ISecret, Secret } from "aws-cdk-lib/aws-secretsmanager";
import * as sqs from "aws-cdk-lib/aws-sqs";
import { SqsEventSource } from "aws-cdk-lib/aws-lambda-event-sources";
import { NodejsFunction } from "aws-cdk-lib/aws-lambda-nodejs";
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as logs from "aws-cdk-lib/aws-logs";
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import { AuthorizationType, LambdaIntegration } from 'aws-cdk-lib/aws-apigateway';
import { Rule, Schedule } from 'aws-cdk-lib/aws-events';
import { LambdaFunction } from 'aws-cdk-lib/aws-events-targets';
import * as batch from 'aws-cdk-lib/aws-batch';
import { DockerImageAsset, Platform } from 'aws-cdk-lib/aws-ecr-assets';
import * as ec2 from 'aws-cdk-lib/aws-ec2';

export interface ShopifyMetricsStackProps extends StackProps {
	deploy_environment_name: string;
	athenaOutput: Bucket;
	curatedBucket: Bucket;
	metricQueue: sqs.Queue;
}

export class ShopifyMetricsStack extends Stack {
	raleonAppDbSecret: ISecret;
	customerioSecret: ISecret;
	hubspotSecret: ISecret;
	isDev: boolean;
	segmentWriterQueue: sqs.Queue;
	jobQueue: batch.CfnJobQueue;
	priorityJobQueue: batch.CfnJobQueue;

	constructor(scope: Construct, id: string, private props?: ShopifyMetricsStackProps) {
		super(scope, id, props);
		this.isDev = isDev(props!.deploy_environment_name);
		this.raleonAppDbSecret = Secret.fromSecretNameV2(
			this, 
			`raleonwebapp-id-${getSuffix(this)}`, 
			this.isDev ? 'raleonwebapp': 'raleonwebapp-prod'
		);
		this.customerioSecret = Secret.fromSecretNameV2(
			this, 
			`customerio-secret-${getSuffix(this)}`, 
			'customerio'
		);
		this.hubspotSecret = Secret.fromSecretNameV2(
			this, 
			`hubspot-secret-${getSuffix(this)}`, 
			'hubspot'
		);
		const vpc = Vpc.fromLookup(
			this,
			`raleon-vpc-shopify-${getSuffix(this)}`,
			{ vpcName: "raleon-vpc" }
		);
		let athenaPermission = new PolicyStatement({
			actions: [
				"athena:*",
				"glue:*",
				"dynamodb:DescribeTable",
				"dynamodb:ListSchemas",
				"dynamodb:ListStreams",
				"dynamodb:ListTables",
				"dynamodb:Query",
				"dynamodb:Scan",
				"lambda:*",
				"s3:*",
			],
			resources: ["*"],
		});

		const athenaReadOnlyPermission = new PolicyStatement({
			actions: [
				"athena:StartQueryExecution",
				"athena:GetQueryExecution",
				"athena:GetQueryResults",
				"athena:ListDatabases",
				"athena:ListTableMetadata",
				"athena:ListDataCatalogs",
				"athena:ListWorkGroups",
				"athena:GetWorkGroup",
				"glue:GetDatabase",
				"glue:GetTable",
				"glue:GetPartitions",
				"glue:GetPartition",
				"glue:BatchGetPartition",
			],
			resources: ["*"],
		});

		let athenaPolicy = new Policy(
			this,
			`metrics-athena-access-policy-${getSuffix(this)}`,
			{
				statements: [athenaPermission],
			}
		);

		const athenaReadOnlyPolicy = new Policy(
			this,
			`metrics-athena-readonly-policy-${getSuffix(this)}`,
			{
				statements: [athenaReadOnlyPermission],
			}
		);
		
		const metricWorker = this.buildMetricWorker(vpc, athenaPolicy);
		const dataWorker = this.buildDataQueryWorker(vpc, athenaReadOnlyPolicy);
		const api = this.createMetricProofApi(metricWorker, dataWorker);
		this.buildSegmentationInfrastucture(vpc, athenaPolicy);
		this.buildMetricIngestion(vpc, athenaPolicy);
		
	}

	private createMetricProofApi(metricWorker: NodejsFunction, dataWorker: NodejsFunction) {
		const defaultLambda = new NodejsFunction(this, 'defaultLambdaAPI', {
			functionName: `defaultLambdaAPI-${getSuffix(this)}`,
			memorySize: 1024,
			timeout: Duration.seconds(30),
			runtime: lambda.Runtime.NODEJS_18_X,
			handler: 'main',
			entry: path.join(__dirname, '/../src/metrics/default.ts'),
			logRetention: logs.RetentionDays.THREE_DAYS,
			logRetentionRetryOptions: {
				base: Duration.seconds(10),
				maxRetries: 100
			}
		});
		const api = new apigateway.LambdaRestApi(this, 'ecommerce-data', {
			restApiName: `ecommerce-data-${getSuffix(this)}`,
			handler: defaultLambda,
			proxy: false,
			deployOptions: {
				stageName: this.props!.deploy_environment_name == 'production' ? 'prod' : 'dev'
			},
			defaultCorsPreflightOptions: {
				allowHeaders: [
					'Content-Type',
					'X-Amz-Date',
					'Authorization',
					'X-Api-Key',
				],
				allowMethods: ['OPTIONS', 'GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
				allowCredentials: true,
				allowOrigins: ['*'],
			},
		});
		const metricResource = api.root.addResource('metric');
		metricResource.addMethod('GET', new apigateway.LambdaIntegration(metricWorker), {
				requestParameters: {
					'method.request.querystring.orgId': true,
					'method.request.querystring.name': true,
					'method.request.querystring.calculation': true,
					'method.request.querystring.start-date': true,
					'method.request.querystring.end-date': true,
					'method.request.querystring.group-by': true,
					'method.request.querystring.campaignId': false,
				},
				authorizationType: AuthorizationType.IAM,
			});

		const dataResource = api.root.addResource('data');
		dataResource.addMethod('POST', new apigateway.LambdaIntegration(dataWorker), {
				authorizationType: AuthorizationType.IAM,
		});
		return api;
	  }
	
	private buildMetricWorker(vpc: IVpc, athenaPolicy?: Policy) {
		// Security Group
		const sg = SecurityGroup.fromLookupByName(this, `metric-ingestion-batch-${getSuffix(this)}`, 'default', vpc);
		const computeEnv = new batch.CfnComputeEnvironment(this, `BatchComputeEnv-${getSuffix(this)}`, {
			type: 'MANAGED',
			computeEnvironmentName: `metric-ingestion-compute-${getSuffix(this)}`,
			computeResources: {
			  type: 'FARGATE_SPOT',
			  maxvCpus: 100,
			  subnets: vpc.privateSubnets.map(subnet => subnet.subnetId),
			  securityGroupIds: [sg.securityGroupId],
			},
			serviceRole: new iam.Role(this, `BatchServiceRole-${getSuffix(this)}`, {
			  assumedBy: new iam.ServicePrincipal('batch.amazonaws.com'),
			  managedPolicies: [
				iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSBatchServiceRole'),
			  ],
			}).roleArn,
		  });
		this.jobQueue = new batch.CfnJobQueue(this, `BatchJobQueue-${getSuffix(this)}`, {
			jobQueueName: `metric-ingestion-job-queue-${getSuffix(this)}`,
			priority: 1,
			computeEnvironmentOrder: [
			  { order: 1, computeEnvironment: computeEnv.ref }
			]
		  });

		const priorityComputeEnv = new batch.CfnComputeEnvironment(this, `PriorityBatchComputeEnv-${getSuffix(this)}`, {
			type: 'MANAGED',
			computeEnvironmentName: `priority-metric-ingestion-compute-${getSuffix(this)}`,
			computeResources: {
			  type: 'FARGATE',
			  maxvCpus: 30,
			  subnets: vpc.privateSubnets.map(subnet => subnet.subnetId),
			  securityGroupIds: [sg.securityGroupId],
			},
			serviceRole: new iam.Role(this, `PriorityBatchServiceRole-${getSuffix(this)}`, {
			  assumedBy: new iam.ServicePrincipal('batch.amazonaws.com'),
			  managedPolicies: [
				iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSBatchServiceRole'),
			  ],
			}).roleArn,
		  });
		this.priorityJobQueue = new batch.CfnJobQueue(this, `PriorityBatchJobQueue-${getSuffix(this)}`, {
			jobQueueName: `priority-metric-ingestion-job-queue-${getSuffix(this)}`,
			priority: 10,
			computeEnvironmentOrder: [
			  { order: 1, computeEnvironment: priorityComputeEnv.ref }
			]
		  });

		let metricWorker = new NodejsFunction(
			this,
			`ecommerce-metric-worker-${getSuffix(this)}`,
			{
				functionName: `ecommerce-metric-worker-${getSuffix(this)}`,
				memorySize: 1024,
				timeout: Duration.seconds(120),
				runtime: lambda.Runtime.NODEJS_18_X,
				handler: "handler",
				logRetention: logs.RetentionDays.THREE_DAYS,
				entry: path.join(__dirname, "/../src/metrics/metric-worker.ts"),
				environment: {
					ATHENA_OUTPUT_BUCKET: `s3://${this.props?.athenaOutput.bucketName}` || "",
					METRIC_QUEUE_URL: this.props!.metricQueue.queueUrl,
					RALEON_DB: this.raleonAppDbSecret.secretArn,
					ATHENA_DB: this.isDev ? 'ecommerce_dev' : 'ecommerce',
					BATCH_JOB_QUEUE: this.jobQueue.ref,
					BATCH_JOB_DEFINITION: `metric-ingestion-jobdef-${getSuffix(this)}`,
					PRIORITY_BATCH_JOB_QUEUE: this.priorityJobQueue.ref,
					PRIORITY_BATCH_JOB_DEFINITION: `priority-metric-ingestion-jobdef-${getSuffix(this)}`,
				},
				vpc,
				vpcSubnets: {
					subnets: [
						Subnet.fromSubnetId(
							this,
							"raleon-subnet-metric-worker",
							"subnet-04ef7beb8a56268d1"
						),
					],
				},
				securityGroups: [
					SecurityGroup.fromLookupByName(
						this,
						"raleon-sg-metric-worker",
						"default",
						vpc
					),
				],
			}
		);

		if (!this.isDev) {
			const rule = new Rule(this, `metric-worker-schedule-${getSuffix(this)}`, {
				ruleName: `metric-worker-schedule-${getSuffix(this)}`,
				schedule: Schedule.cron({ minute: '0', hour: '6'}),
			});
	
			rule.addTarget(new LambdaFunction(metricWorker));
		}
		
		if (metricWorker.role && this.props && athenaPolicy) {
			metricWorker.role.attachInlinePolicy(athenaPolicy);
		}
		this.props?.curatedBucket.grantReadWrite(metricWorker);
		this.raleonAppDbSecret.grantRead(metricWorker);
		this.props?.metricQueue.grantSendMessages(metricWorker);
		metricWorker.addToRolePolicy(new PolicyStatement({
				effect: iam.Effect.ALLOW,
				actions: [
						'batch:SubmitJob',
						'batch:DescribeJobs',
						'batch:ListJobs'
				],
				resources: ['*']
		}));
		return metricWorker;
	}

	private buildDataQueryWorker(vpc: IVpc, athenaPolicy?: Policy) {
		const dataWorker = new NodejsFunction(
			this,
			`athena-query-worker-${getSuffix(this)}`,
			{
				functionName: `athena-query-worker-${getSuffix(this)}`,
				memorySize: 1024,
				timeout: Duration.seconds(120),
				runtime: lambda.Runtime.NODEJS_18_X,
				handler: 'handler',
				logRetention: logs.RetentionDays.THREE_DAYS,
				entry: path.join(__dirname, '/../src/data/data-query-worker.ts'),
				environment: {
					ATHENA_OUTPUT_BUCKET: `s3://${this.props?.athenaOutput.bucketName}` || '',
					ATHENA_DB: this.isDev ? 'ecommerce_dev' : 'ecommerce',
				},
				vpc,
				vpcSubnets: {
					subnets: [
						Subnet.fromSubnetId(
								this,
								'raleon-subnet-athena-query-worker',
								'subnet-04ef7beb8a56268d1'
						),
					],
				},
				securityGroups: [
						SecurityGroup.fromLookupByName(
								this,
								'raleon-sg-athena-query-worker',
								'default',
								vpc
						),
				],
			}
		);

		if (dataWorker.role && athenaPolicy) {
				dataWorker.role.attachInlinePolicy(athenaPolicy);
		}

		this.props?.athenaOutput.grantReadWrite(dataWorker);
		this.props?.curatedBucket.grantRead(dataWorker);

		return dataWorker;
	}

	private buildMetricIngestion(vpc: IVpc, athenaPolicy?: Policy) {
		const dbSecretArn = this.node.tryGetContext(
			getSuffix(this) == 'prod' 
			? 'shopify-db-secret-arn'
			: 'shopify-db-dev-secret-arn');
		const dbSecret = Secret.fromSecretCompleteArn(
			this,
			`ShopifyDBSecret-${getSuffix(this)}`,
			dbSecretArn,
		);
		const organizationEncryptionKeyArn = this.node.tryGetContext(
			getSuffix(this) == 'prod' 
				? 'organization-encryption-key-arn'
				: 'organization-encryption-key-dev-arn'
		);
		const orgEncryptionKeySecret = Secret.fromSecretCompleteArn(
			this,
			`OrganizationEncryptionKey-${getSuffix(this)}`,
			organizationEncryptionKeyArn,
		);
		let metricIngestion = new DockerImageFunction(this, `MetricIngestion-${getSuffix(this)}`, {
			functionName: `metric-ingestion-${getSuffix(this)}`,
			code: DockerImageCode.fromImageAsset(
				path.join(__dirname, '../src/metrics/metric-ingestion'), 
				{ buildArgs: { "--platform": "linux/amd64" } }
			),
			timeout: Duration.seconds(900),
			memorySize: 4096,
			reservedConcurrentExecutions: 15,
			environment: {
				'ATHENA_OUTPUT_BUCKET': `s3://${this.props?.athenaOutput.bucketName}` || "",
				'CURATED_BUCKET': this.props?.curatedBucket.bucketName || "",
				'SECRET_ARN': dbSecret.secretArn,
				'ORGANIZATION_ENCRYPTION_KEY': orgEncryptionKeySecret.secretArn,
				'RALEON_DB': this.raleonAppDbSecret.secretArn,
				'WEBAPP_API_URL': `http://${this.isDev ? this.node.tryGetContext('internal-dev-webapp-domain') : this.node.tryGetContext('internal-webapp-domain')}/api/v1`,
				'ATHENA_DB': this.isDev ? 'ecommerce_dev' : 'ecommerce',
				'POSGRES_DB': this.isDev ? 'postgres_test' : 'posgresreadreplicav3',
				'SEGMENT_WRITER_QUEUE_URL': this.segmentWriterQueue.queueUrl,
				'CUSTOMERIO_SECRET_ARN': this.customerioSecret.secretArn,
				'HUBSPOT_SECRET_ARN': this.hubspotSecret.secretArn,
				'METRIC_QUEUE_URL': this.props!.metricQueue.queueUrl,
				'LLM_ROUTER_LAMBDA': `llm-router-lambda-${getSuffix(this)}`,
			},
			vpc,
			vpcSubnets: {
				subnets: [
					Subnet.fromSubnetId(
						this,
						"raleon-subnet-shopify-metrics-ingestion",
						"subnet-04ef7beb8a56268d1"
					),
				],
			},
			securityGroups: [
				SecurityGroup.fromLookupByName(
					this,
					"raleon-sg-shopify-metric-ingestion",
					"default",
					vpc
				),
			],
		});

		// ECS IAM roles
		const executionRole = new iam.Role(this, `EcsExecutionRole-${getSuffix(this)}`, {
		  assumedBy: new iam.ServicePrincipal('ecs-tasks.amazonaws.com'),
		  managedPolicies: [
			iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AmazonECSTaskExecutionRolePolicy'),
		  ],
		});
	
		const jobRole = new iam.Role(this, `EcsJobRole-${getSuffix(this)}`, {
		  assumedBy: new iam.ServicePrincipal('ecs-tasks.amazonaws.com'),
		});

		
	  
		// Docker Image Asset
		const imageAsset = new DockerImageAsset(this, `MetricIngestionImage-${getSuffix(this)}`, {
		directory: path.join(__dirname, '../src/metrics/metric-ingestion'),
		platform: Platform.LINUX_AMD64,
		});
		const jobDef = new batch.CfnJobDefinition(this, `MetricIngestionJobDef-${getSuffix(this)}`, {
			type: 'container',
			containerProperties: {
			  image: imageAsset.imageUri,
			  resourceRequirements: [
				{ type: 'VCPU', value: '2' },
				{ type: 'MEMORY', value: '4096' }
			  ],
			  environment: [
				{ name: 'ATHENA_OUTPUT_BUCKET', value: `s3://${this.props?.athenaOutput.bucketName}` },
				{ name: 'CURATED_BUCKET', value: this.props?.curatedBucket.bucketName || "" },
				{ name: 'SECRET_ARN', value: dbSecret.secretArn },
				{ name: 'ORGANIZATION_ENCRYPTION_KEY', value: orgEncryptionKeySecret.secretArn },
				{ name: 'RALEON_DB', value: this.raleonAppDbSecret.secretArn },
				{ name: 'WEBAPP_API_URL', value: `http://${this.isDev ? this.node.tryGetContext('internal-dev-webapp-domain') : this.node.tryGetContext('internal-webapp-domain')}/api/v1` },
				{ name: 'ATHENA_DB', value: this.isDev ? 'ecommerce_dev' : 'ecommerce' },
				{ name: 'POSGRES_DB', value: this.isDev ? 'postgres_test' : 'posgresreadreplicav3' },
				{ name: 'SEGMENT_WRITER_QUEUE_URL', value: this.segmentWriterQueue.queueUrl },
				{ name: 'CUSTOMERIO_SECRET_ARN', value: this.customerioSecret.secretArn },
				{ name: 'HUBSPOT_SECRET_ARN', value: this.hubspotSecret.secretArn },
				{ name: 'METRIC_QUEUE_URL', value: this.props!.metricQueue.queueUrl },
				{ name: 'LLM_ROUTER_LAMBDA', value: `llm-router-lambda-${getSuffix(this)}` },
				{ name: 'BATCH_JOB_QUEUE', value: this.jobQueue.ref },
				{ name: 'BATCH_JOB_DEFINITION', value: `metric-ingestion-jobdef-${getSuffix(this)}` },
				{ name: 'PRIORITY_BATCH_JOB_QUEUE', value: this.priorityJobQueue.ref },
				{ name: 'PRIORITY_BATCH_JOB_DEFINITION', value: `priority-metric-ingestion-jobdef-${getSuffix(this)}` },
			  ],
			  executionRoleArn: executionRole.roleArn,
			  jobRoleArn: jobRole.roleArn,
			  logConfiguration: {
				logDriver: 'awslogs',
				options: {
				  'awslogs-group': `/aws/batch/metric-ingestion-${getSuffix(this)}`,
				  'awslogs-region': this.region,
				  'awslogs-stream-prefix': `metric-ingestion-${getSuffix(this)}`
				}
			  },
			  fargatePlatformConfiguration: {
				platformVersion: 'LATEST'
			  },
			  networkConfiguration: {
				assignPublicIp: 'ENABLED'  // Use 'ENABLED' if your tasks need internet access
			  }
			},
			platformCapabilities: ['FARGATE'], 
			jobDefinitionName: `metric-ingestion-jobdef-${getSuffix(this)}`,
			timeout: {
				attemptDurationSeconds: 7200
			  }
		  });
		// Add permission to invoke LLM router lambda
		metricIngestion.addToRolePolicy(new PolicyStatement({
			effect: iam.Effect.ALLOW,
			actions: ['lambda:InvokeFunction'],
			resources: [`arn:aws:lambda:${this.region}:${this.account}:function:llm-router-lambda-${getSuffix(this)}`]
		}));

		if (metricIngestion.role && this.props && athenaPolicy) {
			metricIngestion.role.attachInlinePolicy(athenaPolicy);
		}
		this.props?.curatedBucket.grantReadWrite(metricIngestion);
		dbSecret.grantRead(metricIngestion);
		orgEncryptionKeySecret.grantRead(metricIngestion);
		this.customerioSecret.grantRead(metricIngestion);
		this.hubspotSecret.grantRead(metricIngestion);
		this.raleonAppDbSecret.grantRead(metricIngestion);
		this.props?.metricQueue.grantConsumeMessages(metricIngestion);
		this.props?.metricQueue.grantSendMessages(metricIngestion);
		metricIngestion.addEventSource(
			new SqsEventSource(this.props!.metricQueue, {
				batchSize: 1,
				maxBatchingWindow: Duration.seconds(1),
			})
		);
		this.segmentWriterQueue.grantSendMessages(metricIngestion);

		jobRole.addToPolicy(new PolicyStatement({
			effect: iam.Effect.ALLOW,
			actions: ['lambda:InvokeFunction'],
			resources: [`arn:aws:lambda:${this.region}:${this.account}:function:llm-router-lambda-${getSuffix(this)}`]
		}));

		if (jobRole && this.props && athenaPolicy) {
			jobRole.attachInlinePolicy(athenaPolicy);
		}
		this.props?.curatedBucket.grantReadWrite(jobRole);
		dbSecret.grantRead(jobRole);
		orgEncryptionKeySecret.grantRead(jobRole);
		this.customerioSecret.grantRead(jobRole);
		this.hubspotSecret.grantRead(jobRole);
		this.raleonAppDbSecret.grantRead(jobRole);
		this.segmentWriterQueue.grantSendMessages(jobRole);
		jobRole.addToPolicy(new iam.PolicyStatement({
			actions: ['batch:SubmitJob'],
			resources: ['*'],
		  }));
		  new logs.LogGroup(this, `BatchLogGroup-${getSuffix(this)}`, {
			logGroupName: `/aws/batch/metric-ingestion-${getSuffix(this)}`,
			retention: logs.RetentionDays.THREE_DAYS,
			removalPolicy: RemovalPolicy.DESTROY
		  });
		this.props?.metricQueue.grantConsumeMessages(jobRole);
		this.props?.metricQueue.grantSendMessages(jobRole);
	}


	private buildSegmentationInfrastucture(vpc: IVpc, athenaPolicy?: Policy) {
		this.segmentWriterQueue = new sqs.Queue(this, `segment-writer-queue-${getSuffix(this)}`, {
			queueName: `segment-writer-queue-${getSuffix(this)}`,
			deadLetterQueue: {
				queue: new sqs.Queue(this, `segment-writer-deadletter-queue-${getSuffix(this)}`, {
					queueName: `segment-writer-deadletter-queue-${getSuffix(this)}`,
					retentionPeriod: Duration.days(4),
				}),
				maxReceiveCount: 3,
			},
			visibilityTimeout: Duration.seconds(900),
		});

		const dbSecretArn = this.node.tryGetContext(
            getSuffix(this) == 'prod' 
            ? 'shopify-db-secret-arn' 
            : 'shopify-db-dev-secret-arn');
        const dbSecret = Secret.fromSecretCompleteArn(
            this,
            `ShopifyDBSecret-Tags-${getSuffix(this)}`,
            dbSecretArn,
        );

		const segmentWriter = new NodejsFunction(this, `segment-tag-writer-${getSuffix(this)}`, {
			functionName: `segment-tag-writer-${getSuffix(this)}`,
			memorySize: 1024,
			timeout: Duration.minutes(15),
			runtime: lambda.Runtime.NODEJS_18_X,
			logRetention: logs.RetentionDays.THREE_DAYS,
			entry: path.join(__dirname, '/../src/segmentation/segment-tag-writer.ts'),
			handler: 'main',
			environment: {
				ATHENA_OUTPUT_BUCKET: `s3://${this.props?.athenaOutput?.bucketName}` || "",
				SEGMENT_QUEUE_URL: this.segmentWriterQueue.queueUrl,
				RALEON_DB: this.raleonAppDbSecret.secretArn,
				ATHENA_DB: this.isDev ? 'ecommerce_dev' : 'ecommerce',
				SECRET_ARN: dbSecret.secretArn,
			},
			vpc,
			vpcSubnets: {
				subnets: [
					Subnet.fromSubnetId(
						this,
						`raleon-subnet-shopify-metrics-ingestion-${getSuffix(this)}`,
						"subnet-04ef7beb8a56268d1"
					),
				],
			},
			securityGroups: [
				SecurityGroup.fromLookupByName(
					this,
					`raleon-sg-shopify-metric-ingestion-2-${getSuffix(this)}`,
					"default",
					vpc
				),
			],
		});

		if (segmentWriter.role && this.props && athenaPolicy) {
			segmentWriter.role.attachInlinePolicy(athenaPolicy);
		}

		segmentWriter.addEventSource(
			new SqsEventSource(this.segmentWriterQueue, {
			batchSize: 1,
			maxBatchingWindow: Duration.seconds(1),
		}));

		dbSecret.grantRead(segmentWriter);
		this.segmentWriterQueue.grantConsumeMessages(segmentWriter);
		this.segmentWriterQueue.grantSendMessages(segmentWriter);
	}
}
import { Stack, StackProps, Duration, Tags, RemovalPolicy } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { Effect, ManagedPolicy, Policy, PolicyStatement } from 'aws-cdk-lib/aws-iam';
import * as elbv2 from 'aws-cdk-lib/aws-elasticloadbalancingv2';
import * as elbv2Targets from 'aws-cdk-lib/aws-elasticloadbalancingv2-targets';
import * as acm from 'aws-cdk-lib/aws-certificatemanager';
import { getSuffix } from './utils';
import * as rds from 'aws-cdk-lib/aws-rds';
import * as codepipeline from 'aws-cdk-lib/aws-codepipeline';
import * as codepipeline_actions from 'aws-cdk-lib/aws-codepipeline-actions';
import * as codebuild from 'aws-cdk-lib/aws-codebuild';
import * as codedeploy from 'aws-cdk-lib/aws-codedeploy';
import * as ecr from 'aws-cdk-lib/aws-ecr';
import { ARecord, HostedZone, RecordTarget } from 'aws-cdk-lib/aws-route53';
import { LoadBalancerTarget } from 'aws-cdk-lib/aws-route53-targets';
import { Secret } from 'aws-cdk-lib/aws-secretsmanager';
import { IVpc, SecurityGroup, Subnet, Vpc } from 'aws-cdk-lib/aws-ec2';
import { Topic, Subscription } from 'aws-cdk-lib/aws-sns';
import { EmailSubscription } from 'aws-cdk-lib/aws-sns-subscriptions';
import { Alarm, Metric, TreatMissingData } from 'aws-cdk-lib/aws-cloudwatch';

interface ShopifyAppInstanceStackProps extends StackProps {
	deploy_environment_name: string;
}

export class ShopifyAppInstanceStack extends Stack {
	private vpc: IVpc;
	private securityGroup: SecurityGroup;

	constructor(private scope: Construct, private id: string, private props?: ShopifyAppInstanceStackProps) {
		super(scope, id, props);
		this.vpc = Vpc.fromLookup(this, `raleon-vpc`,{isDefault: false});
	
		const instances = [];
		
		const shopifyInstance = this.createInstance(`shopify-app`, this.getShopifyAppInstanceInitScript());
		instances.push(shopifyInstance);
		
		if (getSuffix(this) === 'prod') {
			const shopifyReplicaInstance = this.createInstance(
				`shopify-app-replica`,
				this.getShopifyAppInstanceInitScript()
			);
			Tags.of(shopifyReplicaInstance).add('Type', `shopify-app-${getSuffix(this)}`);
			instances.push(shopifyReplicaInstance);
		}
		Tags.of(shopifyInstance).add('Type', `shopify-app-${getSuffix(this)}`);

		this.createShopifyAppLB(instances);
		this.createPostgresDB();
		this.createCodePipeline();
	}

	createInstance(
		instanceName: string, 
		startupScript: ec2.CloudFormationInit,
	) {
		
		const instance = new ec2.Instance(this, `${instanceName}-instance`, {
			instanceName: `${instanceName}-${getSuffix(this)}`,
			vpc: this.vpc,
			instanceType: getSuffix(this) === 'prod' ? new ec2.InstanceType('t3.small') : new ec2.InstanceType('t2.small'),
			machineImage: ec2.MachineImage.genericLinux({
				'us-east-1': 'ami-0a3c3a20c09d6f377',
				cpuType: ec2.AmazonLinuxCpuType.X86_64,
			}),
			securityGroup: SecurityGroup.fromLookupByName(this, `shopify-ec2-sg-${instanceName}`, 'raleon-webapp-test', this.vpc),
			blockDevices: [{
				deviceName: '/dev/xvda',
				volume: ec2.BlockDeviceVolume.ebs(8, {
					volumeType: ec2.EbsDeviceVolumeType.GP3
				})
			}],
			init: startupScript,
		});

		instance.role.addManagedPolicy(
			ManagedPolicy.fromAwsManagedPolicyName('AmazonSSMManagedInstanceCore')
		);
		instance.role!.addManagedPolicy(
			ManagedPolicy.fromAwsManagedPolicyName('AmazonEC2ContainerRegistryFullAccess')
		);
		instance.role!.addManagedPolicy(
			ManagedPolicy.fromAwsManagedPolicyName('AmazonS3FullAccess')
		);
		instance.role.attachInlinePolicy(new Policy(this, `InstanceKMSDecryptEncryptPolicy-${instanceName}`, {
			statements: [
				new PolicyStatement({
					effect: Effect.ALLOW,
					actions: [
						"kms:Decrypt",
						"kms:Encrypt"
					],
					resources: ['*']
				}),
				new PolicyStatement({
					effect: Effect.ALLOW,
					actions: [
						"secretsmanager:GetSecretValue",
						"secretsmanager:DescribeSecret",
						"secretsmanager:ListSecretVersionIds"
					],
					resources: [
						'arn:aws:secretsmanager:us-east-1:************:secret:shopify-app-cQskH4',
						'arn:aws:secretsmanager:us-east-1:************:secret:shopify-app-dev-jvG4pq',
						'arn:aws:secretsmanager:us-east-1:************:secret:ShopifyAppInstanceStackShop-ay9oN9ABj3Fr-37mgtP',
						'arn:aws:secretsmanager:us-east-1:************:secret:ShopifyAppInstanceStackdevS-h9f6Wf8OwA2h-ErSpDJ',
					]
				})
			],
		}));
		if(getSuffix(this) === 'prod') {
			const snsTopic = new Topic(this, `Ec2CpuAlarmTopic-${instanceName}`, {
				displayName: 'EC2 Alarm Topic'
			});
		
			snsTopic.addSubscription(new EmailSubscription('<EMAIL>'));
			const cpuUtilizationMetric = new Metric({
				namespace: 'AWS/EC2',
				metricName: 'CPUUtilization',
				dimensionsMap: {
					InstanceId: instance.instanceId,
				},
				period: Duration.minutes(5),
			});
			
			const cpuAlarm = new Alarm(this, `CpuAlarm-${instanceName}`, {
				metric: cpuUtilizationMetric,
				threshold: 80, 
				evaluationPeriods: 2,
				treatMissingData: TreatMissingData.NOT_BREACHING,
				alarmDescription: 'Alarm when EC2 CPU utilization exceeds 80%',
				actionsEnabled: true,
			});
			
			cpuAlarm.addAlarmAction({
				bind: () => ({ alarmActionArn: snsTopic.topicArn })
			});

			const ec2Alarm = new Alarm(this, `EC2Alarm-${instanceName}`, {
				metric: new Metric({
					namespace: 'AWS/EC2',
					metricName: 'StatusCheckFailed_Instance',
					dimensionsMap: {
						InstanceId: instance.instanceId,
					},
					statistic: 'sum',
					period: Duration.minutes(5),
				}),
				threshold: 1,
				evaluationPeriods: 1,
				alarmDescription: `Alarm when the ${instanceName} instance status check fails`,
			});
			ec2Alarm.addAlarmAction({
				bind: () => ({ alarmActionArn: snsTopic.topicArn })
			});
		}

		instance.connections.allowFromAnyIpv4(ec2.Port.tcp(22), 'Allow SSH from anywhere');

		return instance;
	}

	private createShopifyAppLB(instances: ec2.Instance[]) {
		const certificate = acm.Certificate.fromCertificateArn(
			this, 
			`ShopifyAppCert-${getSuffix(this)}`,
			'arn:aws:acm:us-east-1:************:certificate/77000be0-28b9-4686-b576-4d5fbb39fee0'
		);

		const publicSubnets = this.vpc.selectSubnets({
			subnetType: ec2.SubnetType.PUBLIC,
			onePerAz: true
		});
	
		const lb = new elbv2.ApplicationLoadBalancer(this, `ShopifyAppLB-${getSuffix(this)}`, {
			loadBalancerName: `shopify-app-${getSuffix(this)}`,
			vpc: this.vpc,
			internetFacing: true,
			securityGroup: SecurityGroup.fromLookupByName(this, 'shopify-lb-sg', 'raleon-load-balancer', this.vpc),
			vpcSubnets: publicSubnets,
		});

		const hostedZone = HostedZone.fromLookup(this, 'Raleon-HostedZone', {
			domainName: 'raleon.io',
		});

		new ARecord(this, 'ShopifyAppAliasRecord', {
			zone: hostedZone,
			recordName: getSuffix(this) == 'prod' ? 'loyalty' : `loyalty-${getSuffix(this)}`,
			target: RecordTarget.fromAlias(new LoadBalancerTarget(lb)),
		});

		const httpListener = lb.addListener(`shopify-app-80-${getSuffix(this)}`, { port: 80 })
		const httpsListener = lb.addListener(`shopify-app-443-${getSuffix(this)}`, { 
			port: 443, 
			protocol: elbv2.ApplicationProtocol.HTTPS,
			certificates: [certificate] 
		});

		httpListener				
			.addAction(`Redirect-${getSuffix(this)}`, {
				action: elbv2.ListenerAction.redirect({
					protocol: 'HTTPS',
					port: '443',
					host: '#{host}',
					path: '/#{path}',
					query: '#{query}',
					permanent: true
				})
			});

		const targets: any[] = [];
		instances.forEach((instance) => {
			targets.push(new elbv2Targets.InstanceTarget(instance, 80));
		});		

		httpsListener
			.addTargets(`shop-https-${getSuffix(this)}`, {
				targetGroupName: `shopify-app-${getSuffix(this)}`,
				port: 80,
				protocol: elbv2.ApplicationProtocol.HTTP,
				loadBalancingAlgorithmType: elbv2.TargetGroupLoadBalancingAlgorithmType.LEAST_OUTSTANDING_REQUESTS,
				targets,
			});
	}

	private getShopifyAppInstanceInitScript(): ec2.CloudFormationInit {
		const scriptUrl = 'https://instance-init-scripts.s3.us-east-1.amazonaws.com/ShopifyAppInit.sh'
		return ec2.CloudFormationInit.fromElements(
			ec2.InitCommand.shellCommand(
				`curl -o /home/<USER>/init.sh ${scriptUrl} && chmod +x /home/<USER>/init.sh && /home/<USER>/init.sh`
			),
		);
	}

	private createPostgresDB() {
		const dbInstance = new rds.DatabaseInstance(this, `ShopifyAppDB-${getSuffix(this)}`, {
			databaseName: `shopifyApp${getSuffix(this)}`,
			engine: rds.DatabaseInstanceEngine.postgres({
				version: rds.PostgresEngineVersion.VER_13_11
			}),
			instanceType: getSuffix(this) === 'prod' ? ec2.InstanceType.of(ec2.InstanceClass.T3, ec2.InstanceSize.MEDIUM) : ec2.InstanceType.of(ec2.InstanceClass.T3, ec2.InstanceSize.MICRO),
			vpc: this.vpc,
			securityGroups: [SecurityGroup.fromLookupByName(this, 'raleon-sg-metric-processor', 'default', this.vpc)],
			multiAz: false,
			allocatedStorage: 20,
			storageType: rds.StorageType.GP2,
			cloudwatchLogsExports: ['postgresql', 'upgrade'],
			credentials: rds.Credentials.fromGeneratedSecret('master'),
			removalPolicy: getSuffix(this) === 'prod' ? undefined : RemovalPolicy.DESTROY,
			parameterGroup: rds.ParameterGroup.fromParameterGroupName(this, `ShopifyParameterGroup-${getSuffix(this)}`, 'default.postgres13'),
			vpcSubnets: {
				subnetType: ec2.SubnetType.PUBLIC
			},
			instanceIdentifier: `ShopifyAppDB-${getSuffix(this)}`,
		})
		if(getSuffix(this) === 'prod') {
			const snsTopic = new Topic(this, 'RdsCpuAlarmTopic', {
				displayName: 'RDS Alarm Topic'
			});
		
			snsTopic.addSubscription(new EmailSubscription('<EMAIL>'));
		
			const cpuUtilizationMetric = new Metric({
				namespace: 'AWS/RDS',
				metricName: 'CPUUtilization',
				dimensionsMap: {
					DBInstanceIdentifier: dbInstance.instanceIdentifier,
				},
			});
		
			new Alarm(this, 'RdsCpuAlarm', {
				metric: cpuUtilizationMetric,
				threshold: 80,
				evaluationPeriods: 2,
				datapointsToAlarm: 2,
				treatMissingData: TreatMissingData.NOT_BREACHING,
				alarmDescription: 'Alarm when RDS CPU utilization exceeds 80%',
				actionsEnabled: true,
			}).addAlarmAction({
				bind: () => ({ alarmActionArn: snsTopic.topicArn })
			});
		
			const statusCheckMetric = new Metric({
				namespace: 'AWS/RDS',
				metricName: 'StatusCheckFailed',
				dimensionsMap: {
					DBInstanceIdentifier: dbInstance.instanceIdentifier,
				},
			});
		
			new Alarm(this, 'RdsStatusCheckFailedAlarm', {
				metric: statusCheckMetric,
				threshold: 1,
				evaluationPeriods: 1,
				alarmDescription: 'Alarm when RDS instance status check fails',
				actionsEnabled: true,
			}).addAlarmAction({
				bind: () => ({ alarmActionArn: snsTopic.topicArn })
			});

			const databaseConnectionsMetric = new Metric({
				namespace: 'AWS/RDS',
				metricName: 'DatabaseConnections',
				dimensionsMap: {
					DBInstanceIdentifier: dbInstance.instanceIdentifier,
				},
			});
		
			new Alarm(this, 'RdsDatabaseConnectionsAlarm', {
				metric: databaseConnectionsMetric,
				threshold: 70,
				evaluationPeriods: 1,
				alarmDescription: 'Alarm when RDS database connections exceed the defined threshold',
				actionsEnabled: true,
			}).addAlarmAction({
				bind: () => ({ alarmActionArn: snsTopic.topicArn })
			});
		}		
	}

	private createCodePipeline() {
		const ecrRepo = new ecr.Repository(this, 'ShopifyAppEcrRepo', {
			repositoryName: `shopify-app-${getSuffix(this)}`
		});

		const sourceArtifact = new codepipeline.Artifact();

        const sourceAction = new codepipeline_actions.CodeStarConnectionsSourceAction({
            actionName: 'GithubSource',
			owner: 'RaleonHQ',
            repo: 'raleon-shopify',
			connectionArn: 'arn:aws:codestar-connections:us-east-1:************:connection/c684c070-7b4f-4d2f-98d9-524361d60d5c',
            output: sourceArtifact,
			branch: getSuffix(this) == 'dev' ? 'dev' : 'main',
        });

        const buildProject = new codebuild.PipelineProject(this, `shopify-app-pipeline-project-${getSuffix(this)}`, {
			projectName: `shopify-app-${getSuffix(this)}`,
            environment: {
                buildImage: codebuild.LinuxBuildImage.STANDARD_4_0,
				privileged: true,
				environmentVariables: { 
					'AWS_ACCOUNT_ID': { value: this.props?.env?.account },
					'DOCKER_USERNAME': { value: 'raleon' },
					'DOCKER_PASSWORD': { value: 'bHih;QB@3y8J6%V' },
					'IMAGE_REPO_NAME': { value: ecrRepo.repositoryName },
					'IMAGE_TAG': { value: 'latest' },
					'IS_DEV': { value: getSuffix(this) === 'dev' }
				},
            },
			vpc: this.vpc,
			securityGroups: [SecurityGroup.fromLookupByName(this, `raleon-shop-default-sg-${getSuffix(this)}`, 'default', this.vpc)],
			subnetSelection: {
				subnets: [
					Subnet.fromSubnetId(
						this,
						`shopify-instance-priv-lambda-${getSuffix(this)}`,
						"subnet-04ef7beb8a56268d1"
					),
				],
			}
        });
		ecrRepo.grantPullPush(buildProject.role!);
		buildProject.role!.addManagedPolicy(ManagedPolicy.fromAwsManagedPolicyName('AmazonEC2ContainerRegistryFullAccess'));
		const dbSecretArn = this.node.tryGetContext(`${getSuffix(this) == 'dev' ? 'shopify-db-dev-secret-arn' : 'shopify-db-secret-arn'}`);
		const dbSecret = Secret.fromSecretCompleteArn(this, `ShopifyDBSecret-${getSuffix(this)}`, dbSecretArn);
		dbSecret.grantRead(buildProject.role!);

		buildProject.role?.attachInlinePolicy(new Policy(this, 'EC2-Describe-Permissions', {
			statements: [
				new PolicyStatement({
					effect: Effect.ALLOW,
					actions: [
						"ec2:DescribeInstances",
						"ec2:DescribeNetworkInterfaces",
						"ec2:DeleteNetworkInterface",
						"ecr:*",
					],
					resources: ['*']
				})
			],
		}))

		const buildOutput = new codepipeline.Artifact('BuildOutput');

        const buildAction = new codepipeline_actions.CodeBuildAction({
            actionName: 'BuildAction',
            project: buildProject,
            input: sourceArtifact,
            outputs: [buildOutput],
        });

        const app = new codedeploy.ServerApplication(this, `ShopifyApp-Server-Application-${getSuffix(this)}`, {
            applicationName: `shopify-app-${getSuffix(this)}`
        });

        const deploymentGroup = new codedeploy.ServerDeploymentGroup(this, `ShopifyDeploymentGroup-${getSuffix(this)}`, {
			deploymentGroupName: `shopify-app-${getSuffix(this)}`,
            application: app,
            deploymentConfig: codedeploy.ServerDeploymentConfig.ALL_AT_ONCE,
            ec2InstanceTags: new codedeploy.InstanceTagSet({
                'Type': [`shopify-app-${getSuffix(this)}`]
            }),
        });

		deploymentGroup.role?.addManagedPolicy(ManagedPolicy.fromAwsManagedPolicyName('AmazonS3FullAccess'));
		deploymentGroup.role?.addManagedPolicy(ManagedPolicy.fromAwsManagedPolicyName('EC2InstanceProfileForImageBuilderECRContainerBuilds'));
		deploymentGroup.role?.attachInlinePolicy(new Policy(this, 'KMSDecryptEncryptPolicy', {
			statements: [
				new PolicyStatement({
					effect: Effect.ALLOW,
					actions: [
						"kms:Decrypt",
						"kms:Encrypt"
					],
					resources: ['*']
				}),
			],
		}))

        const deployAction = new codepipeline_actions.CodeDeployServerDeployAction({
            actionName: 'DeployAction',
            input: buildOutput,
            deploymentGroup: deploymentGroup,
        });

        new codepipeline.Pipeline(this, `Pipeline-${getSuffix(this)}`, {
			pipelineName: `shopify-app-${getSuffix(this)}`,
            stages: [
                {
                    stageName: 'Source',
                    actions: [sourceAction],
                },
                {
                    stageName: 'Build',
                    actions: [buildAction],
                },
                {
                    stageName: 'Deploy',
                    actions: [deployAction],
                },
            ],
        });
    }
}

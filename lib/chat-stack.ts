import { Stack, StackProps, Duration} from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { NodejsFunction } from 'aws-cdk-lib/aws-lambda-nodejs';
import { Runtime } from 'aws-cdk-lib/aws-lambda';
import { RetentionDays } from 'aws-cdk-lib/aws-logs';
import path = require('path');
import { getSuffix } from './utils';
import { IVpc, Subnet, SecurityGroup, Vpc} from 'aws-cdk-lib/aws-ec2';
import * as Utils from './utils';
import { Rule, Schedule } from 'aws-cdk-lib/aws-events';
import { LambdaFunction } from 'aws-cdk-lib/aws-events-targets';
import { Secret } from "aws-cdk-lib/aws-secretsmanager";

interface ChatStackProps extends StackProps {
	deploy_environment_name: string;
}

export class ChatStack extends Stack {

	private vpc: IVpc;
	private isDev: boolean;

	constructor(scope: Construct, id: string, private props?: ChatStackProps) {
		super(scope, id, props);
		this.isDev = Utils.isDev(props!.deploy_environment_name);

		this.vpc = Vpc.fromLookup(
			this,
			`raleon-vpc-shopify-${getSuffix(this)}`,
			{ vpcName: "raleon-vpc" }
		);

		const webAppTokenSecret = Secret.fromSecretNameV2(
			this, 
			`integration-id-${getSuffix(this)}`, 
			this.isDev ? 'Dev-Raleon-Service-API': 'Prod-Raleon-Service-API'
		);

		const chatResetLambda = new NodejsFunction(this, `ChatResetLambda-${getSuffix(this)}`, {
			functionName: `chat-reset-${getSuffix(this)}`,
			runtime: Runtime.NODEJS_18_X,
			handler: 'main',
			entry: path.join(__dirname, `../src/notifications/chat-reset.ts`),
			environment: {
				WEBAPP_API_URL: `http://${this.isDev ? this.node.tryGetContext('internal-dev-webapp-domain') : this.node.tryGetContext('internal-webapp-domain')}/api/v1`,
				WEBAPP_TOKEN_SECRET: webAppTokenSecret.secretArn
			},
			logRetention: RetentionDays.THREE_DAYS,
			memorySize: 1024,
			timeout: Duration.minutes(15),
			vpc: this.vpc,
			vpcSubnets: { 
				subnets: [Subnet.fromSubnetId(this, "raleon-subnet-shopify", "subnet-04ef7beb8a56268d1"),], 
			}, 
			securityGroups: [SecurityGroup.fromLookupByName(this, "raleon-sg-shopify", "default", this.vpc),],
		});

		let chatResetSchedule = new Rule(this, `chat-reset-schedule-${getSuffix(this)}`, {
			schedule: Schedule.cron({
				day: '*',
				hour: '4',
				minute: '0',
			}),
		});

		chatResetSchedule.addTarget(new LambdaFunction(chatResetLambda));
		webAppTokenSecret.grantRead(chatResetLambda);
	}
}

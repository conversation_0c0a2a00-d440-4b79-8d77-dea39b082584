import * as cdk from "aws-cdk-lib";
import { Construct } from "constructs";
import { StackProps } from "aws-cdk-lib";
import {
	getSuffix,
	isDev,
} from "./utils";
import * as codepipeline from "aws-cdk-lib/aws-codepipeline";
import * as codepipeline_actions from "aws-cdk-lib/aws-codepipeline-actions";
import * as codebuild from "aws-cdk-lib/aws-codebuild";
import { PolicyStatement } from "aws-cdk-lib/aws-iam";
import { SecurityGroup, Subnet, Vpc } from "aws-cdk-lib/aws-ec2";

interface ShopifyPipelineStackProps extends StackProps {
	deploy_environment_name: string;
}

export class ShopifyPipelineStack extends cdk.Stack {
	isDev: boolean;
	constructor(
		scope: Construct,
		id: string,
		private props?: ShopifyPipelineStackProps
	) {
		super(scope, id, props);
		this.isDev = isDev(props!.deploy_environment_name);
		this.createCodePipeline()
	}

	private createCodePipeline() {
		const sourceArtifact = new codepipeline.Artifact();

		const sourceAction =
			new codepipeline_actions.CodeStarConnectionsSourceAction({
				actionName: "GithubSource",
				owner: "RaleonHQ",
				repo: "shopify-backend",
				connectionArn:
					"arn:aws:codestar-connections:us-east-1:831543322268:connection/c684c070-7b4f-4d2f-98d9-524361d60d5c",
				output: sourceArtifact,
				branch: this.props?.deploy_environment_name === 'prod' ? 'main' : 'dev',
			});

		const vpc = Vpc.fromLookup(this, 'raleon-vpc-shopify', {vpcName: 'raleon-vpc'});

		const buildProject = new codebuild.PipelineProject(
			this,
			"shopify-backend",
			{
				projectName: `shopify-backend-${getSuffix(this)}`,
				environment: {
					buildImage: codebuild.LinuxBuildImage.STANDARD_7_0,
					privileged: true,
					environmentVariables: {
						AWS_ACCOUNT_ID: { value: this.props?.env?.account },
					},
				},
				vpc: vpc,
				subnetSelection: {subnets: [Subnet.fromSubnetId(this, 'lambda-subnet-private-codebuild', 'subnet-04ef7beb8a56268d1')]},
				securityGroups: [SecurityGroup.fromLookupByName(this, 'raleon-sg-shopify-codebuild', 'raleon-webapp-development', vpc)],
			}
		);

		buildProject.role!.addToPrincipalPolicy(new PolicyStatement(
			{
				actions: [
					'cloudformation:*',
					'ssm:GetParameter',
					'ec2:*'
				],
				resources: ['*'],
			},
		));
		buildProject.role!.addToPrincipalPolicy(new PolicyStatement({
			actions: [
				'sts:AssumeRole',
				'iam:PassRole'
			],
			resources: [
				'arn:aws:iam::*:role/cdk-readOnlyRole',
				'arn:aws:iam::*:role/cdk-hnb659fds-deploy-role-*',
				'arn:aws:iam::*:role/cdk-hnb659fds-file-publishing-*'
			]
		}));

		const buildOutput = new codepipeline.Artifact(
			"BuildOutput-shopify-backend"
		);

		const buildAction = new codepipeline_actions.CodeBuildAction({
			actionName: "BuildAction",
			project: buildProject,
			environmentVariables: {
				IS_DEV: { value: this.isDev },
			},
			input: sourceArtifact,
			outputs: [buildOutput],
		});

		new codepipeline.Pipeline(this, "Pipeline-shopify-backend", {
			pipelineName: `shopify-backend-${getSuffix(this)}`,
			stages: [
				{
					stageName: "Source",
					actions: [sourceAction],
				},
				{
					stageName: "Build",
					actions: [buildAction],
				},
			],
		});
	}
}

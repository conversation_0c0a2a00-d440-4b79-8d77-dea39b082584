import { Stack, StackProps, RemovalPolicy, Duration, CfnOutput } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { Table, AttributeType, BillingMode } from 'aws-cdk-lib/aws-dynamodb';
import { PolicyStatement, ServicePrincipal } from "aws-cdk-lib/aws-iam";
import { NodejsFunction } from 'aws-cdk-lib/aws-lambda-nodejs';
import { Runtime } from 'aws-cdk-lib/aws-lambda';
import { RetentionDays } from 'aws-cdk-lib/aws-logs';
import path = require('path');
import { getSuffix, isDev } from './utils';
import { WebSocketApi, WebSocketStage } from '@aws-cdk/aws-apigatewayv2-alpha';
import { WebSocketLambdaIntegration } from '@aws-cdk/aws-apigatewayv2-integrations-alpha';

interface ShopifyWebSocketStackProps extends StackProps {
	deploy_environment_name: string;
}

export class ShopifyWebSocketStack extends Stack {

	webSocketApi: WebSocketApi;
	chatConnectionsTable: Table;

	constructor(scope: Construct, id: string, props: ShopifyWebSocketStackProps) {
		super(scope, id, props);

		this.chatConnectionsTable = new Table(this, `CustomerChatConnections-${getSuffix(this)}`, {
			tableName: `customer-chat-connections-${getSuffix(this)}`,
			partitionKey: { name: 'PK', type: AttributeType.STRING },
			sortKey: { name: 'SK', type: AttributeType.STRING },
			timeToLiveAttribute: 'ttl',
			removalPolicy: isDev(props.deploy_environment_name) ? RemovalPolicy.DESTROY : RemovalPolicy.RETAIN,
			billingMode: BillingMode.PAY_PER_REQUEST,
		});

		const webSocketApi = new WebSocketApi(this, `Shopify-Websocket-Api-${getSuffix(this)}`, {
			apiName: `shopify-chat-api-${getSuffix(this)}`,
			routeSelectionExpression: '$request.body.action',
			connectRouteOptions: {
				integration: new WebSocketLambdaIntegration(
					`connect-int-${getSuffix(this)}`,
					this.createLambdaFunction('connect', this.chatConnectionsTable.tableName)
				),
			},
			disconnectRouteOptions: {
				integration: new WebSocketLambdaIntegration(
					`disconnect-int-${getSuffix(this)}`,
					this.createLambdaFunction('disconnect', this.chatConnectionsTable.tableName)
				),
			},
		});

		new WebSocketStage(this, `shopify-chat-stage-${getSuffix(this)}`, {
			webSocketApi,
			stageName: 'prod',
			autoDeploy: true
		});

		const routes: string[] = [
			'customer-connected', 
			'customer-event'
		];

		for (const route of routes) {
			const lambda = this.createLambdaFunction(route, this.chatConnectionsTable.tableName, webSocketApi.apiId);
			this.chatConnectionsTable.grantReadWriteData(lambda);
			const apiArn = `arn:aws:execute-api:${this.region}:${this.account}:${webSocketApi.apiId}/*/*/*`;

			lambda.grantInvoke(new ServicePrincipal('apigateway.amazonaws.com', {
				conditions: {
					"ArnLike": {
						"aws:SourceArn": apiArn
					}
				},
			}));

			webSocketApi.addRoute(route, {
				integration: new WebSocketLambdaIntegration(`${route}-integration`, lambda),
			});
			webSocketApi.grantManageConnections(lambda);

			this.webSocketApi = webSocketApi;
		}
	}

	private createLambdaFunction(name: string, tableName: string, websocketApiId?: string): NodejsFunction {
		return new NodejsFunction(this, `ShopifyChatLambda-${name}-${getSuffix(this)}`, {
			functionName: `shopify-chat-${name}-${getSuffix(this)}`,
			runtime: Runtime.NODEJS_18_X,
			handler: 'main',
			entry: path.join(__dirname, `../src/shopify-chat-lambdas/${name}.ts`),
			environment: {
				TABLE_NAME: tableName,
				WEBSOCKET_ENDPOINT_URL: websocketApiId ? `https://${websocketApiId}.execute-api.${this.region}.amazonaws.com/prod/` : '',
			},
			logRetention: RetentionDays.THREE_DAYS,
			memorySize: 256,
		});
	}
}

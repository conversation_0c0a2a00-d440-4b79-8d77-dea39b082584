import { Duration, Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as elbv2 from 'aws-cdk-lib/aws-elasticloadbalancingv2';
import * as elbv2Targets from 'aws-cdk-lib/aws-elasticloadbalancingv2-targets';
import { PrivateHostedZone, ARecord, RecordTarget } from 'aws-cdk-lib/aws-route53';
import { LoadBalancerTarget } from 'aws-cdk-lib/aws-route53-targets';
import { getSuffix, isDev } from './utils';

interface WebappInternalLoadBalancerStackProps extends StackProps {
	deploy_environment_name: string;
}

export class WebappInternalLoadBalancerStack extends Stack {
	private isDev: boolean;
	constructor(scope: Construct, id: string, props?: WebappInternalLoadBalancerStackProps) {
		super(scope, id, props);
		this.isDev = isDev(props!.deploy_environment_name);
		const vpc = ec2.Vpc.fromLookup(this, 'raleon-vpc-webapp-internal', { vpcName: 'raleon-vpc' });
		let internalDomain = this.node.tryGetContext('internal-webapp-domain');
		if(this.isDev) {
			internalDomain = this.node.tryGetContext('internal-dev-webapp-domain');
		}

		const devInstanceId = this.node.tryGetContext('dev-ec2');
		const prod1InstanceId = this.node.tryGetContext('prod-ec2');
		const prod2InstanceId = this.node.tryGetContext('prod-ec2-2');
		const prod3InstanceId = this.node.tryGetContext('prod-ec2-3');

		const albSecurityGroup = new ec2.SecurityGroup(this, `InternalAlbSG-${getSuffix(this)}`, {
			vpc,
			description: 'Security group for internal ALB',
			allowAllOutbound: true
		});
		albSecurityGroup.addIngressRule(ec2.Peer.ipv4('10.0.0.0/16'), ec2.Port.tcp(80), 'Allow HTTP from VPC');

		const lb = new elbv2.ApplicationLoadBalancer(this, `InternalWebappLB-${getSuffix(this)}`, {
		    vpc,
		    internetFacing: false,
		    loadBalancerName: `webapp-internal-${props?.deploy_environment_name}`,
		    securityGroup: albSecurityGroup
		});
		const targetGroup = new elbv2.ApplicationTargetGroup(this, `InternalWebappTG-${getSuffix(this)}`, {
		    vpc,
		    port: 80,
		    protocol: elbv2.ApplicationProtocol.HTTP,
		    healthCheck: {
		        path: '/api/v1/ping',
		        port: '80',
		        protocol: elbv2.Protocol.HTTP,
		        healthyThresholdCount: 2,
		        unhealthyThresholdCount: 3,
		        timeout: Duration.seconds(5),
		        interval: Duration.seconds(30)
		    }
		});

		if (this.isDev) {
		    targetGroup.addTarget(new elbv2Targets.InstanceIdTarget(devInstanceId, 80));
		} else {
		    targetGroup.addTarget(new elbv2Targets.InstanceIdTarget(prod1InstanceId, 80));
		    targetGroup.addTarget(new elbv2Targets.InstanceIdTarget(prod2InstanceId, 80));
		    targetGroup.addTarget(new elbv2Targets.InstanceIdTarget(prod3InstanceId, 80));
		}

		lb.addListener('HttpListener', { port: 80, open: true }).addTargetGroups('tg', { targetGroups: [targetGroup] });

		if (internalDomain) {
		    const zone = new PrivateHostedZone(this, 'InternalHostedZone', {
		        zoneName: internalDomain,
		        vpc,
		    });
		    new ARecord(this, 'InternalAliasRecord', {
		        zone,
		        target: RecordTarget.fromAlias(new LoadBalancerTarget(lb)),
		    });
		}
	}
}
import { Duration, Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { RestApi, LambdaIntegration, AwsIntegration, Model, MethodLoggingLevel, CfnMethod, PassthroughBehavior, HttpIntegration, ContentHandling } from 'aws-cdk-lib/aws-apigateway';
import { NodejsFunction } from 'aws-cdk-lib/aws-lambda-nodejs';
import { Runtime } from 'aws-cdk-lib/aws-lambda';
import * as path from "path";
import { getSuffix, isDev } from './utils';
import { RetentionDays } from 'aws-cdk-lib/aws-logs';
import { SecurityGroup, Subnet, Vpc } from 'aws-cdk-lib/aws-ec2';
import { Secret } from 'aws-cdk-lib/aws-secretsmanager';
import { Effect, ManagedPolicy, Policy, PolicyDocument, PolicyStatement, Role, ServicePrincipal } from 'aws-cdk-lib/aws-iam';
import { CfnDeliveryStream } from 'aws-cdk-lib/aws-kinesisfirehose';
import { Bucket } from 'aws-cdk-lib/aws-s3';
import { Rule, Schedule } from 'aws-cdk-lib/aws-events';
import { LambdaFunction } from 'aws-cdk-lib/aws-events-targets';

interface ShopifyAppProxyStackProps extends StackProps {
	deploy_environment_name: string;
	curatedBucket: Bucket;
}

export class ShopifyAppProxyStack extends Stack {
	private isDev: boolean;
	private firehoseDeliveryStream: CfnDeliveryStream;
	private firehoseRole: Role;

	constructor(scope: Construct, id: string, private props?: ShopifyAppProxyStackProps) {
		super(scope, id, props);
		this.isDev = isDev(props!.deploy_environment_name);
		this.createAPIGateway();

		this.createEarnUpdateLambda();

		this.createShopifyAdminProxy(); //this is for making calls from scripts so we don't use the wrong api verson
	}

	private createShopifyAdminProxy() {
		const api = new RestApi(this, `ShopifyAdminProxy-${getSuffix(this)}`, {
			restApiName: `shopify-admin-proxy-${getSuffix(this)}`,
			description: 'This service serves as a proxy for Shopify admin api calls.',
		});

		const proxyLambda = this.createProxyLambda('shopify-admin-proxy', 'shopify-proxy/admin-proxy.ts');
		const proxyIntegration = new LambdaIntegration(proxyLambda);

		const proxyResource = api.root.addResource('{proxy+}');
		proxyResource.addMethod('ANY', proxyIntegration);
	}

	private createAPIGateway() {
		const api = new RestApi(this, `ShopifyAppProxyApi-${getSuffix(this)}`, {
			restApiName: `shopify-app-proxy-${getSuffix(this)}`,
			description: 'This service serves as a proxy for Shopify app.',
			deployOptions: {
				loggingLevel: MethodLoggingLevel.INFO,
				dataTraceEnabled: true
			}
		});

		const proxyLambda = this.createProxyLambda('shopify-app-proxy', 'shopify-proxy/webapp-proxy.ts');
		const proxyIntegration = new LambdaIntegration(proxyLambda);

		//this is standard api gateway sqs to lambda
		const eventProxyLambda = this.createEventProxyLambda();
		const eventProxyIntegration = new LambdaIntegration(eventProxyLambda);

		const eventProxyResource = api.root.addResource('event-stream');
		eventProxyResource.addMethod('ANY', eventProxyIntegration);

		this.createLoyaltyEventFirehoseProxy(api);
		this.createPixelTracking(api);

		const proxyResource = api.root.addResource('{proxy+}');
		proxyResource.addMethod('ANY', proxyIntegration);
	}

	private createProxyLambda(fnName: string, fnPath: string) {
		const dbSecretArn = this.node.tryGetContext(
			getSuffix(this) == 'prod'
				? 'shopify-db-secret-arn'
				: 'shopify-db-dev-secret-arn');

		const appSecretArn = this.node.tryGetContext(
			getSuffix(this) == 'prod'
				? 'shopify-app-secret-arn'
				: 'shopify-app-dev-secret-arn'
		);
		const dbSecret = Secret.fromSecretCompleteArn(
			this,
			`ShopifyDBSecret-${fnName}-${getSuffix(this)}`,
			dbSecretArn,
		);
		const appSecret = Secret.fromSecretCompleteArn(
			this,
			`ShopifyAppSecret-${fnName}-${getSuffix(this)}`,
			appSecretArn,
		);

		const vpc = Vpc.fromLookup(
			this,
			`raleon-vpc-shopify-${fnName}--${getSuffix(this)}`,
			{ vpcName: "raleon-vpc" }
		);

		const lambda = new NodejsFunction(this, `ProxyLambdaFunction-${fnName}-${getSuffix(this)}`, {
			functionName: `${fnName}-${getSuffix(this)}`,
			runtime: Runtime.NODEJS_18_X,
			memorySize: 512,
			timeout: Duration.seconds(60),
			entry: path.join(__dirname, `/../src/${fnPath}`),
			handler: 'main',
			logRetention: RetentionDays.THREE_DAYS,
			environment: {
					SECRET_ARN: dbSecret.secretArn,
					SHOPIFY_SECRET_KEY_ARN: appSecretArn,
					WEBAPP_API_URL: `http://${this.isDev ? this.node.tryGetContext('internal-dev-webapp-domain') : this.node.tryGetContext('internal-webapp-domain')}/api/v1`,
					LATEST_SHOPIFY_API_VERSION: '2024-07'
			},
			vpc,
			vpcSubnets: {
				subnets: [
					Subnet.fromSubnetId(
						this,
						`raleon-subnet-${fnName}-shopify`,
						"subnet-04ef7beb8a56268d1"
					),
				],
			},
			securityGroups: [
				SecurityGroup.fromLookupByName(
					this,
					`raleon-sg-${fnName}-shopify`,
					"default",
					vpc
				),
			],
		});

		dbSecret.grantRead(lambda);
		appSecret.grantRead(lambda);
		return lambda;
	}

	private createEventProxyLambda() {
		const dbSecretArn = this.node.tryGetContext(
			getSuffix(this) == 'prod'
				? 'shopify-db-secret-arn'
				: 'shopify-db-dev-secret-arn');

		const appSecretArn = this.node.tryGetContext(
			getSuffix(this) == 'prod'
				? 'shopify-app-secret-arn'
				: 'shopify-app-dev-secret-arn'
		);
		const dbSecret = Secret.fromSecretCompleteArn(
			this,
			`ShopifyDBSecret-event-${getSuffix(this)}`,
			dbSecretArn,
		);
		const appSecret = Secret.fromSecretCompleteArn(
			this,
			`ShopifyAppSecret-event-${getSuffix(this)}`,
			appSecretArn,
		);
		const raleonAppDbSecret = Secret.fromSecretNameV2(
			this,
			`raleonwebapp-id-event-${getSuffix(this)}`,
			this.isDev ? 'raleonwebapp' : 'raleonwebapp-prod'
		);

		const vpc = Vpc.fromLookup(
			this,
			`raleon-vpc-shopify-event-${getSuffix(this)}`,
			{ vpcName: "raleon-vpc" }
		);


		const lambda = new NodejsFunction(this, `EventProxyLambdaFunction-${getSuffix(this)}`, {
			functionName: `shopify-event-proxy-${getSuffix(this)}`,
			runtime: Runtime.NODEJS_18_X,
			memorySize: 512,
			timeout: Duration.seconds(60),
			entry: path.join(__dirname, "/../src/shopify-proxy/event-proxy.ts"),
			handler: 'main',
			logRetention: RetentionDays.THREE_DAYS,
			environment: {
				SECRET_ARN: dbSecret.secretArn,
				SHOPIFY_SECRET_KEY_ARN: appSecretArn,
				EVENT_API_URL: `https://${this.isDev ? 'c7z7eka815' : 's393yf9cql'}.execute-api.us-east-1.amazonaws.com`,
				RALEON_DB: raleonAppDbSecret.secretArn,
			},
			vpc,
			vpcSubnets: {
				subnets: [
					Subnet.fromSubnetId(
						this,
						"raleon-subnet-shopify-event",
						"subnet-04ef7beb8a56268d1"
					),
				],
			},
			securityGroups: [
				SecurityGroup.fromLookupByName(
					this,
					"raleon-sg-shopify-event",
					"default",
					vpc
				),
			],
		});
		lambda.role?.attachInlinePolicy(new Policy(
			this,
			`invoke-event-stream-${getSuffix(this)}`,
			{
				statements: [new PolicyStatement({
					actions: ['execute-api:Invoke'],
					resources: [
						`arn:aws:execute-api:us-east-1:${this.account}:${this.isDev ? 'c7z7eka815' : 's393yf9cql'}/*/*/*`
					],
				})],
			}
		));

		dbSecret.grantRead(lambda);
		appSecret.grantRead(lambda);
		raleonAppDbSecret.grantRead(lambda);
		return lambda;
	}

	private createPixelTracking(api: RestApi) {
		const resource = api.root.addResource('track');
		const pixelResource = resource.addResource('pixel');
		
		const apiUrl = `https://${api.restApiId}.execute-api.us-east-1.amazonaws.com`;
		const stageName = 'prod';
	
		// Create IAM role for the integration
		const integrationRole = new Role(this, 'PixelTrackingIntegrationRole', {
			assumedBy: new ServicePrincipal('apigateway.amazonaws.com'),
			inlinePolicies: {
				'api-gateway-policy': new PolicyDocument({
					statements: [
						new PolicyStatement({
							actions: ['execute-api:Invoke'],
							resources: [`arn:aws:execute-api:us-east-1:${this.account}:${api.restApiId}/${stageName}/PUT/loyalty-event`],
							effect: Effect.ALLOW
						})
					]
				})
			}
		});
	
	
		const pixelIntegration = new AwsIntegration({
			service: 'firehose',
			integrationHttpMethod: 'POST',
			action: 'PutRecord',
			options: {
				credentialsRole: this.firehoseRole,
				integrationResponses: [{
					statusCode: '200',
					responseTemplates: {
						'image/gif': 'R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'
					},
					responseParameters: {
						'method.response.header.Content-Type': "'image/gif'",
						'method.response.header.Cache-Control': "'no-cache, no-store, must-revalidate'",
						'method.response.header.Pragma': "'no-cache'",
						'method.response.header.Expires': "'0'"
					}
				}],
				requestTemplates: {
					'application/json': `
						#set($timestamp = $context.requestTimeEpoch)
						#set($payload = '{"event": "email_open","timestamp": '+ $timestamp +',"customer": "'+$input.params('ralid')+'","data": { "pid": "'+$input.params('pid')+'","raleonuserid": "'+$input.params('ralid')+'","time": "'+$input.params('time')+'"},"organization": "'+$input.params('organization')+'"}')
						{
						"DeliveryStreamName": "${this.firehoseDeliveryStream.deliveryStreamName}",
						"Record": {
							"Data": "$util.base64Encode($payload)"
						}
						}
						`
				},
			}
		});
	
		pixelResource.addMethod('GET', pixelIntegration, {
			requestParameters: {
				'method.request.querystring.pid': true,
				'method.request.querystring.ralid': true,
				'method.request.querystring.time': true,
				'method.request.querystring.organization': true
			},
			methodResponses: [{
				statusCode: '200',
				responseParameters: {
					'method.response.header.Content-Type': true,
					'method.response.header.Cache-Control': true,
					'method.response.header.Pragma': true,
					'method.response.header.Expires': true
				},
				responseModels: {
					'image/gif': Model.EMPTY_MODEL
				}
			}]
		});
	}

	private createLoyaltyEventFirehoseProxy(api: RestApi) {
		this.createFirehose();

		const firehoseRole = new Role(this, 'ApiGatewayFirehoseRole', {
			assumedBy: new ServicePrincipal('apigateway.amazonaws.com'),
		});

		firehoseRole.addToPolicy(new PolicyStatement({
			resources: [`arn:aws:firehose:us-east-1:${this.account}:deliverystream/${this.firehoseDeliveryStream.deliveryStreamName}`],
			actions: ['firehose:PutRecord', 'firehose:PutRecordBatch'],
		}));

		this.firehoseRole = firehoseRole;

		const resource = api.root.addResource('loyalty-event');
		resource.addMethod('PUT', new AwsIntegration({
			service: 'firehose',
			integrationHttpMethod: 'POST',
			action: 'PutRecord',
			options: {
				credentialsRole: firehoseRole,
				integrationResponses: [{
					statusCode: '200',
					responseTemplates: {
						'application/json': JSON.stringify({ status: 'OK' }),
					},
				}],
				requestTemplates: {
					'application/json':
						`#set($customerId = $input.params('logged_in_customer_id'))
						#set($input.path('$').customer = $customerId)
						{
							"DeliveryStreamName": "${this.firehoseDeliveryStream.deliveryStreamName}",
							"Record": {
								"Data": "$util.base64Encode($input.json('$'))"
							}
						}`
				},
			}
		}), {
			methodResponses: [{
				statusCode: '200',
				responseModels: {
					'application/json': Model.EMPTY_MODEL,
				},
			}],
		});
	}

	//TODO: enable cloudwatch logs

	private createFirehose() {
		const transformFunction = new NodejsFunction(this, `event-stream-transform-${getSuffix(this)}`, {
			functionName: `event-stream-transform-${getSuffix(this)}`,
			memorySize: 512,
			timeout: Duration.minutes(1),
			runtime: Runtime.NODEJS_18_X,
			handler: 'main',
			logRetention: RetentionDays.THREE_DAYS,
			logRetentionRetryOptions: {
				base: Duration.seconds(10),
				maxRetries: 100
			},
			entry: path.join(__dirname, '/../src/streaming/loyalty-event-handler.ts'),
			environment: {
				CURATED_BUCKET: this.props!.curatedBucket.bucketName || "",
				ATHENA_DB: this.isDev ? 'loyalty_events_dev' : 'loyalty_events',
			},
		});

		transformFunction.addToRolePolicy(new PolicyStatement({
			actions: ['firehose:PutRecordBatch', 'firehose:PutRecord'],
			resources: ['*'],
		}));

		const firehoseRole = new Role(this, `FirehoseRole-${getSuffix(this)}`, {
			assumedBy: new ServicePrincipal('firehose.amazonaws.com'),
		});

		const firehosePolicy = new PolicyStatement({
			actions: [
				'glue:GetDatabase',
				'glue:GetDatabases',
				'glue:GetTable',
				'glue:GetTableVersion',
				'glue:GetTableVersions',
			],
			resources: [
				`arn:aws:glue:us-east-1:${this.account}:catalog`,
				`arn:aws:glue:us-east-1:${this.account}:database/loyalty_events`,
				`arn:aws:glue:us-east-1:${this.account}:table/loyalty_events/events`
			]
		})

		firehoseRole.attachInlinePolicy(new Policy(this, `FirehosePolicy-${getSuffix(this)}`, {
			statements: [firehosePolicy]
		}));

		transformFunction.grantInvoke(firehoseRole);
		transformFunction.addPermission(`functionConfigPermission-${getSuffix(this)}`, {
			principal: new ServicePrincipal('firehose.amazonaws.com'),
			action: 'lambda:GetFunctionConfiguration'
		});

		this.props!.curatedBucket.grantReadWrite(firehoseRole);

		this.firehoseDeliveryStream = new CfnDeliveryStream(this, `EventStreamDeliveryStream-${getSuffix(this)}`, {
			deliveryStreamType: 'DirectPut',
			deliveryStreamName: `event-stream-${getSuffix(this)}`,
			extendedS3DestinationConfiguration: {
				bucketArn: this.props!.curatedBucket.bucketArn,
				bufferingHints: {
					intervalInSeconds: this.isDev ? 60 : 300,
					sizeInMBs: 100,
				},
				prefix: 'events/organization=!{partitionKeyFromLambda:organization}/event=!{partitionKeyFromLambda:event}/year=!{partitionKeyFromLambda:year}/month=!{partitionKeyFromLambda:month}/day=!{partitionKeyFromLambda:day}/',
				errorOutputPrefix: 'errors/',
				roleArn: firehoseRole.roleArn,
				dataFormatConversionConfiguration: {
					enabled: true,
					inputFormatConfiguration: {
						deserializer: {
							openXJsonSerDe: {},
						},
					},
					outputFormatConfiguration: {
						serializer: {
							parquetSerDe: {},
						},
					},
					schemaConfiguration: {
						databaseName: 'loyalty_events',
						tableName: 'events',
						region: 'us-east-1',
						roleArn: firehoseRole.roleArn,
					},
				},
				processingConfiguration: {
					enabled: true,
					processors: [
						{
							type: 'Lambda',
							parameters: [
								{
									parameterName: 'LambdaArn',
									parameterValue: transformFunction.functionArn,
								},
							],
						},
					],
				},
				dynamicPartitioningConfiguration: {
					enabled: true,
				},
			},
		});
	}

	private createEarnUpdateLambda() {
		const vpc = Vpc.fromLookup(
			this,
			`raleon-vpc-webapp-wte-${getSuffix(this)}`,
			{ vpcName: "raleon-vpc" }
		);

		const lambda = new NodejsFunction(this, `Webapp-Giveaway-Wte-Updater-${getSuffix(this)}`, {
			functionName: `webapp-giveaway-wte-updater-${getSuffix(this)}`,
			runtime: Runtime.NODEJS_18_X,
			memorySize: 512,
			timeout: Duration.seconds(60),
			entry: path.join(__dirname, "/../src/webapp/wte-updater.ts"),
			handler: 'main',
			logRetention: RetentionDays.THREE_DAYS,
			environment: {
				WEBAPP_API_URL: this.isDev ? this.node.tryGetContext('internal-dev-webapp-domain') : this.node.tryGetContext('internal-webapp-domain'),
			},
			vpc,
			vpcSubnets: {
				subnets: [
					Subnet.fromSubnetId(
						this,
						"raleon-subnet-shopify-wte",
						"subnet-04ef7beb8a56268d1"
					),
				],
			},
			securityGroups: [
				SecurityGroup.fromLookupByName(
					this,
					"raleon-sg-shopify-wte",
					"default",
					vpc
				),
			],
		});

		const rule = new Rule(this, 'Rule', {
			schedule: Schedule.cron({ minute: '1', hour: '*' }),
		});

		rule.addTarget(new LambdaFunction(lambda));
		return lambda;
	}
}

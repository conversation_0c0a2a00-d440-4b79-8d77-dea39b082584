import { Duration, Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { getSuffix, isDev,MAX_CONCURRENCY_LAMBDAS,MAX_CONCURRENCY_LAMBDAS_DEV, } from './utils';
import { DockerImageCode, DockerImageFunction } from 'aws-cdk-lib/aws-lambda';
import path = require('path');
import { Bucket } from 'aws-cdk-lib/aws-s3';
import { Policy, PolicyStatement } from 'aws-cdk-lib/aws-iam';
import { IVpc, SecurityGroup, Subnet, Vpc } from "aws-cdk-lib/aws-ec2";
import { ISecret, Secret } from "aws-cdk-lib/aws-secretsmanager";
import * as sqs from "aws-cdk-lib/aws-sqs";
import { SqsEventSource } from "aws-cdk-lib/aws-lambda-event-sources";
import { NodejsFunction } from "aws-cdk-lib/aws-lambda-nodejs";
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as logs from "aws-cdk-lib/aws-logs";
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import { AuthorizationType, LambdaIntegration } from 'aws-cdk-lib/aws-apigateway';
import { Rule, Schedule } from 'aws-cdk-lib/aws-events';
import { LambdaFunction } from 'aws-cdk-lib/aws-events-targets';

export interface ShopifyOffersStackProps extends StackProps {
	deploy_environment_name: string;
	athenaOutput: Bucket;
	curatedBucket: Bucket;
}

export class ShopifyOffersStack extends Stack {
	raleonAppDbSecret: ISecret;
	isDev: boolean;

	constructor(scope: Construct, id: string, private props?: ShopifyOffersStackProps) {
		super(scope, id, props);
		this.isDev = isDev(props!.deploy_environment_name);
		this.raleonAppDbSecret = Secret.fromSecretNameV2(
			this, 
			`raleonwebapp-id-${getSuffix(this)}`, 
			this.isDev ? 'raleonwebapp': 'raleonwebapp-prod'
		);
		const vpc = Vpc.fromLookup(
			this,
			`raleon-vpc-shopify-${getSuffix(this)}`,
			{ vpcName: "raleon-vpc" }
		);
		let athenaPermission = new PolicyStatement({
			actions: [
				"athena:*",
				"glue:*",
				"dynamodb:DescribeTable",
				"dynamodb:ListSchemas",
				"dynamodb:ListStreams",
				"dynamodb:ListTables",
				"dynamodb:Query",
				"dynamodb:Scan",
				"lambda:*",
				"s3:*",
			],
			resources: ["*"],
		});

		let athenaPolicy = new Policy(
			this,
			`offers-athena-access-policy-${getSuffix(this)}`,
			{
				statements: [athenaPermission],
			}
		);
		const offerIngestion = this.buildOfferIngestion(vpc, athenaPolicy);
		const offerWorker = this.buildOfferWorker(offerIngestion, vpc, athenaPolicy);
		const api = this.createOfferProofApi(offerWorker);
		
	}

	private createOfferProofApi(offerWorker: NodejsFunction) {
		const defaultLambda = new NodejsFunction(this, 'defaultOfferLambdaAPI', {
			functionName: `defaultOfferLambdaAPI-${getSuffix(this)}`,
			memorySize: 1024,
			timeout: Duration.seconds(30),
			runtime: lambda.Runtime.NODEJS_18_X,
			handler: 'main',
			entry: path.join(__dirname, '/../src/offers/default.ts'),
			logRetention: logs.RetentionDays.THREE_DAYS,
			logRetentionRetryOptions: {
				base: Duration.seconds(10),
				maxRetries: 100
			}
		});
		const api = new apigateway.LambdaRestApi(this, 'ecommerce-offer', {
			restApiName: `ecommerce-offer-${getSuffix(this)}`,
			handler: defaultLambda,
			proxy: false,
			deployOptions: {
				stageName: 'v1'
			},
			defaultCorsPreflightOptions: {
				allowHeaders: [
					'Content-Type',
					'X-Amz-Date',
					'Authorization',
					'X-Api-Key',
				],
				allowMethods: ['OPTIONS', 'GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
				allowCredentials: true,
				allowOrigins: ['*'],
			},
		});
		const offerResource = api.root.addResource('offer');
		offerResource.addMethod('GET', new apigateway.LambdaIntegration(offerWorker), {
			requestParameters: {
			  'method.request.querystring.orgId': true,
			  'method.request.querystring.offerId': true
			},
			authorizationType: AuthorizationType.IAM,
		  });
		return api;
	  }
	
	private buildOfferWorker(offerIngestion: string, vpc: IVpc, athenaPolicy?: Policy) {	

		let offerWorker = new NodejsFunction(
			this,
			`ecommerce-offer-worker-${getSuffix(this)}`,
			{
				functionName: `ecommerce-offer-worker-${getSuffix(this)}`,
				memorySize: 1024,
				timeout: Duration.seconds(120),
				runtime: lambda.Runtime.NODEJS_18_X,
				handler: "handler",
				logRetention: logs.RetentionDays.THREE_DAYS,
				entry: path.join(__dirname, "/../src/offers/offer-worker.ts"),
				environment: {
					ATHENA_OUTPUT_BUCKET: `s3://${this.props?.athenaOutput.bucketName}` || "",
					RALEON_DB: this.raleonAppDbSecret.secretArn,
					ATHENA_DB: this.isDev ? 'ecommerce_dev' : 'ecommerce',
					POSGRES_DB: this.isDev ? 'postgres_test' : 'posgresreadreplicav3',
					FUNCTION_NAME: offerIngestion,
				},
				vpc,
				vpcSubnets: {
					subnets: [
						Subnet.fromSubnetId(
							this,
							"raleon-subnet-offer-worker",
							"subnet-04ef7beb8a56268d1"
						),
					],
				},
				securityGroups: [
					SecurityGroup.fromLookupByName(
						this,
						"raleon-sg-offer-worker",
						"default",
						vpc
					),
				],
			}
		);

		if (!this.isDev) {
			const rule = new Rule(this, `offer-worker-schedule-${getSuffix(this)}`, {
				ruleName: `offer-worker-schedule-${getSuffix(this)}`,
				schedule: Schedule.cron({ minute: '0', hour: '6'}),
			});
	
			rule.addTarget(new LambdaFunction(offerWorker));
		}
		
		if (offerWorker.role && this.props && athenaPolicy) {
			offerWorker.role.attachInlinePolicy(athenaPolicy);
		}
		this.props?.curatedBucket.grantReadWrite(offerWorker);
		this.raleonAppDbSecret.grantRead(offerWorker);
		return offerWorker;
	}

	private buildOfferIngestion(vpc: IVpc, athenaPolicy?: Policy) {
		const dbSecretArn = this.node.tryGetContext(
			getSuffix(this) == 'prod' 
			? 'shopify-db-secret-arn'
			: 'shopify-db-dev-secret-arn');
		const dbSecret = Secret.fromSecretCompleteArn(
			this,
			`ShopifyDBSecret-${getSuffix(this)}`,
			dbSecretArn,
		);
		const organizationEncryptionKeyArn = this.node.tryGetContext(
			getSuffix(this) == 'prod' 
				? 'organization-encryption-key-arn'
				: 'organization-encryption-key-dev-arn'
		);
		const orgEncryptionKeySecret = Secret.fromSecretCompleteArn(
			this,
			`OrganizationEncryptionKey-${getSuffix(this)}`,
			organizationEncryptionKeyArn,
		);
		let offerIngestion = new DockerImageFunction(this, `OfferIngestion-${getSuffix(this)}`, {
			functionName: `offer-ingestion-${getSuffix(this)}`,
			code: DockerImageCode.fromImageAsset(
				path.join(__dirname, '../src/offers/offer-ingestion'), 
				{ buildArgs: { "--platform": "linux/amd64" } }
			),
			timeout: Duration.seconds(900),
			memorySize: 4096,
			reservedConcurrentExecutions: 15,
			environment: {
				'ATHENA_OUTPUT_BUCKET': `s3://${this.props?.athenaOutput.bucketName}` || "",
				'CURATED_BUCKET': this.props?.curatedBucket.bucketName || "",
				'SECRET_ARN': dbSecret.secretArn,
				'ORGANIZATION_ENCRYPTION_KEY': orgEncryptionKeySecret.secretArn,
				'RALEON_DB': this.raleonAppDbSecret.secretArn,
				'WEBAPP_API_URL': `http://${this.isDev ? this.node.tryGetContext('internal-dev-webapp-domain') : this.node.tryGetContext('internal-dev-webapp-domain')}/api/v1`,
				'ATHENA_DB': this.isDev ? 'ecommerce_dev' : 'ecommerce',
				'POSGRES_DB': this.isDev ? 'postgres_test' : 'posgresreadreplicav3',
			},
			vpc,
			vpcSubnets: {
				subnets: [
					Subnet.fromSubnetId(
						this,
						"raleon-subnet-shopify-offers-ingestion",
						"subnet-04ef7beb8a56268d1"
					),
				],
			},
			securityGroups: [
				SecurityGroup.fromLookupByName(
					this,
					"raleon-sg-shopify-offer-ingestion",
					"default",
					vpc
				),
			],
		});
		if (offerIngestion.role && this.props && athenaPolicy) {
			offerIngestion.role.attachInlinePolicy(athenaPolicy);
		}
		this.props?.curatedBucket.grantReadWrite(offerIngestion);
		this.props?.athenaOutput.grantReadWrite(offerIngestion)
		dbSecret.grantRead(offerIngestion);
		orgEncryptionKeySecret.grantRead(offerIngestion);
		this.raleonAppDbSecret.grantRead(offerIngestion);
		return offerIngestion.functionName;
	}

}
{"app": "npx ts-node --prefer-ts-exts bin/shopify-backend.ts", "watch": {"include": ["**"], "exclude": ["README.md", "cdk*.json", "**/*.d.ts", "**/*.js", "tsconfig.json", "package*.json", "yarn.lock", "node_modules", "test"]}, "context": {"@aws-cdk/aws-lambda:recognizeLayerVersion": true, "@aws-cdk/core:checkSecretUsage": true, "@aws-cdk/core:target-partitions": ["aws", "aws-cn"], "@aws-cdk-containers/ecs-service-extensions:enableDefaultLogDriver": true, "@aws-cdk/aws-ec2:uniqueImdsv2TemplateName": true, "@aws-cdk/aws-ecs:arnFormatIncludesClusterName": true, "@aws-cdk/aws-iam:minimizePolicies": true, "@aws-cdk/core:validateSnapshotRemovalPolicy": true, "@aws-cdk/aws-codepipeline:crossAccountKeyAliasStackSafeResourceName": true, "@aws-cdk/aws-s3:createDefaultLoggingPolicy": true, "@aws-cdk/aws-sns-subscriptions:restrictSqsDescryption": true, "@aws-cdk/aws-apigateway:disableCloudWatchRole": true, "@aws-cdk/core:enablePartitionLiterals": true, "@aws-cdk/aws-events:eventsTargetQueueSameAccount": true, "@aws-cdk/aws-iam:standardizedServicePrincipals": true, "@aws-cdk/aws-ecs:disableExplicitDeploymentControllerForCircuitBreaker": true, "@aws-cdk/aws-iam:importedRoleStackSafeDefaultPolicyName": true, "@aws-cdk/aws-s3:serverAccessLogsUseBucketPolicy": true, "@aws-cdk/aws-route53-patters:useCertificate": true, "@aws-cdk/customresources:installLatestAwsSdkDefault": false, "@aws-cdk/aws-rds:databaseProxyUniqueResourceName": true, "@aws-cdk/aws-codedeploy:removeAlarmsFromDeploymentGroup": true, "@aws-cdk/aws-apigateway:authorizerChangeDeploymentLogicalId": true, "@aws-cdk/aws-ec2:launchTemplateDefaultUserData": true, "@aws-cdk/aws-secretsmanager:useAttachedSecretResourcePolicyForSecretTargetAttachments": true, "@aws-cdk/aws-redshift:columnId": true, "@aws-cdk/aws-stepfunctions-tasks:enableEmrServicePolicyV2": true, "@aws-cdk/aws-ec2:restrictDefaultSecurityGroup": true, "@aws-cdk/aws-apigateway:requestValidatorUniqueId": true, "@aws-cdk/aws-kms:aliasNameRef": true, "@aws-cdk/aws-autoscaling:generateLaunchTemplateInsteadOfLaunchConfig": true, "@aws-cdk/core:includePrefixInUniqueNameGeneration": true, "@aws-cdk/aws-opensearchservice:enableOpensearchMultiAzWithStandby": true, "shopify-event-bus-sources": {"jason": "aws.partner/shopify.com/57006456833/Raleon-Shopify-jason", "mikebernard": "aws.partner/shopify.com/53545566209/Raleon-Shopify-mikebernard", "dev": "aws.partner/shopify.com/69897814017/RaleoyLoyaltyDevEventBridge", "prod": "aws.partner/shopify.com/58197049345/Raleon-Shopify-Prod"}, "shopify-event-bus-arns": {"dev": "arn:aws:events:us-east-1:831543322268:event-bus/aws.partner/shopify.com/69897814017/RaleoyLoyaltyDevEventBridge", "prod": "arn:aws:events:us-east-1:831543322268:event-bus/aws.partner/shopify.com/58197049345/Raleon-Shopify-Prod", "jason": "arn:aws:events:us-east-1:831543322268:event-bus/aws.partner/shopify.com/57006456833/Raleon-Shopify-jason"}, "shopify-db-secret-arn": "arn:aws:secretsmanager:us-east-1:831543322268:secret:ShopifyAppInstanceStackShop-ay9oN9ABj3Fr-37mgtP", "shopify-db-dev-secret-arn": "arn:aws:secretsmanager:us-east-1:831543322268:secret:ShopifyAppInstanceStackdevS-h9f6Wf8OwA2h-ErSpDJ", "shopify-app-secret-arn": "arn:aws:secretsmanager:us-east-1:831543322268:secret:shopify-app-cQskH4", "shopify-app-dev-secret-arn": "arn:aws:secretsmanager:us-east-1:831543322268:secret:shopify-app-dev-jvG4pq", "unsplash-api-secret-arn": "arn:aws:secretsmanager:us-east-1:831543322268:secret:unsplash-ENUuVw", "organization-encryption-key-arn": "arn:aws:secretsmanager:us-east-1:831543322268:secret:organization_keys_prod-KWK6Ny", "organization-encryption-key-dev-arn": "arn:aws:secretsmanager:us-east-1:831543322268:secret:organization_keys_dev-ShI8dK", "internal-webapp-domain": "internal.raleon.io", "internal-dev-webapp-domain": "internal-dev.raleon.io", "dev-ec2": "i-011b341e3ece59165", "prod-ec2": "i-0fbb5c423b989abf4", "prod-ec2-2": "i-0a7d808924fd4cb29", "prod-ec2-3": "i-053afc0ddd77e09ec"}}
import { IIntegrationProcessor } from './integration-processors/integration-processor-interface';
import { KlaviyoProcessor } from './integration-processors/klaviyo-processor';
import { SendlaneProcessor } from './integration-processors/sendlane-processor';
export class IntegrationFactory {
    static getProcessor(integrationId: number): IIntegrationProcessor {
		if (!integrationId) {
			throw new Error("Integration ID not provided");
		}

		const processor = IntegrationProcessors[integrationId];
		if (!processor) {
			throw new Error(`Integration processor not found for ID ${integrationId}`);
		}
		return processor;
    }
}

// we can look these up by id from the db instead for maintainability
export const IntegrationProcessors: Record<number, IIntegrationProcessor> = {
	// 1: JudgeMeProcessor,
	2: new KlaviyoProcessor(),
	// 3: StampedReviewsProcessor,
	// 4: SkioProcessor,
	7: new SendlaneProcessor(),
}

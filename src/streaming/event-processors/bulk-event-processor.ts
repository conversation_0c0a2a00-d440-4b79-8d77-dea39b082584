import { S3Client } from "@aws-sdk/client-s3";
import { IEventProcessor } from "./event-processor-interface";
const { ParquetWriter, ParquetSchema } = require('@dsnp/parquetjs');
const { PutObjectCommand } = require('@aws-sdk/client-s3');
const fs = require('fs');
const s3Client = new S3Client({ region: "us-east-1" });

export class BulkEventProcessor implements IEventProcessor {
	async process(message: any): Promise<void> {
		await this.saveEventData(message);
	}

	formatDate(timestamp: any) {
		const date = new Date(timestamp);
		const year = date.getUTCFullYear();
		const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
		const day = date.getUTCDate().toString().padStart(2, '0');
		return `year=${year}/month=${month}/day=${day}`;
	}

	async saveEventData(message: any) {
		const orgId = message.orgId;
		const eventName = message.eventName;
		const datePath = this.formatDate(message.timestamp);

		const eventSchema = new ParquetSchema({
			customer: { type: "UTF8" },
			timestamp: { type: "TIMESTAMP_MILLIS" },
			data: { type: "UTF8", optional: true }
		});

		const filePath = `/tmp/event_${orgId}_${datePath.replace(/\//g, '_')}.parquet`;
		const writer = await ParquetWriter.openFile(eventSchema, filePath);

		let data = {
			customer: message.customerId,
			timestamp: new Date(message.timestamp),
			data: message.data ? JSON.stringify(message.data) : null
		};
		await writer.appendRow(data);

		await writer.close();

		const params = {
			Bucket: process.env.CURATED_BUCKET,
			Key: `events/organization=${orgId}/event=${eventName}/${datePath}/event_${orgId}_${datePath.replace(/\//g, '_')}.parquet`,
			Body: fs.createReadStream(filePath),
		};

		const command = new PutObjectCommand(params);
		await s3Client.send(command);
	}
}
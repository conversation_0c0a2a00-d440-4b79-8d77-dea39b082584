export class Handler {
    async main(event: any): Promise<any> {
        console.log('event data: ', JSON.stringify(event.records));
        const output = [];

        for (const record of event.records) {
            try {
                const data = (Buffer.from(record.data, 'base64')).toString('ascii');
                console.log('decoded payload', data);
                const jsonData = JSON.parse(data);

                // Handle null/invalid organization
                if (!jsonData.organization) {
                    console.log('Null or missing organization ID');
                    output.push({
                        recordId: record.recordId,
                        result: 'ProcessingFailed',
                        data: record.data
                    });
                    continue;
                }

                const orgIdPattern = /^[0-9]+$/;
                if (!orgIdPattern.test(jsonData.organization)) {
                    console.log('Invalid organization ID');
                    output.push({
                        recordId: record.recordId,
                        result: 'ProcessingFailed',
                        data: record.data
                    });
                    continue;
                }

                if (typeof jsonData.customer !== 'string') {
                    console.log('Invalid customer');
                    continue;
                }

                if (typeof jsonData.event !== 'string' || jsonData.event.trim() === '') {
                    console.log('Invalid event');
                    continue;
                }

                const dateFromTimestamp = new Date(jsonData.timestamp);
                if (isNaN(dateFromTimestamp.getTime())) {
                    console.log('Invalid timestamp');
                    continue;
                }

                let dataContent = {};
                if (jsonData.data) {
                    try {
                        dataContent = typeof jsonData.data === 'string' ? JSON.parse(jsonData.data) : jsonData.data;
                    } catch (e) {
                        console.log('Invalid data');
                        continue;
                    }
                }

                const date = new Date(jsonData.timestamp);
                const year = date.getUTCFullYear();
                const month = String(date.getUTCMonth() + 1).padStart(2, '0');
                const day = String(date.getUTCDate()).padStart(2, '0');

                const mappedData: any = {
                    customer: jsonData.customer || '',
                    timestamp: date, 
                    organization: jsonData.organization || '',
                    event: jsonData.event || '',
                    friendlyName: jsonData.friendlyName || '',
                    data: JSON.stringify(jsonData.data || {})
                };

                output.push({
                    recordId: record.recordId,
                    result: 'Ok',
                    data: (Buffer.from(JSON.stringify(mappedData))).toString('base64'),
                    metadata: {
                        partitionKeys: {
                            organization: mappedData.organization,
                            event: mappedData.event,
                            year: year,
                            month: month,
                            day: day
                        }
                    }
                });
            } catch (error: any) {
                console.log(`Error processing record: ${error.message}`);
                // Always include recordId even on error
                output.push({
                    recordId: record.recordId,
                    result: 'ProcessingFailed',
                    data: record.data
                });
                continue;
            }
        }

        console.log(`Processing completed. Successful records ${output.length}.`);
        return { records: output };
    }
}

export const handler = new Handler();
export const main = handler.main.bind(handler);

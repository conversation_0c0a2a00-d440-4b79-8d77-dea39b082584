import { IEventProcessor } from './event-processors/event-processor-interface';
import { DefaultEventProcessor } from './event-processors/default-event';
import { BulkEventProcessor } from './event-processors/bulk-event-processor';
export class StreamingFactory {
	static getProcessor(eventName: string): IEventProcessor {
		switch (eventName) {
			//add more events here
			case EVENT_NAMES.OFFER_RECEIVED:
				return new BulkEventProcessor();
			default:
				return new DefaultEventProcessor();
		}
	}
}

export enum EVENT_NAMES {
	OFFER_RECEIVED = 'offer_received',
}

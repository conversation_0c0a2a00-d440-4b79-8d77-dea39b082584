import { APIGatewayProxyResultV2, SQSEvent } from "aws-lambda";
import { StreamingFactory } from './streaming-factory';
import { IntegrationFactory } from './integration-factory';
import { getKlaviyoIntegrationByOrg, getConfiguredRaleonEmails, getSendlaneIntegrationByOrg } from "../utils/raleon-helper";
import SQS = require("aws-sdk/clients/sqs");

export class Handler {
	private sqsClient: SQS = new SQS();
	
	async main(event: SQSEvent): Promise<APIGatewayProxyResultV2> {
		console.log(JSON.stringify(event.Records));
		const messages = event.Records.map((record: any) => JSON.parse(record.body));

		for (let message of messages) {
			try {
				const processor = StreamingFactory.getProcessor(message.eventName);
				await processor.process(message);

				// add specific event type support to each integration
				const enabledIntegrationIds = await this.getEnabledIntegrations(message.orgId);
				if (enabledIntegrationIds?.length) {
					for (const integrationId of enabledIntegrationIds) {
						const integrationProcessor = IntegrationFactory.getProcessor(integrationId);
						await integrationProcessor.process(message);
					}
				}

				const raleonLoyaltyEmails = await this.getRaleonEmailConfiguration(message.orgId, message.eventName);
				if (raleonLoyaltyEmails?.length) {
					console.log(`sending to email lambda`);
					await this.sendToEmailLambda(raleonLoyaltyEmails[0], message);
				}

			} catch (error) {
				console.error("Error processing message:", error);
			}
		}

		return {
			statusCode: 200,
			body: JSON.stringify("Messages processed successfully!"),
		};
	}

	private async getEnabledIntegrations(orgId: number): Promise<number[]> {
		console.log(`getting enabled integrations`);

		//TODO: uncomment this code when we send events to other integrations
		// const integrations = (await getEnabledIntegrationsByOrg(orgId)) || [];

		let enabledIntegrations = [];
		// if (integrations?.length) {
		// 	enabledIntegrations = integrations.map((integration: any) => parseInt(integration.integrationId)); //not sure if we need to parse int here or not. I think it's a string from db
		// }
		
		const isKlaviyoEnabled = await getKlaviyoIntegrationByOrg(orgId);
		if (isKlaviyoEnabled) {
			enabledIntegrations.push(2);
		}

		const isSendlaneEnabled = await getSendlaneIntegrationByOrg(orgId);
		if (isSendlaneEnabled) {
			enabledIntegrations.push(7);
		}

		console.log(`enabled integrations: ${enabledIntegrations}`)

		return enabledIntegrations;
	}

	private async getRaleonEmailConfiguration(orgId: number, eventName: string): Promise<any> {
		const loyaltyEvents = await getConfiguredRaleonEmails(orgId, eventName);
		if (!loyaltyEvents || loyaltyEvents.length === 0) {
			return [];
		}
		return loyaltyEvents;
	}

	private async sendToEmailLambda(emailConfig: any, eventData: any): Promise<void> {
		await this.sqsClient.sendMessage({
			QueueUrl: process.env.LOYALTY_EMAIL_QUEUE_URL as string,
			MessageBody: JSON.stringify({ emailConfig, eventData }),
		}).promise();
	}
}

export const handler = new Handler();
export const main = handler.main.bind(handler);

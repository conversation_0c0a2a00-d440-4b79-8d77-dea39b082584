import { APIGatewayProxyResultV2 } from 'aws-lambda';
import { getAPIaccessToken, getCustomerEmails, getShopInfoByOrgId } from '../../utils/shopify-helper';
import { IIntegrationProcessor } from './integration-processor-interface';
import { decrypt, getSendlaneCustomIntegrationByOrg, getSendlaneIntegrationByOrg } from '../../utils/raleon-helper';
import { GetSecretValueCommand, SecretsManagerClient } from '@aws-sdk/client-secrets-manager';
import { EVENT_NAMES } from '../streaming-factory';

export class SendlaneProcessor implements IIntegrationProcessor {
	private batchedEventTypes = [EVENT_NAMES.OFFER_RECEIVED];

	async process(message: any): Promise<APIGatewayProxyResultV2> {
		console.log("Processing Order:", message);

		if (this.batchedEventTypes.includes(message.eventName)) {
			return await this.batchProcess(message);
		}
		const [sendlaneKey, sendlaneCustomIntegrationToken] = await this.getSendlaneIntegration(message.orgId);
		const shopInfo = await getShopInfoByOrgId(message.orgId);
		const shopifyAccessToken = await getAPIaccessToken(shopInfo.shopDomain);

		if (!sendlaneKey) {
			console.error(`Sendlane key not available for org ${message.orgId}`);
			return {
				body: "Sendlane key not found",
				statusCode: 404,
			};
		}

		if (!message.customerId) {
			throw new Error('Customer ID is required to send events to Sendlane')
		}
		const customerEmailsResponse = await getCustomerEmails(
			shopInfo.shopDomain,
			shopifyAccessToken,
			[message.customerId]
		);

		if (customerEmailsResponse.statusCode !== 200) {
			console.error('Failed to get customer emails:', customerEmailsResponse.body);
			return customerEmailsResponse;
		}

		const customers = JSON.parse(customerEmailsResponse.body).customers;

		if (customers.length === 0) {
			console.log("No customers found for given IDs.");
			if (message.eventName !== 'referral_received') {
				return {
					body: "No customers found",
					statusCode: 404,
				};
			}
		}

		await this.createEvent(sendlaneKey, sendlaneCustomIntegrationToken, message.eventName);

		const customFields = await this.updateAndGetSendlaneCustomFieldTypes(sendlaneKey, this.RALEON_CUSTOM_FIELD_NAMES);

		for (const customer of customers) {
			await this.sendEventToSendlane(sendlaneKey, sendlaneCustomIntegrationToken, customer.email, message, customFields);
		}

		return {
			body: "Events sent successfully to Sendlane",
			statusCode: 200,
		};
	}

	async batchProcess(message: any): Promise<APIGatewayProxyResultV2> {
		console.log("Processing batch of orders:", message);
		const [sendlaneKey, sendlaneCustomIntegrationToken] = await this.getSendlaneIntegration(message.orgId);
		const shopInfo = await getShopInfoByOrgId(message.orgId);
		const shopifyAccessToken = await getAPIaccessToken(shopInfo.shopDomain);

		if (!sendlaneKey) {
			console.error(`Sendlane key not available for org ${message.orgId}`);
			return {
				body: "Sendlane key not found",
				statusCode: 404,
			};
		}

		let customerIds: string[] = [];
		if (!message.data || typeof message.data === 'object' || !message.data.length) {
			customerIds.push(message.customerId);
		} else {
			customerIds = message.data.map((data: any) => data.customerId);
		}
		
		const customerEmailsResponse = await getCustomerEmails(
			shopInfo.shopDomain,
			shopifyAccessToken,
			customerIds
		);

		if (customerEmailsResponse.statusCode !== 200) {
			console.error('Failed to get customer emails:', customerEmailsResponse.body);
			return customerEmailsResponse;
		}

		const customers = JSON.parse(customerEmailsResponse.body).customers;

		if (customers.length === 0) {
			console.log("No customers found for given IDs.");
			return {
				body: "No customers found",
				statusCode: 404,
			};
		}

		const customerIdToEmailMap = new Map<string, string>();
		for (const customer of customers) {
			customerIdToEmailMap.set(customer.id.toString(), customer.email);
		}


		const eventsPayload = this.createEventsPayload(message, customerIdToEmailMap);
		const eventTypes = Array.from(new Set(eventsPayload.map((x: any) => x.eventName)));
		const existingEvents = await this.getEvents(sendlaneKey, sendlaneCustomIntegrationToken);
		const missingEventTypes = eventTypes.filter((eventType: string) => !existingEvents.some((evt: any) => evt.name === eventType));
		
		for (const eventType of missingEventTypes) {
			await this.createEvent(sendlaneKey, sendlaneCustomIntegrationToken, eventType);
		}

		const customFields = await this.updateAndGetSendlaneCustomFieldTypes(sendlaneKey, this.RALEON_CUSTOM_FIELD_NAMES);

		for (const evt of eventsPayload) {
			console.log(`Event sent to Sendlane: ${JSON.stringify(evt)}`);
			this.sendEventToSendlane(sendlaneKey, sendlaneCustomIntegrationToken, evt.attributes.profile.data.attributes.email, evt, customFields);
		}

		return {
			body: "Bulk events sent successfully to Sendlane",
			statusCode: 200,
		};
	}

	private createEventsPayload(message: any, customers: Map<string, string>): any[] {
		const payloads: any[] = [];
		
		let dataMap = new Map<string, any>();
		if (Array.isArray(message.data)) {
			dataMap = new Map<string, any>(
				message.data.map((data: any) => [data.customerId, data])
			);
		} else if (message.data && typeof message.data === 'object') {
			dataMap.set(message.customerId, message.data);
		} else {
			console.error("Invalid data format in message.data");
		}
		
		for (const [customerId, email] of customers.entries()) {
			let customerData = dataMap.get(customerId);
			if (customerData) {
				payloads.push({
					type: 'event-bulk-create',
					attributes: {
						profile: {
							data: {
								type: 'profile',
								attributes: {
									email: email
								}
							}
						},
						events: {
							data: [
								{
									type: 'event',
									attributes: {
										properties: {
											...customerData,
										},
										time: message.timestamp,
										metric: { data: { type: 'metric', attributes: { name: message.friendlyName } } }
									}
								}
							]
						}
					}
				});
			}
		}
	
		return payloads;
	}
	
	private chunkArray(array: any[], chunkSize: number): any[][] {
		const results = [];
		while (array.length) {
			results.push(array.splice(0, chunkSize));
		}
		return results;
	}

	



	private async getSendlaneIntegration(orgId: number): Promise<[string, string]> {
		const sendlaneKeyForOrg = await getSendlaneIntegrationByOrg(orgId);
		if (!sendlaneKeyForOrg) {
			console.error(`Sendlane integration not found for org ${orgId}`);
			throw new Error("Sendlane integration not found");
		}

		const sendlaneCustomIntegrationTokenForOrg = await getSendlaneCustomIntegrationByOrg(orgId);
		if (!sendlaneKeyForOrg) {
			console.error(`Sendlane custom integration not found for org ${orgId}`);
			throw new Error("Sendlane custom integration not found");
		}

		// return sendlaneKeyForOrg;
		const sendlaneSecretKey = await this.getIntegrationSecret(sendlaneKeyForOrg.secretkeyid);
		const sendlaneApiKey = decrypt(sendlaneKeyForOrg.value, sendlaneSecretKey);

		console.log(`Sendlane API Key: ${sendlaneApiKey}`);

		const sendlaneCustomSecretKey = await this.getIntegrationSecret(sendlaneCustomIntegrationTokenForOrg.secretkeyid);
		const sendlaneCustomIntegrationToken = decrypt(sendlaneCustomIntegrationTokenForOrg.value, sendlaneCustomSecretKey);

		console.log(`Sendlane Custom Integration Token: ${sendlaneCustomIntegrationToken}`);

		return [sendlaneApiKey, sendlaneCustomIntegrationToken];
	}

	private async sendEventToSendlane(apiKey: string, token: string, email: string, message: any, customFields?: Array<{id: number, name: string}>): Promise<boolean> {
		

		if (message.eventName === 'referral_received') {
			// let profile = await this.getSendlaneProfile(apiKey, message.data.email);
			// if (!profile || !profile.data?.length) {
			// 	profile = await this.createSendlaneProfile(apiKey, message.data.email, message);
			// 	if (!profile.data?.attributes?.email) {
			// 		console.error('Failed to create profile in Sendlane');
			// 		return false;
			// 	}
			// 	email = profile.data.attributes.email;
			// } else {
			// 	email = profile.data[0].attributes.email;
			// }
			await this.getOrFindSendlaneContact(apiKey, email, undefined, customFields);
		}

		// const url = 'https://api.sendlane.com/v2/tracking/event';
		// const payload: any = {
		// 	token,
		// 	email,
		// 	data: {
		// 		properties: {
		// 			[message.friendlyName]: message.eventName,
		// 			time: message.timestamp
		// 		},
		// 		metric: {
		// 			data: {
		// 				type: "metric",
		// 				attributes: { name: message.friendlyName }
		// 			}
		// 		},
		// 		profile: {
		// 			data: {
		// 				type: "profile",
		// 				attributes: {
		// 					email: email,
		// 					properties: {}
		// 				}
		// 			}
		// 		}
		// 	}
		// };

		// if (message.data) {
		// 	for (const data in message.data) {
		// 		payload.data.attributes.properties[data] = message.data[data];
		// 	}
		// }

		if (message.eventName === 'customer_birthday') {
			this.getOrFindSendlaneContact(apiKey, email, {
				[this.RALEON_BIRTHDAY]: message.data.birthday
			}, customFields);
		}
		if (message.eventName === 'vip_tier_updated') {
			this.getOrFindSendlaneContact(apiKey, email, {
				[this.RALEON_VIP_TIER]: message.data.vipTier
			}, customFields);
		}
		if (message.eventName === 'point_balance_change') {
			this.getOrFindSendlaneContact(apiKey, email, {
				[this.RALEON_POINTS]: message.data.totalPoints
			}, customFields);
		}
		if (message.eventName === 'next_reward_update') {
			this.getOrFindSendlaneContact(apiKey, email, {
				[this.RALEON_NEXT_REWARD]: message.data.nextReward,
				[this.RALEON_NEXT_REWARD_DEFICIT]: message.data.deficit
			}, customFields);
		}
		// console.log(`sending payload to Sendlane: ${JSON.stringify(payload)}`);

		// const response = await fetch(url, {
		// 	method: 'POST',
		// 	headers: {
		// 		'Content-Type': 'application/json',
		// 		'Authorization': `Bearer ${apiKey}`,
		// 	},
		// 	body: JSON.stringify(payload)
		// });

		// if (!response.ok) {
		// 	console.error(`Failed to send event to Sendlane: ${response.statusText}`);
		// 	return false;
		// }

		await this.sendEvent(apiKey, token, message.eventName, message.data, email);

		console.log('Event sent to Sendlane');

		return true;
	}

	async getIntegrationSecret(secretKeyId: string) {
		const client = new SecretsManagerClient({
			endpoint: "https://vpce-060670840daa313c8-mcqyvy4b.secretsmanager.us-east-1.vpce.amazonaws.com",
			region: "us-east-1",
		});
		const command = new GetSecretValueCommand({
			SecretId: process.env.ORG_INTEGRATION_KEYS_SECRET_ARN,
		});
		const response = await client.send(command);
		const secret = JSON.parse(response.SecretString!);
		return secret[secretKeyId];
	}


	private async createEvent(key: string, token: string, eventName: string) {
		const existingEvents = await this.getEvents(key, token);
		if (existingEvents?.some?.((event: any) => event.name === 'Raleon Test')) {
			return existingEvents.find((event: any) => event.name === 'Raleon Test');
		}

		const url = `https://api.sendlane.com/v2/integrations/custom/${token}/events`;
		const options = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			},
			body: JSON.stringify({
				name: eventName
			})
		};

		const response = await fetch(url, options);

		if (response.status !== 201 || !response.ok) {
			throw new Error('Invalid Sendlane API key');
		}

		const data = await response.json();

		return data.data;
	}

	private async getEvents(key: string, token: string) {
		const url = `https://api.sendlane.com/v2/integrations/custom/${token}/events`;
		const options = {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			}
		};

		const response = await fetch(url, options);
		
		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}


		const data = await response.json();

		return data.data;
	}

	private async sendEvent(key: string, token: string, eventName: string, eventData: any, email: string) {
		const url = `https://api.sendlane.com/v2/tracking/event`;
		const options = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			},
			body: JSON.stringify({
				token,
				custom_event: eventName,
				email,
				data: eventData
			})
		};

		const response = await fetch(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();

		return data.data;
	}


	private async getExistingSendlaneCustomIntegration(key: string) {
		if (!key) {
			throw new Error('Invalid Sendlane API key');
		}

		const url = 'https://api.sendlane.com/v2/integrations/custom';
		const options = {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			}
		};

		const response = await fetch(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();
		const integrations = data.data;

		return integrations.find((integration: any) => integration.name === 'Raleon');
	}

	private async deleteExistingSendlaneCustomIntegration(key: string, token: string) {
		if (!key) {
			throw new Error('Invalid Sendlane API key');
		}

		if (!(await this.getExistingSendlaneCustomIntegration(key))) {
			return;
		}

		const url = `https://api.sendlane.com/v2/integrations/custom/${token}`;
		const options = {
			method: 'DELETE',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			}
		};

		const response = await fetch(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		// const integrations = data.data;

		// return integrations.find((integration: any) => integration.name === 'Raleon');
	}

	private async createSendlaneCustomIntegration(key: string) {
		if (!key) {
			throw new Error('Invalid Sendlane API key');
		}

		const existing = await this.getExistingSendlaneCustomIntegration(key);
		if (existing) {
			return existing;
		}

		const url = 'https://api.sendlane.com/v2/integrations/custom';
		const options = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			},
			body: JSON.stringify({
				name: 'Raleon',
				url: 'https://app.raleon.io',
				description: 'Raleon custom integration',
				default_sync_list: 0,
				emailProfile: "Billing"
			})
		};

		const response = await fetch(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();


		const testEvent = await this.createSendlaneTestEvent(key, data.data.token, );
		await this.sendSendlaneTestEvent(testEvent, key, data.data.token);

		return data.data;
	}

	private async createSendlaneTestEvent(key: string, token: string) {
		const existingEvents = await this.getSendlaneEvents(key, token);
		if (existingEvents?.some?.((event: any) => event.name === 'Raleon Test')) {
			return existingEvents.find((event: any) => event.name === 'Raleon Test');
		}

		const url = `https://api.sendlane.com/v2/integrations/custom/${token}/events`;
		const options = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			},
			body: JSON.stringify({
				name: 'Raleon Test'
			})
		};

		const response = await fetch(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();

		return data.data;
	}

	private async getSendlaneEvents(key: string, token: string) {
		const url = `https://api.sendlane.com/v2/integrations/custom/${token}/events`;
		const options = {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			}
		};

		const response = await fetch(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();

		return data.data;
	}

	private async sendSendlaneTestEvent(event: any, key: string, token: string) {
		const url = `https://api.sendlane.com/v2/tracking/event`;
		const options = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			},
			body: JSON.stringify({
				token,
				custom_event: 'Raleon Test',
				data: {
					test: 'test'
				}
			})
		};

		const response = await fetch(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();

		return data.data;
	}

	// private async getSendlaneLists(key: string) {
	// 	const url = `https://api.sendlane.com/v2/lists`;
	// 	const options = {
	// 		method: 'GET',
	// 		headers: {
	// 			'Content-Type': 'application/json',
	// 			Authorization: `Bearer ${key}`
	// 		}
	// 	};

	// 	const response = await fetch(url, options);

	// 	if (response.status !== 200 || !response.ok) {
	// 		throw new HttpErrors.Unauthorized('Invalid Sendlane API key');
	// 	}

	// 	const data = await response.json();

	// 	return data.data;
	// }


	private async deleteSendlaneContactList(key: string, listId: number) {
		const url = `https://api.sendlane.com/v2/lists/${listId}`;
		const options = {
			method: 'DELETE',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			}
		};

		const response = await fetch(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();

		return data.data;
	}

	private async createSendlaneContactList(key: string) {
		const url = `https://api.sendlane.com/v2/lists`;
		const options = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			},
			body: JSON.stringify({
				name: 'Raleon',
				sender_id: 1
			})
		};

		const response = await fetch(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();

		return data.data;
	}


	private async getSendlaneLists(key: string) {
		const url = `https://api.sendlane.com/v2/lists`;
		const options = {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			}
		};

		const response = await fetch(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();

		return data.data;
	}

	private async getSendlaneCustomFields(key: string) {
		const url = `https://api.sendlane.com/v2/custom-fields`;
		const options = {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			}
		};

		const response = await fetch(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();

		return data.data;
	}


	private async createSendlaneCustomField(key: string, name: string, type: 'string') {
		const url = `https://api.sendlane.com/v2/custom-fields`;
		const options = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			},
			body: JSON.stringify({
				name,
				// type
			})
		};

		const response = await fetch(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();

		return data.data;
	}




	private async createSendlaneListContact(key: string, listId: number, email: string) {
		const url = `https://api.sendlane.com/v2/lists/${listId}/contacts`;
		const options = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			},
			body: JSON.stringify({ contacts: [{
				email,
				// "first_name": "string",
				// "last_name": "string",
				// "phone": "+19105555555",
				// email_consent: false,
				// sms_consent: [],
				// tag_ids: [],
				// tagNames: [],
				// custom_fields: []
			}]})
		};

		const response = await fetch(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();

		return data.data;
	}



	private async updateSendlaneContactCustomFields(key: string, contactId: number, customFields: Array<{ id: number, value: string }>) {
		const url = `https://api.sendlane.com/v2/contacts/${contactId}/custom-fields`;
		const options = {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			},
			body: JSON.stringify({ custom_fields: customFields })
		};

		const response = await fetch(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();

		return data.data;
	}



	private async getSendlaneContactByEmail(key: string, email: string) {
		const url = `https://api.sendlane.com/v2/contacts/search?email=${email}`;
		const options = {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			},
			// body: JSON.stringify({ email })
		};

		const response = await fetch(url, options);

		if (response.status === 422 || response.status === 404) {
			return undefined;
		}

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();

		return data.data;
	}

	private async getSendlaneListContacts(key: string, listId: number) {
		const url = `https://api.sendlane.com/v2/lists/${listId}/contacts`;
		const options = {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${key}`
			}
		};

		const response = await fetch(url, options);

		if (response.status < 200 || response.status > 299 || !response.ok) {
			throw new Error(`Sendlane API Error: ${response.status} - ${response.statusText}`);
		}

		const data = await response.json();

		return data.data;
	}

	private async updateAndGetSendlaneCustomFieldTypes(apiKey: string, customFieldNames: Array<string>) {
		const existingCustomFields = await this.getSendlaneCustomFields(apiKey);
		for (const customFieldName of customFieldNames) {
			let matchingCustomField = existingCustomFields.find((field: any) => field.name === customFieldName);
			if (!matchingCustomField) {
				matchingCustomField = await this.createSendlaneCustomField(apiKey, customFieldName, 'string');

				existingCustomFields.push(matchingCustomField);
			}
		}

		return existingCustomFields;
	}

	private async getOrFindSendlaneContact(apiKey: string, email: string, customFields: { [name: string]: string } = {}, existingCustomFields?: Array<{id: number, name: string}>) {
		const existingLists = await this.getSendlaneLists(apiKey);
		const raleonList = existingLists.find((list: any) => list.name === 'Raleon');
		if (raleonList) {
			let contact = await this.getSendlaneContactByEmail(apiKey, email);
			if (!contact) {
				contact = await this.createSendlaneListContact(apiKey, raleonList.id, email);
			}

			existingCustomFields = (existingCustomFields || await this.getSendlaneCustomFields(apiKey));

			const fieldsToUpdate = [];
			for (const customFieldName in customFields) {
				let matchingCustomField = existingCustomFields!.find((field: any) => field.name === customFieldName);
				if (!matchingCustomField) {
					matchingCustomField = await this.createSendlaneCustomField(apiKey, customFieldName, 'string');
				}

				fieldsToUpdate.push({ id: matchingCustomField!.id, value: customFields[customFieldName] });
			}

			if (!fieldsToUpdate.length) {
				return
			}

			await this.updateSendlaneContactCustomFields(apiKey, contact.id, fieldsToUpdate);
		}
	}

	readonly RALEON_BIRTHDAY = 'Raleon - Birthday';
	readonly RALEON_VIP_TIER = 'Raleon - VIP Tier';
	readonly RALEON_POINTS = 'Raleon Loyalty Points';
	readonly RALEON_OFFER_RECEIVED = 'Raleon Offer Received';
	readonly RALEON_NEXT_REWARD = 'Raleon Next Reward';
	readonly RALEON_NEXT_REWARD_DEFICIT = 'Raleon Next Reward Point Deficit';
	readonly RALEON_CUSTOM_FIELD_NAMES = [
		this.RALEON_BIRTHDAY,
		this.RALEON_VIP_TIER,
		this.RALEON_POINTS,
		this.RALEON_OFFER_RECEIVED,
		this.RALEON_NEXT_REWARD,
		this.RALEON_NEXT_REWARD_DEFICIT
	];

}
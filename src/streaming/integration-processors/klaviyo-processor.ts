import { APIGatewayProxyResultV2 } from 'aws-lambda';
import { getAPIaccessToken, getCustomerEmails, getShopInfoByOrgId } from '../../utils/shopify-helper';
import { IIntegrationProcessor } from './integration-processor-interface';
import { decrypt, getKlaviyoIntegrationByOrg } from '../../utils/raleon-helper';
import { GetSecretValueCommand, SecretsManagerClient } from '@aws-sdk/client-secrets-manager';
import { EVENT_NAMES } from '../streaming-factory';

export class KlaviyoProcessor implements IIntegrationProcessor {
	private batchedEventTypes = [EVENT_NAMES.OFFER_RECEIVED];

	async process(message: any): Promise<APIGatewayProxyResultV2> {
		console.log("Processing Order:", message);

		if (this.batchedEventTypes.includes(message.eventName)) {
			return await this.batchProcess(message);
		}
		const klaviyoKey = await this.getKlaviyoIntegration(message.orgId);
		const shopInfo = await getShopInfoByOrgId(message.orgId);
		const shopifyAccessToken = await getAPIaccessToken(shopInfo.shopDomain);

		if (!klaviyoKey) {
			console.error(`Klaviyo key not available for org ${message.orgId}`);
			return {
				body: "Klaviyo key not found",
				statusCode: 404,
			};
		}

		if (!message.customerId) {
			throw new Error('Customer ID is required to send events to Klaviyo')
		}
		const customerEmailsResponse = await getCustomerEmails(
			shopInfo.shopDomain,
			shopifyAccessToken,
			[message.customerId]
		);

		if (customerEmailsResponse.statusCode !== 200) {
			console.error('Failed to get customer emails:', customerEmailsResponse.body);
			return customerEmailsResponse;
		}

		const customers = JSON.parse(customerEmailsResponse.body).customers;

		if (customers.length === 0) {
			console.log("No customers found for given IDs.");
			if (message.eventName !== 'referral_received') {
				return {
					body: "No customers found",
					statusCode: 404,
				};
			}
		}

		for (const customer of customers) {
			await this.sendEventToKlaviyo(klaviyoKey, customer.email, message);
		}

		return {
			body: "Events sent successfully to Klaviyo",
			statusCode: 200,
		};
	}

	async batchProcess(message: any): Promise<APIGatewayProxyResultV2> {
		console.log("Processing batch of orders:", message);
		const klaviyoKey = await this.getKlaviyoIntegration(message.orgId);
		const shopInfo = await getShopInfoByOrgId(message.orgId);
		const shopifyAccessToken = await getAPIaccessToken(shopInfo.shopDomain);

		if (!klaviyoKey) {
			console.error(`Klaviyo key not available for org ${message.orgId}`);
			return {
				body: "Klaviyo key not found",
				statusCode: 404,
			};
		}

		let customerIds: string[] = [];
		if (!message.data || typeof message.data === 'object' || !message.data.length) {
			customerIds.push(message.customerId);
		} else {
			customerIds = message.data.map((data: any) => data.customerId);
		}
		
		const customerEmailsResponse = await getCustomerEmails(
			shopInfo.shopDomain,
			shopifyAccessToken,
			customerIds
		);

		if (customerEmailsResponse.statusCode !== 200) {
			console.error('Failed to get customer emails:', customerEmailsResponse.body);
			return customerEmailsResponse;
		}

		const customers = JSON.parse(customerEmailsResponse.body).customers;

		if (customers.length === 0) {
			console.log("No customers found for given IDs.");
			return {
				body: "No customers found",
				statusCode: 404,
			};
		}

		const customerIdToEmailMap = new Map<string, string>();
		for (const customer of customers) {
			customerIdToEmailMap.set(customer.id.toString(), customer.email);
		}

		const eventsPayload = this.createEventsPayload(message, customerIdToEmailMap);
		const chunks = this.chunkArray(eventsPayload, 900);
		for (const chunk of chunks) {
			await this.sendBulkEventsToKlaviyo(klaviyoKey, chunk);
		}

		return {
			body: "Bulk events sent successfully to Klaviyo",
			statusCode: 200,
		};
	}

	private createEventsPayload(message: any, customers: Map<string, string>): any[] {
		const payloads: any[] = [];
		
		let dataMap = new Map<string, any>();
		if (Array.isArray(message.data)) {
			dataMap = new Map<string, any>(
				message.data.map((data: any) => [data.customerId, data])
			);
		} else if (message.data && typeof message.data === 'object') {
			dataMap.set(message.customerId, message.data);
		} else {
			console.error("Invalid data format in message.data");
		}
		
		for (const [customerId, email] of customers.entries()) {
			let customerData = dataMap.get(customerId);
			if (customerData) {
				payloads.push({
					type: 'event-bulk-create',
					attributes: {
						profile: {
							data: {
								type: 'profile',
								attributes: {
									email: email
								}
							}
						},
						events: {
							data: [
								{
									type: 'event',
									attributes: {
										properties: {
											...customerData,
										},
										time: message.timestamp,
										metric: { data: { type: 'metric', attributes: { name: message.friendlyName } } }
									}
								}
							]
						}
					}
				});
			}
		}
	
		return payloads;
	}
	
	private chunkArray(array: any[], chunkSize: number): any[][] {
		const results = [];
		while (array.length) {
			results.push(array.splice(0, chunkSize));
		}
		return results;
	}

	private async sendBulkEventsToKlaviyo(apiKey: string, events: any[]): Promise<void> {
		const url = 'https://a.klaviyo.com/api/event-bulk-create-jobs/';
		const payload = {
			data: {
				type: 'event-bulk-create-job',
				attributes: {
					'events-bulk-create': {
						data: events
					}
				}
			}
		};
		console.log('posting request to url:', url);
		const response = await fetch(url, {
			method: 'POST',
			headers: {
				'accept': 'application/json',
				'revision': '2024-07-15',
				'content-type': 'application/json',
				'Authorization': `Klaviyo-API-Key ${apiKey}`
			},
			body: JSON.stringify(payload)
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error(`Failed to send bulk events to Klaviyo: ${response.status} ${response.statusText} ${errorText}`);
			throw new Error(`Failed to send bulk events to Klaviyo: ${response.status} ${response.statusText} ${errorText}`);
		}

		console.log('Bulk events sent to Klaviyo successfully');
	}



	private async getKlaviyoIntegration(orgId: number): Promise<string> {
		const klaviyoKeyForOrg = await getKlaviyoIntegrationByOrg(orgId);
		if (!klaviyoKeyForOrg) {
			console.error(`Klaviyo integration not found for org ${orgId}`);
			throw new Error("Klaviyo integration not found");
		}
		const klaviyoSecretKey = await this.getIntegrationSecret(klaviyoKeyForOrg.secretkeyid);
		return decrypt(klaviyoKeyForOrg.value, klaviyoSecretKey);
	}

	private async sendEventToKlaviyo(apiKey: string, email: string, message: any): Promise<boolean> {
		if (message.eventName === 'referral_received') {
			let profile = await this.getKlaviyoProfile(apiKey, message.data.email);
			if (!profile || !profile.data?.length) {
				profile = await this.createKlaviyoProfile(apiKey, message.data.email, message);
				if (!profile.data?.attributes?.email) {
					console.error('Failed to create profile in Klaviyo');
					return false;
				}
				email = profile.data.attributes.email;
			} else {
				email = profile.data[0].attributes.email;
			}
		}

		const url = 'https://a.klaviyo.com/api/events';
		const payload: any = {
			data: {
				type: "event",
				attributes: {
					properties: {
						[message.friendlyName]: message.eventName,
						time: message.timestamp
					},
					metric: {
						data: {
							type: "metric",
							attributes: { name: message.friendlyName }
						}
					},
					profile: {
						data: {
							type: "profile",
							attributes: {
								email: email,
								properties: {}
							}
						}
					}
				}
			}
		};

		if (message.data) {
			for (const data in message.data) {
				payload.data.attributes.properties[data] = message.data[data];
			}
		}

		if (message.eventName === 'customer_birthday') {
			payload.data.attributes.profile.data.attributes.properties[this.RALEON_BIRTHDAY] = message.data.birthday;
		}
		if (message.eventName === 'vip_tier_updated') {
			payload.data.attributes.profile.data.attributes.properties[this.RALEON_VIP_TIER] = message.data.vipTier;
		}
		if (message.eventName === 'point_balance_change') {
			payload.data.attributes.profile.data.attributes.properties[this.RALEON_POINTS] = message.data.totalPoints;
		}
		if (message.eventName === 'next_reward_update') {
			payload.data.attributes.profile.data.attributes.properties[this.RALEON_NEXT_REWARD] = message.data.nextReward;
			payload.data.attributes.profile.data.attributes.properties[this.RALEON_NEXT_REWARD_DEFICIT] = message.data.deficit;
		}
		console.log(`sending payload to klaviyo: ${JSON.stringify(payload)}`);

		const response = await fetch(url, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'Authorization': `Klaviyo-API-Key ${apiKey}`,
				'Revision': this.KLAVIYO_API_VERSION,
			},
			body: JSON.stringify(payload)
		});

		if (!response.ok) {
			console.error(`Failed to send event to Klaviyo: ${response.statusText}`);
			return false;
		}

		console.log('Event sent to Klaviyo');

		return true;
	}

	private async getKlaviyoProfile(apiKey: string, email: string): Promise<any> {
		const url = `https://a.klaviyo.com/api/profiles?filter=equals(email,"${encodeURIComponent(email)}")`;

		const response = await fetch(url, {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				'Authorization': `Klaviyo-API-Key ${apiKey}`,
				'Revision': this.KLAVIYO_API_VERSION,
			}
		});

		const responseJson = await response.json();
		if (!response.ok) {
			console.error(`Failed to fetch profile from Klaviyo: ${JSON.stringify(responseJson)}`);
			return null;
		}

		return responseJson;
	}

	private async createKlaviyoProfile(apiKey: string, email: string, message: any): Promise<any> {
		const payload: any = {
			data: {
				type: 'profile',
				attributes: {
					email: decodeURIComponent(message.data.email),
				}
			}
		};

		const response = await fetch('https://a.klaviyo.com/api/profiles', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'Authorization': `Klaviyo-API-Key ${apiKey}`,
				'Revision': this.KLAVIYO_API_VERSION,
			},
			body: JSON.stringify(payload),
		});

		const responseJson = await response.json();
		if (!response.ok) {
			console.error(`Failed to create profile in Klaviyo: ${JSON.stringify(responseJson)}`);
			return false;
		}
		return responseJson;
	}

	async getIntegrationSecret(secretKeyId: string) {
		const client = new SecretsManagerClient({
			endpoint: "https://vpce-060670840daa313c8-mcqyvy4b.secretsmanager.us-east-1.vpce.amazonaws.com",
			region: "us-east-1",
		});
		const command = new GetSecretValueCommand({
			SecretId: process.env.ORG_INTEGRATION_KEYS_SECRET_ARN,
		});
		const response = await client.send(command);
		const secret = JSON.parse(response.SecretString!);
		return secret[secretKeyId];
	}

	readonly RALEON_BIRTHDAY = 'Raleon - Birthday';
	readonly RALEON_VIP_TIER = 'Raleon - VIP Tier';
	readonly RALEON_POINTS = 'Raleon Loyalty Points';
	readonly RALEON_OFFER_RECEIVED = 'Raleon Offer Received';
	readonly RALEON_NEXT_REWARD = 'Raleon Next Reward';
	readonly RALEON_NEXT_REWARD_DEFICIT = 'Raleon Next Reward Point Deficit';
	readonly KLAVIYO_API_VERSION = '2024-02-15';

}
import { APIGatewayProxyEvent, APIGatewayProxyResultV2 } from "aws-lambda";
import { PutObjectCommand, S3Client } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";


export class Handler {
	async main(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResultV2> {
		console.log(JSON.stringify(event, null, 2));

		if (!event.queryStringParameters!.orgId || !event.queryStringParameters!.fileName) {
			return {
				statusCode: 400,
				body: 'Missing required query parameters: orgId, fileName'
			};
		}

		const url = await this.getPresignedDownloadUrl({
			orgId: event.queryStringParameters!.orgId,
			fileName: event.queryStringParameters!.fileName,
		} as ImageUploadData);
		
		return {
			statusCode: 200,
			body: url
		};
	}

	private async getPresignedDownloadUrl(uploadData: ImageUploadData): Promise<string> {
        const s3Client = new S3Client({ region: process.env.AWS_REGION });

        const command = new PutObjectCommand({
            Bucket: process.env.IMAGE_BUCKET_NAME!,
            Key: `images/organization=${uploadData.orgId}/${uploadData.fileName}`,
        });

        return await getSignedUrl(s3Client, command, { expiresIn: 60 });
    }
}

export interface ImageUploadData {
	orgId: string;
	fileName: string;
}

export const handler = new Handler();
export const main = handler.main.bind(handler);
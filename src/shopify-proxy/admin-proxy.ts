import { APIGatewayProxyResultV2 } from 'aws-lambda';
import fetch from 'node-fetch';
import { GetSecretValueCommand, SecretsManagerClient } from '@aws-sdk/client-secrets-manager';
import { getAPIaccessToken } from '../utils/shopify-helper';
export class Handler {

	async main(event: any, context: any): Promise<APIGatewayProxyResultV2> {
		console.log("event 👉", JSON.stringify(event, null, 2));

		if (event.headers && !event.headers['X-Shopify-Access-Token']) {
			return {
				statusCode: 400,
				body: JSON.stringify({
					message: 'Must specify the shopify access token in the X-Shopify-Access-Token header'
				})
			}
		}

		if (!event.headers['X-Shopify-Domain']) {
			return {
				statusCode: 400,
				body: JSON.stringify({
					message: 'Must specify the shopify store domain in the X-Shopify-Domain header'
				})
			}
		}

		let accessToken;
		try {
			accessToken = await getAPIaccessToken(event.headers['X-Shopify-Domain']);
		} catch (e) {
			console.error("Error getting access token:", e);
			return {
				statusCode: 400,
				body: JSON.stringify({
					message: (e as Error).message || 'Error getting access token'
				})
			}
		}

		if (!accessToken || event.headers['X-Shopify-Access-Token'] !== accessToken) {
			return {
				statusCode: 403,
				body: JSON.stringify({
					message: 'Invalid Access Token'
				})
			}
		}

		const apiVersionMatch = event.path.match(/20\d{2}-\d{2}/); //matches 20**-** to get the api version
		if (apiVersionMatch != process.env.LATEST_SHOPIFY_API_VERSION) {
			return {
				statusCode: 400,
				body: JSON.stringify({
					message: `You must use the latest api version which is set to ${process.env.LATEST_SHOPIFY_API_VERSION}`
				})
			}
		}

		let url = `https://${event.headers['X-Shopify-Domain']}${event.path}`;
		if (event.queryStringParameters) {
			for (const param of Object.keys(event.queryStringParameters)) {
				url += `${url.includes('?') ? '&' : '?'}${param}=${event.queryStringParameters[param]}`;
			}
		}
		
		return await this.forwardRequest(
			url,
			event.httpMethod,
			accessToken,
			event.body || undefined
		);
	}

	async forwardRequest(
		url: string,
		method: string,
		accessToken: string,
		body?: string,
	): Promise<APIGatewayProxyResultV2> {
		try {
			const options: any = {
				method,
				headers: {
					"Content-Type": "application/json",
					"X-Shopify-Access-Token": accessToken,
				},
			};
			if (body) options.body = body;
			const response = await fetch(url, options);
			const contentType = response.headers.get('content-type');
			
			let result: APIGatewayProxyResultV2 = {
				statusCode: response.status,
				headers: { 'Content-Type': contentType || 'application/json' },
				body: ''
			};
			
			if (contentType?.includes('application/json')) {
				result.body = JSON.stringify(await response.json());
			} else {
				const rawBody = await response.text();
				try {
					result.body = JSON.stringify(JSON.parse(rawBody));
				} catch(e) {
					result.body = rawBody;
				}
			}
			
			return result;
		} catch (e) {
			console.error("Error during forwardRequest:", e);
			return {
				statusCode: 502,
				body: 'Error forwarding request',
			};
		}
	}
}
export const handler = new Handler();
export const main = handler.main.bind(handler);

import { APIGatewayProxyResultV2 } from 'aws-lambda';
import fetch from 'node-fetch';
import * as crypto from 'crypto';
import { GetSecretValueCommand, SecretsManagerClient } from '@aws-sdk/client-secrets-manager';
import * as jwt from 'jsonwebtoken';
import { getLoyaltyEvents, getOrgIdByShopifyDomain } from '../utils/raleon-helper';

export class Handler {

	private secret: any;
	private loyaltyEvents: any;

	async main(event: any, context: any): Promise<APIGatewayProxyResultV2> {
		console.log("event 👉", JSON.stringify(event, null, 2));
		
		this.secret = await this.getShopifyAPISecret();
		this.loyaltyEvents = await getLoyaltyEvents();

		const signature = event.queryStringParameters.signature;

		if (!signature) {
			return {
				statusCode: 400,
				body: 'Missing signature'
			}
		}

		this.validateSignature(event.queryStringParameters);
		
		const validEventData = this.validateData(event)
		if (!validEventData) {
			return {
				statusCode: 400,
				body: 'Invalid event data'
			}
		}

		await getOrgIdByShopifyDomain(event.queryStringParameters.shop);
		const body = this.prepareBody(event);
		
		return await this.forwardRequest(
			`${process.env.EVENT_API_URL!}/prod/event`, 
			'POST',
			body,
		);
	}

	async forwardRequest(
		url: string,
		method: string,
		body?: string
	): Promise<APIGatewayProxyResultV2> {
		try {
			const options: any = {
				method,
				headers: { "Content-Type": "application/json" },
			};
			if (body) options.body = body;
			const response = await fetch(url, options);
			const contentType = response.headers.get('content-type');
			
			let result: APIGatewayProxyResultV2 = {
				statusCode: response.status,
				headers: { 'Content-Type': contentType || 'application/json' },
				body: ''
			};
			
			if (contentType?.includes('application/json')) {
				result.body = JSON.stringify(await response.json());
			} else {
				const rawBody = await response.text();
				try {
					result.body = JSON.stringify(JSON.parse(rawBody));
				} catch(e) {
					result.body = rawBody;
				}
			}
			
			return result;
		} catch (e) {
			console.error("Error during forwardRequest:", e);
			return {
				statusCode: 502,
				body: 'Error forwarding request',
			};
		}
	}

	private validateSignature(queryStringParams: any): void {
		const signature = queryStringParams.signature;
		delete queryStringParams.signature;
		const sortedParams = Object.keys(queryStringParams)
			.sort()
			.map((key: string) => `${key}=${queryStringParams[key]}`)
			.join('');

		const calculatedSignature = crypto
			.createHmac('sha256', this.secret)
			.update(sortedParams)
			.digest('hex');

		if (calculatedSignature !== signature) {
			throw new Error('Invalid signature');
		}
	}

	private validateData(event: any): boolean {
		if (!event.body) {
			console.error('Missing event body');
			return false;
		}

		if (typeof event.body === 'string') {
			try {
				event.body = JSON.parse(event.body);
			} catch (e) {
				console.error('Invalid event body');
				return false;
			}
		}
		if (!event.body.eventName) {
			console.error('Missing event name');
			return false;
		}

		const eventName = event.body.eventName;
		let found = false;
		for (let loyaltyEvent of this.loyaltyEvents) {
			if (loyaltyEvent.name === eventName) {
				found = true;
				if (!loyaltyEvent.dataStructure) {
					return true;
				}
				return this.validateObject(loyaltyEvent.dataStructure, event.body);
			}
		}

		if (!found) {
			console.error('Invalid event name');
		}
		return false;
	}

	private validateObject(structure: any, data: any): boolean {
		for (let key in structure) {
			if (structure[key].required && !(key in data)) {
				console.error(`Missing required key: ${key}`);
				return false;
			}

			if (key in data) {
				if (typeof data[key] !== structure[key].type) {
					console.error(`Invalid type for key: ${key}`);
					return false;
				}

				if (structure[key].type === 'object' && structure[key].properties) {
					if (!this.validateObject(structure[key].properties, data[key])) {
						console.error(`Invalid properties for key: ${JSON.stringify(data[key])}`);
						return false;
					}
				}
			}
		}
		return true;
	}


	private prepareBody(event: any): string {
		let body = event.body || undefined;
		
		//TODO: will need to dynamically handle these cases somehow
		if (body.data && body.data?.email) {
			body.data.email = encodeURIComponent(body.data.email);
		}
		if (body && !body.customerId) {
			body.customerId = event.queryStringParameters.logged_in_customer_id;
		} else {
			throw new Error('No customer id');
		}
		if (typeof body !== 'string') {
			body = JSON.stringify(body);
		}
		return body;
	}

	private async getShopifyAPISecret() {
		const client = new SecretsManagerClient({
			endpoint: "https://vpce-060670840daa313c8-mcqyvy4b.secretsmanager.us-east-1.vpce.amazonaws.com",
			region: "us-east-1",
		});
		const command = new GetSecretValueCommand({
			SecretId: process.env.SHOPIFY_SECRET_KEY_ARN,
		});
		const response = await client.send(command);
		const secret = JSON.parse(response.SecretString!);
		return secret.SHOPIFY_API_SECRET;
	}
}
export const handler = new Handler();
export const main = handler.main.bind(handler);

import { APIGatewayProxyResultV2 } from 'aws-lambda';
import fetch from 'node-fetch';
import * as crypto from 'crypto';
import { GetSecretValueCommand, SecretsManagerClient } from '@aws-sdk/client-secrets-manager';
import * as jwt from 'jsonwebtoken';

export class Handler {

	private secret: any;

	async main(event: any, context: any): Promise<APIGatewayProxyResultV2> {
		console.log("event 👉", JSON.stringify(event, null, 2));
		
		if (event.path === '/loyalty-enabled' && event.httpMethod == 'GET') {
			return await this.isLoyaltyEnabled(event);
		}

		if (event.path === '/free-gift-enabled' && event.httpMethod == 'GET') {
			return await this.isFreeGiftEnabled(event);
		}

		if(event.path === '/loyalty-preview' && event.httpMethod == 'GET') {
			return await this.getLoyaltyPreview(event);
		}

		if (event.path === '/guest-earns' && event.httpMethod == 'GET') {
			return await this.getGuestEarns(event);
		}
		
		if (event.path.startsWith('/loyalty-translations') && event.httpMethod == 'GET') {
			return await this.getLoyaltyTranslations(event);
		}

		if(event.path === '/launcher-details' && event.httpMethod == 'GET') {
			return await this.getLauncherDetails(event);
		}

		if(event.path.startsWith('/inventory-coupons/uuid') && event.httpMethod == 'POST') {
			return await this.getAnonymousCouponGrant(event);
		}

		this.secret = await this.getShopifyAPISecret();

		const customerId = event.queryStringParameters.logged_in_customer_id;
		const signature = event.queryStringParameters.signature;

		if (!customerId) {
			console.log(`No customer id. Proxy Failed.`);
			return {
				statusCode: 400,
				body: 'Please supply a customer id',
			}
		}

		if (!signature) {
			return {
				statusCode: 400,
				body: 'Missing signature'
			}
		}

		this.validateSignature(event.queryStringParameters);

		return await this.forwardRequest(
			`${process.env.WEBAPP_API_URL!}${event.path}`, 
			event.httpMethod,
			customerId,
			event.queryStringParameters.shop,
			event.body || undefined
		);
	}

	async forwardRequest(
		url: string,
		method: string,
		customerId: string,
		shopDomain: string,
		body?: string
	): Promise<APIGatewayProxyResultV2> {
		const accessToken = this.generateCustomerDigest(customerId, shopDomain);
		try {
			const options: any = {
				method,
				headers: {
					"Content-Type": "application/json",
					Authorization: `Bearer ${accessToken}`,
					'ngrok-skip-browser-warning': true,
				},
			};
			if (body) options.body = body;
			console.log(`Forwarding request to ${url} with method ${method} and body:`, body);
			const response = await fetch(url, options);
			const contentType = response.headers.get('content-type');
			
			let result: APIGatewayProxyResultV2 = {
				statusCode: response.status,
				headers: { 'Content-Type': contentType || 'application/json' },
				body: ''
			};
			
			if (contentType?.includes('application/json')) {
				result.body = JSON.stringify(await response.json());
			} else {
				const rawBody = await response.text();
				try {
					result.body = JSON.stringify(JSON.parse(rawBody));
				} catch(e) {
					result.body = rawBody;
				}
			}
			
			return result;
		} catch (e) {
			console.error("Error during forwardRequest:", e);
			return {
				statusCode: 502,
				body: 'Error forwarding request',
			};
		}
	}

	private validateSignature(queryStringParams: any): void {
		const signature = queryStringParams.signature;
		delete queryStringParams.signature;
		const sortedParams = Object.keys(queryStringParams)
			.sort()
			.map((key: string) => `${key}=${queryStringParams[key]}`)
			.join('');

		const calculatedSignature = crypto
			.createHmac('sha256', this.secret)
			.update(sortedParams)
			.digest('hex');

		if (calculatedSignature !== signature) {
			throw new Error('Invalid signature');
		}
	}

	private async isLoyaltyEnabled(event: any): Promise<APIGatewayProxyResultV2> {
		const orgId = event.queryStringParameters.orgId;
		if (!orgId) {
			return {
				statusCode: 400,
				body: 'Please supply an orgId',
			}
		}

		try {
			const response = await fetch(`${process.env.WEBAPP_API_URL!}/loyalty-enabled?orgId=${orgId}`);
			const contentType = response.headers.get('content-type');
			console.log(`Loyalty enabled response content-type: ${contentType}`);
			
			const responseText = await response.text();
			try {
				const data = JSON.parse(responseText);
				return {
					statusCode: 200,
					body: JSON.stringify(data),
				};
			} catch (e) {
				console.log("Non-JSON response from loyalty-enabled:", responseText);
				throw e;
			}
		} catch (e) {
			console.error("Error in loyalty-enabled:", e);
			return {
				statusCode: 502,
				body: 'Error checking loyalty status'
			};
		}
	}

	private async isFreeGiftEnabled(event: any): Promise<APIGatewayProxyResultV2> {
		const orgId = event.queryStringParameters.orgId;
		if (!orgId) {
			return {
				statusCode: 400,
				body: 'Please supply an orgId',
			}
		}

		try {
			const response = await fetch(`${process.env.WEBAPP_API_URL!}/promotional-campaign/free-gift-enabled?orgId=${orgId}`);
			const contentType = response.headers.get('content-type');
			console.log(`Free gift enabled response content-type: ${contentType}`);
			
			const responseText = await response.text();
			try {
				const data = JSON.parse(responseText);
				return {
					statusCode: 200,
					body: JSON.stringify(data),
				};
			} catch (e) {
				console.log("Non-JSON response from free-gift-enabled:", responseText);
				throw e;
			}
		} catch (e) {
			console.error("Error in free-gift-enabled:", e);
			return {
				statusCode: 502,
				body: 'Error checking free gift status'
			};
		}
	}

	private async getAnonymousCouponGrant(event: any): Promise<APIGatewayProxyResultV2> {
		const response = await fetch(`${process.env.WEBAPP_API_URL!}${event.path}`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({}),
		});
		const data = await response.text();
	
		return {
			statusCode: 200,
			body: data,
		}
	}

	private async getLauncherDetails(event: any): Promise<APIGatewayProxyResultV2> {
		const orgId = event.queryStringParameters.orgId;
		if (!orgId) {
			return {
				statusCode: 400,
				body: 'Please supply an orgId',
			}
		}

		try {
			const response = await fetch(`${process.env.WEBAPP_API_URL!}/launcher-details?orgId=${orgId}`);
			const contentType = response.headers.get('content-type');
			console.log(`Launcher details response content-type: ${contentType}`);
			
			const responseText = await response.text();
			try {
				const data = JSON.parse(responseText);
				return {
					statusCode: 200,
					body: JSON.stringify(data),
				};
			} catch (e) {
				console.log("Non-JSON response from launcher-details:", responseText);
				throw e;
			}
		} catch (e) {
			console.error("Error in launcher-details:", e);
			return {
				statusCode: 502,
				body: 'Error getting launcher details'
			};
		}
	}

	private async getLoyaltyPreview(event: any): Promise<APIGatewayProxyResultV2> {
		const orgId = event.queryStringParameters.orgId;
		if (!orgId) {
			return {
				statusCode: 400,
				body: 'Please supply an orgId',
			}
		}

		try {
			const response = await fetch(`${process.env.WEBAPP_API_URL!}/loyalty-preview?orgId=${orgId}`);
			const contentType = response.headers.get('content-type');
			console.log(`Loyalty preview response content-type: ${contentType}`);
			
			const responseText = await response.text();
			try {
				const data = JSON.parse(responseText);
				return {
					statusCode: 200,
					body: JSON.stringify(data),
				};
			} catch (e) {
				console.log("Non-JSON response from loyalty-preview:", responseText);
				throw e;
			}
		} catch (e) {
			console.error("Error in loyalty-preview:", e);
			return {
				statusCode: 502,
				body: 'Error getting loyalty preview'
			};
		}
	}

	private async getGuestEarns(event: any): Promise<APIGatewayProxyResultV2> {
		const orgId = event.queryStringParameters.orgId;
		if (!orgId) {
			return {
				statusCode: 400,
				body: 'Please supply an orgId',
			}
		}

		try {
			const response = await fetch(`${process.env.WEBAPP_API_URL!}/guest-earns?orgId=${orgId}`);
			const contentType = response.headers.get('content-type');
			console.log(`Guest earns response content-type: ${contentType}`);
			
			const responseText = await response.text();
			try {
				const data = JSON.parse(responseText);
				return {
					statusCode: 200,
					body: JSON.stringify(data),
				};
			} catch (e) {
				console.log("Non-JSON response from guest-earns:", responseText);
				throw e;
			}
		} catch (e) {
			console.error("Error in guest-earns:", e);
			return {
				statusCode: 502,
				body: 'Error getting guest earns'
			};
		}
	}

	private async getLoyaltyTranslations(event: any): Promise<APIGatewayProxyResultV2> {
		const orgId = event.path.split('/').reverse()[0];
		if (!orgId || !Number.isFinite(Number(orgId))) {
			return {
				statusCode: 400,
				body: 'Please supply a valid orgId',
			}
		}

		try {
			const response = await fetch(`${process.env.WEBAPP_API_URL!}/loyalty-translations/${orgId}`);
			const contentType = response.headers.get('content-type');
			console.log(`Loyalty translations response content-type: ${contentType}`);
			
			const responseText = await response.text();
			try {
				const data = JSON.parse(responseText);
				return {
					statusCode: 200,
					body: JSON.stringify(data),
				};
			} catch (e) {
				console.log("Non-JSON response from loyalty-translations:", responseText);
				throw e;
			}
		} catch (e) {
			console.error("Error in loyalty-translations:", e);
			return {
				statusCode: 502,
				body: 'Error getting loyalty translations'
			};
		}
	}

	private generateCustomerDigest(customerId: string, shopDomain: string) {
		return jwt.sign(
			{ customerId, shopDomain },
			this.secret,
			{ expiresIn: '10m' }
		);
	}

	private async getShopifyAPISecret() {
		const client = new SecretsManagerClient({
			endpoint: "https://vpce-060670840daa313c8-mcqyvy4b.secretsmanager.us-east-1.vpce.amazonaws.com",
			region: "us-east-1",
		});
		const command = new GetSecretValueCommand({
			SecretId: process.env.SHOPIFY_SECRET_KEY_ARN,
		});
		const response = await client.send(command);
		const secret = JSON.parse(response.SecretString!);
		return secret.SHOPIFY_API_SECRET;
	}
}
export const handler = new Handler();
export const main = handler.main.bind(handler);

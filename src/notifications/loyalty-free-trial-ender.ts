import { fetchJSON, fetchUnauthenticatedJSO<PERSON> } from "../utils/shopify-helper";


const WEBAPP_API_URL = process.env.WEBAPP_API_URL || 'https://app.raleon.io/api/v1';
export class Handler {

	private isProd: boolean;

	async main(event: any, context: any): Promise<void> {
		this.isProd = context.functionName.includes('-prod');

		await this.updateFreeTrials('');
	}

	async updateFreeTrials(accessToken: string) {
		console.log('sending');
		const response = await fetchUnauthenticatedJSON(
			`${WEBAPP_API_URL}/update-free-trials`,
			"POST"	
		);
		console.log("data 👉", JSON.stringify(response, null, 2));
	}
}

export const handler = new Handler();
export const main = handler.main.bind(handler);

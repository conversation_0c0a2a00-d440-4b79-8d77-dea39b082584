import { EmailRecord } from './loyalty-staged-email';

export class EmailAggregator {

	private aggregatedEmails: EmailRecord[] = [];

	aggregate(stagedEmails: EmailRecord[]): EmailRecord[] {
		const customerEmailRecords = new Map<string, EmailRecord[]>();
		for (const email of stagedEmails) {
			const key = `${email.customerId}-${email.eventName}`;
			if (!customerEmailRecords.has(key)) {
				customerEmailRecords.set(key, []);
			}
			customerEmailRecords.get(key)?.push(email);	
			this.aggregatedEmails.push(email);
		}

		return Array.from(this.aggregateEmailsByType(customerEmailRecords).values());
	}

	getAggregatedEmails(): EmailRecord[] {
		return this.aggregatedEmails;
	}

	private aggregateEmailsByType(customerEmailRecords: Map<string, EmailRecord[]>): Map<string, EmailRecord> {
		const aggregatedEmails = new Map<string, EmailRecord>();
		for (const [key, emails] of customerEmailRecords) {
			if (emails[0].eventName === 'point_balance_change') {
				const aggregatedData = this.aggregatePointBalanceEmails(emails);
				aggregatedEmails.set(key, aggregatedData);
			}
		}
		return aggregatedEmails;
	}

	private aggregatePointBalanceEmails(emails: EmailRecord[]): EmailRecord {
		const pointsEarned = emails.reduce((acc, email) => acc + email.data?.balanceChange!, 0);
		return {
			...emails[0],
			data: {
				...emails[0].data,
				balanceChange: pointsEarned,
			},
		}
	}
}
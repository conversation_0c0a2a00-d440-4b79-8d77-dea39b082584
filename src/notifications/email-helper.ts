import { DynamoDB } from "@aws-sdk/client-dynamodb";
import { SendEmailCommand, SESClient } from "@aws-sdk/client-ses";
import { marshall } from "@aws-sdk/util-dynamodb";
import { getAPIaccessToken, getCustomerEmails, getShopInfoByOrgId } from "../utils/shopify-helper";

export class EmailHelper {

	static sesClient: SESClient = new SESClient({ region: "us-east-1" });
	static dynamoClient = new DynamoDB({ region: "us-east-1" });

	static async sendEmails(emailsToSend: EmailMessage[], isProd: boolean): Promise<void> {
		const template = await EmailHelper.getEmailTemplate();
		for (const email of emailsToSend) {
			const brandingData = typeof email.branding === 'string' ? JSON.parse(email.branding) : email.branding;
			const brandedTemplate = EmailHelper.processTemplate(
				template, 
				brandingData, 
				email.eventData, 
				email.customerId, 
				isProd
			);
			const shopDomain = email.shopDomain.replace('.myshopify.com', '');
			const sender = email.fromName ? 
				`${email.fromName} <${shopDomain}@raleon-rewards.com>` : 
				`${shopDomain}@raleon-rewards.com`;

			const params: any = {
				Destination: {
					ToAddresses: [email.address],
				},
				Message: {
					Body: {
						Html: {
							Charset: "UTF-8",
							Data: brandedTemplate,
						},
					},
					Subject: {
						Charset: "UTF-8",
						Data: email.emailSubject,
					},
				},
				Source: sender,
			}
			console.log(`store email: ${email.storeEmail}`);
			if (email.storeEmail) {
				params.ReplyToAddresses = [email.storeEmail];
			}
			try {
				const command = new SendEmailCommand(params);
				const response = await EmailHelper.sesClient.send(command);
				console.log(`Email sent! Message ID: ${response.MessageId}`);
			} catch (error) {
				console.error("Error sending email:", error);
				throw error;
			}
		}
	}

	private static async getEmailTemplate(): Promise<any> {
		const response = await fetch('https://raleoncdn.s3.amazonaws.com/loyalty-emails/email-template-v1.html');
		return await response.text();
	}

	private static processTemplate(
		template: string,
		branding: any,
		eventData: any,
		customerId: string,
		isProd: boolean,
	): string {
		for (const key in branding) {
			const regex = new RegExp(`{{${key}}}`, 'g');
			template = template.replace(regex, branding[key]);
		}
		
		const customerIdRegex = new RegExp('{{customerId}}', 'g');
		template = template.replace(customerIdRegex, customerId);
		
		if (eventData && JSON.stringify(eventData) !== '{}') {
			for (const key in eventData) {
				const regex = new RegExp(`{{${key}}}`, 'g');
				template = template.replace(regex, eventData[key]);
			}
		}

		template = template.replace('{{subdomain}}', isProd ? 'app' : 'dev');
		return template;
	}

	static async stageEmail(emailToStage: any): Promise<boolean> {
		const typesToStage = ['point_balance_change'];
		const ttl = (Math.floor(Date.now() / 1000) + 900) * 1000; //15 minutes
		if (typesToStage.includes(emailToStage.eventData.eventName)) {
			const record = marshall({
				customerId: emailToStage.eventData.customerId,
				address: emailToStage.address,
				timestamp: Date.now().toString(),
				eventName: emailToStage.eventData.eventName,
				orgId: emailToStage.eventData.orgId,
				data: emailToStage.eventData.data,
				ttl: ttl.toString(),
			});
	
			const params = {
			  TableName: process.env.STAGING_TABLE!,
			  Item: record,
			};
			
			console.log('Staging email:', record);
			await EmailHelper.dynamoClient.putItem(params);
			return true;
		}
		return false;
	}

	static shouldSmartSendSkip(lastEmailSent: string): boolean {
		if (lastEmailSent) {
			const lastEmailSentDate = new Date(lastEmailSent);
			const timeSinceLastEmailInHours = (new Date().getTime() - lastEmailSentDate.getTime()) / (1000 * 3600);
			if (timeSinceLastEmailInHours < 24) {
				console.log(`Smart send enabled and last email sent ${timeSinceLastEmailInHours} hours ago`);
				return true;
			}
		}
		return false;
	}

	static async getCustomerEmails(orgCustomers: Map<string, string[]>): Promise<Map<string, string>> {
		const emails = new Map<string, string>();
		for (const [orgId, customerIds] of orgCustomers) {
			const shopInfo = await getShopInfoByOrgId(orgId);
			const shopifyAccessToken = await getAPIaccessToken(shopInfo.shopDomain);
			const customerEmailsResponse = await getCustomerEmails(shopInfo.shopDomain, shopifyAccessToken, customerIds);
			if (!customerEmailsResponse || customerEmailsResponse.statusCode !== 200) {
				console.error('Error getting customer emails');
				continue;
			}
			const customers = JSON.parse(customerEmailsResponse.body).customers;
			for (const email of customers) {
				emails.set(email.id.toString(), email.email);
			}
		}
		return emails;
	}
}

export interface EmailMessage {
	orgId: number;
	shopDomain: string;
	eventName: string;
	address: string;
	customerId: string;
	emailSubject: string;
	storeEmail: string;
	fromName: string;
	branding: string;
	eventData: object;
}

import { getRaleonIdentity, saveLastEmailSentTime, getAllConfiguredRaleonEmails } from "../utils/raleon-helper";
import { getShopInfoByOrgId } from "../utils/shopify-helper";
import { marshall, unmarshall } from "@aws-sdk/util-dynamodb";
import { DynamoDB } from "@aws-sdk/client-dynamodb";
import { EmailHelper, EmailMessage } from "./email-helper";
import { EmailAggregator } from "./email-aggregator";

export class Handler {

	private dynamoClient = new DynamoDB({ region: "us-east-1" });
	private isProd: boolean;

	async main(event: any, context: any): Promise<void> {
		this.isProd = context.functionName.includes('-prod');

		const stagedEmails: EmailRecord[] = await this.getStagedRecords();

		if (!stagedEmails || !stagedEmails.length) {
			console.log('No emails to send');
			return;
		}

		const aggregator = new EmailAggregator();
		const aggregatedEmails = aggregator.aggregate(stagedEmails);

		const groupedRecords: Map<number, EmailRecord[]> = this.groupRecordsByOrgId(aggregatedEmails);
		const emailsToSend: EmailMessage[] = [];
		const identitiesGettingEmails: any[] = [];

		const emailsToDelete = aggregator.getAggregatedEmails();

		for (const [orgId, emails] of groupedRecords) {
			const shopInfo = await getShopInfoByOrgId(orgId.toString());

			const enabledEmailEvents = await getAllConfiguredRaleonEmails(orgId);
			if (!enabledEmailEvents || !enabledEmailEvents.length) {
				console.error('No enabled email events found');
				continue;
			}
			const enabledEmailEventsMap: Map<string, any> = new Map(
				enabledEmailEvents.map((event: any) => [event.name, event])
			);
			
			for (const email of emails) {
				const emailEvent = enabledEmailEventsMap.get(email.eventName);
				const customerId = typeof email.customerId == 'string' ? parseInt(email.customerId) : email.customerId;
				const rui = await getRaleonIdentity(customerId);

				if (!rui) {
					console.error(`Customer ${email.customerId} is not a Raleon user`);
					continue;
				}
				if (rui.unsubscribed) {
					console.log(`Customer ${email.customerId} has unsubscribed from emails`);
					continue;
				}

				if (emailEvent.smartsend && EmailHelper.shouldSmartSendSkip(rui.lastemailsent)) {
					continue;
				}

				const emailData: EmailMessage = {
					orgId: orgId,
					shopDomain: shopInfo.shopDomain,
					eventName: emailEvent.eventName,
					address: email.address,
					customerId: customerId.toString(),
					emailSubject: emailEvent.emailsubject,
					storeEmail: shopInfo.email,
					fromName: emailEvent.fromname,
					branding: emailEvent.branding,
					eventData: email.data,
				};

				emailsToSend.push(emailData);
				identitiesGettingEmails.push(rui.id);
			}
		}

		if (emailsToSend.length > 0) {
			await EmailHelper.sendEmails(emailsToSend, this.isProd);
		}

		if (identitiesGettingEmails.length > 0) {
			await saveLastEmailSentTime(identitiesGettingEmails);
		}

		if (emailsToDelete.length) {
			await this.deleteStagedEmails(emailsToDelete);
		}
	}

	private async getStagedRecords(): Promise<EmailRecord[]> {
		const stagedRecords = await this.dynamoClient.scan({
			TableName: process.env.STAGING_TABLE!,
			FilterExpression: 'attribute_not_exists(#ttl) OR #ttl > :now',
			ExpressionAttributeValues: {
				':now': { S: `${Math.floor(Date.now()).toString()}` },
			},
			ExpressionAttributeNames: {
				'#ttl': 'ttl',
			},
		});
		return stagedRecords.Items?.map((record) => unmarshall(record))! as EmailRecord[] || [];
	}

	private async deleteStagedEmails(emailsToDelete: EmailRecord[]): Promise<void> {
		const deletePromises = emailsToDelete.map((email) => {
			return this.dynamoClient.deleteItem({
				TableName: process.env.STAGING_TABLE!,
				Key: marshall({
					customerId: email.customerId.toString(), 
					timestamp: email.timestamp
				}),
			});
		});
		await Promise.all(deletePromises);
	}

	private groupRecordsByOrgId(stagedEmails: EmailRecord[]): Map<number, EmailRecord[]> {
		const orgs = new Map<number, EmailRecord[]>();
		for (const email of stagedEmails) {
			if (!orgs.has(email.orgId)) {
				orgs.set(email.orgId, []);
			}
			orgs.get(email.orgId)?.push(email);
		}
		return orgs;
	}
}

export interface EmailRecord {
	customerId: number;
	address: string;
	timestamp: number;
	eventName: string;
	orgId: number;
	data: any;
	ttl: number;
}

export const handler = new Handler();
export const main = handler.main.bind(handler);

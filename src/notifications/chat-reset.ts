import { fetchJ<PERSON><PERSON>, fetchUnauthenticatedJSO<PERSON> } from "../utils/shopify-helper";
import { SecretsManager } from "aws-sdk";

const WEBAPP_API_URL = process.env.WEBAPP_API_URL || 'https://app.raleon.io/api/v1';
const WEBAPP_TOKEN_SECRET = process.env.WEBAPP_TOKEN_SECRET || '';

// Initialize the Secrets Manager client
const secretsManager = new SecretsManager();

// Function to retrieve secret from AWS Secrets Manager
async function getSecret(secretArn: string): Promise<string> {
    const data = await secretsManager.getSecretValue({ SecretId: secretArn }).promise();
    if (data.SecretString) {
        const secretData = JSON.parse(data.SecretString);
        if (secretData.Raleon_Service_API) {
            return secretData.Raleon_Service_API;
        }
        throw new Error('API key not found in secret');
    }
    throw new Error('Secret not found');
}

export class Handler {
    private apiKey: string = '';
    
    async main(event: any, context: any): Promise<void> {
        // Retrieve the secret from AWS Secrets Manager
        try {
            this.apiKey = await getSecret(WEBAPP_TOKEN_SECRET);
            await this.resetQuota();
        } catch (error) {
            console.error('Error retrieving secret:', error);
            throw error;
        }
    }

    async resetQuota() {
        console.log('sending');
        const response = await fetchJSON(
            this.apiKey,
            `${WEBAPP_API_URL}/messages/quota/reset-daily`,
            "POST",
            undefined,
            'ApiKey'	
        );
        console.log("data 👉", JSON.stringify(response, null, 2));
    }
}

export const handler = new Handler();
export const main = handler.main.bind(handler);

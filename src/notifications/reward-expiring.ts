

import { SQSEvent } from "aws-lambda";
import { getOrgsWithEmailEventConfigured, getRaleonApiKey } from "../utils/raleon-helper";
import SQS = require("aws-sdk/clients/sqs");
import exp = require("constants");
import fetch from "node-fetch";

const WEBAPP_API_URL = process.env.WEBAPP_API_URL || 'https://app.raleon.io/api/v1';

export class Handler {

	private isProd: boolean;
	REWARD_EXPIRING_NAME = 'reward_expiring';
	sqsClient = new SQS({ region: 'us-east-1' });

	async main(event: SQSEvent, context: any): Promise<any> {
		console.log(`getting emails for event: ${this.REWARD_EXPIRING_NAME}`);
		const orgs = await getOrgsWithEmailEventConfigured(this.REWARD_EXPIRING_NAME);
		console.log(`orgs: ${JSON.stringify(orgs)}`);
		if (orgs.length === 0) {
			console.log(`No orgs have the loyalty event: ${this.REWARD_EXPIRING_NAME} configured`);
			return;
		}

		const configuredOrgs = new Map<number, any>(orgs.map((o: any) => [o.orgid, o]));

		let expiringCoupons;
		try {
			expiringCoupons = await this.getInventoryCouponsWithIdentity(Array.from(configuredOrgs.keys()));
		} catch(e) {
			console.log(`Error getting expiring coupons: ${e}`);
			return;
		}
		console.log(`expiring coupons: ${JSON.stringify(expiringCoupons)}`);
		
		const messagesToSend = expiringCoupons
			.filter((coupon: any) => (
				configuredOrgs.get(coupon.orgId)?.smartsend === false || 
				coupon.lastemailsent === null || 
				new Date(coupon.lastemailsent) < new Date(Date.now() - 24 * 60 * 60 * 1000))
			)
			.map((coupon: any) => {
				const orgEventData = configuredOrgs.get(coupon.orgId);
				return {
					eventData: {
						...coupon,
					},
					emailConfig: {
						emailsubject: orgEventData.emailsubject,
						fromname: orgEventData.fromname,
						branding: orgEventData.branding,
						smartsend: orgEventData.smartsend,
					},
				};
			});

		console.log(`messages to send: ${JSON.stringify(messagesToSend)}`);

		await this.addMessagesToQueue(messagesToSend);
		
		return {
			statusCode: 200,
			body: JSON.stringify("Messages processed successfully!"),
		};
	}

	private async getInventoryCouponsWithIdentity(orgIds: number[]): Promise<any[]> {
		let apiKey = await getRaleonApiKey();
		let response = await fetch(
			`${WEBAPP_API_URL}/inventory-coupons/expiring?orgIds=${orgIds.join(',')}`,
			{
				headers: {
					Authorization: `ApiKey ${apiKey}`,
				},
			}
		);
		const data: any = await response.json();
		console.log(`Loyalty Log response 👉`, data);
		return data.body ? JSON.parse(data.body) : [];
	}

	private async addMessagesToQueue(messages: any[]): Promise<void> {
		const chunkSize = 10;
		const chunks = [];
		for (let i = 0; i < messages.length; i += chunkSize) {
			chunks.push(messages.slice(i, i + chunkSize));
		}

		for (const chunk of chunks) {
			const params = {
				Entries: chunk.map((m, i) => {
					return {
						Id: i.toString(),
						MessageBody: JSON.stringify(m),
					};
				}),
				QueueUrl: process.env.QUEUE_URL!,
			};
			await this.sqsClient.sendMessageBatch(params).promise();
		}
	}
}

export const handler = new Handler();
export const main = handler.main.bind(handler);

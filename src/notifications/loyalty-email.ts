import { APIGatewayProxyResultV2, SQSEvent } from "aws-lambda";
import { getRaleonIdentity, saveLastEmailSentTime } from "../utils/raleon-helper";
import { getAPIaccessToken, getCustomerEmails, getShopInfoByOrgId } from "../utils/shopify-helper";
import { EmailHelper, EmailMessage } from "./email-helper";

export class Handler {

	private isProd: boolean;

	async main(event: SQSEvent, context: any): Promise<APIGatewayProxyResultV2> {
		console.log(JSON.stringify(event.Records));
		this.isProd = context.functionName.includes('-prod');
		const messages = event.Records.map((record: any) => JSON.parse(record.body));

		const orgCustomers: Map<string, string[]> = new Map();
		for (const message of messages) {
			if (!orgCustomers.has(message.eventData.orgId)) {
				orgCustomers.set(message.eventData.orgId, []);
			}
			orgCustomers.get(message.eventData.orgId)!.push(message.eventData.customerId);
		}

		const customerEmails: Map<string, string> = await EmailHelper.getCustomerEmails(orgCustomers);

		let emailsToSend: EmailMessage[] = [];
		let identitiesGettingEmails: any[] = [];

		for (const message of messages) {
			if (this.shouldSkipSend(message)) {
				continue;
			}

			if (!message.eventData.customerId || customerEmails.get(message.eventData.customerId) === undefined) {
				throw new Error('Customer ID is required to send an email to the customer');
			}

			const shopInfo = await getShopInfoByOrgId(message.eventData.orgId);
			console.log('Shop info:', shopInfo);

			const rui = await getRaleonIdentity(message.eventData.customerId);
			if (!rui) {
				console.error(`Customer ${message.eventData.customerId} is not a Raleon user`);
				continue;
			}
			if (rui.unsubscribed) {
				console.log(`Customer ${message.eventData.customerId} has unsubscribed from emails`);
				continue;
			}

			const emailStaged = await EmailHelper.stageEmail({ 
				...message, 
				address: customerEmails.get(message.eventData.customerId)!,
			});
			if (emailStaged) {
				continue;
			}

			const smartSendEnabled = message.emailConfig.smartsend;
			if (smartSendEnabled && EmailHelper.shouldSmartSendSkip(rui.lastemailsent)) {
				continue;
			}
			
			identitiesGettingEmails.push(rui.id);

			emailsToSend.push({
				orgId: message.eventData.orgId,
				shopDomain: !!shopInfo.shopDomain ? shopInfo.shopDomain.replace('.myshopify.com', '') : 'raleon',
				storeEmail: shopInfo.email,
				eventName: message.eventData.eventName,
				address: customerEmails.get(message.eventData.customerId)!,
				customerId: message.eventData.customerId,
				emailSubject: message.emailConfig.emailsubject,
				storeEmail: shopInfo.email,
				fromName: message.emailConfig.fromname,
				branding: message.emailConfig.branding,
				eventData: message.eventData.data,
			});
		}

		if (emailsToSend.length > 0) {
			await EmailHelper.sendEmails(emailsToSend, this.isProd);
		}

		if (identitiesGettingEmails.length > 0) {
			await saveLastEmailSentTime(identitiesGettingEmails);
		}

		return {
			statusCode: 200,
			body: JSON.stringify("Messages processed successfully!"),
		};
	}

	private shouldSkipSend(message: any) {
		if (
			message.eventData.eventName === 'point_balance_change' &&
			message.eventData?.data?.balanceChange <= 0
		) {
			return true;
		}
		return false;
	}
}

export const handler = new Handler();
export const main = handler.main.bind(handler);

import { APIGatewayProxyResultV2 } from 'aws-lambda';
import fetch from 'node-fetch';
const https = require('https');

export class Handler {

	async main(event: any, context: any): Promise<any> {
		console.log("event 👉", JSON.stringify(event, null, 2));
		await this.request('launch-all-giveaways');
		await this.request('end-all-giveaways');

		return {
			statusCode: 202,
			body: JSON.stringify({
				message: "Requests sent to launch and end all started or ended giveaways"
			}),
		};
	}

	async request(path: string): Promise<any> {
		const options: any = {
			hostname: process.env.WEBAPP_API_URL,
			method: 'POST',
			port: 443,
			path: `/api/v1/${path}`,
			headers: {
				"Content-Type": "application/json",
				'ngrok-skip-browser': true,
			},
		};

		return new Promise((resolve, reject) => {
			let req = https.request(options);
			req.write(JSON.stringify({}));
			req.end(null, null, () => {
				resolve(req);
			})
		});
	}
}
export const handler = new Handler();
export const main = handler.main.bind(handler);

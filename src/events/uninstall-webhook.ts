import { APIGatewayProxyResultV2, SQSEvent } from "aws-lambda";
import fetch from "node-fetch";
import { queryDatabase, updateDatabase } from "../utils/raleon-helper";

export class Handler {
    async main(event: SQSEvent): Promise<APIGatewayProxyResultV2> {
        const messages = event.Records.map((record: any) => {
            try {
                const message = JSON.parse(record.body);
                console.log(`webhook event ${JSON.stringify(message)}`);
                const payload = message.detail.payload;
                return {
                    ...payload,
                    topic: message.detail.metadata["X-Shopify-Topic"],
                    shopDomain: message.detail.metadata["X-Shopify-Shop-Domain"],
                };
            } catch (error) {
                console.error(`Error parsing record: ${record.body}`, error);
                return null; 
            }
        }).filter(m => m !== null);

    for (let message of messages) {
        const today = new Date().toISOString().slice(0, 10);
        const query = `
            UPDATE organization 
            SET "uninstalleddate" = $1 
            WHERE externaldomain = $2 
            RETURNING 
                id,
                (
                    SELECT planId 
                    FROM organizationplan 
                    WHERE orgid = organization.id 
                    LIMIT 1
                ) as planId,
                (
                    SELECT subscriptionId 
                    FROM organizationplan 
                    WHERE orgid = organization.id 
                    LIMIT 1
                ) as subscriptionId
        `;
        const values = [today, message.myshopify_domain];
        let freeTrialEnded = false;
        
        try {
            const response = await queryDatabase(query, values);
            console.log(`Uninstall date updated to ${today} for ${message.myshopify_domain}`);
            if (response[0].planId != 7 && !response[0].subscriptionId) {
                freeTrialEnded = true;
            }
        } catch (err) {
            console.error("Error updating uninstall date", err);
            throw err;
        }
        
        const customerIoEndpoint = `https://track.customer.io/api/v1/customers/${message.email}/events`;
        const customerIoAuth = `Basic ${Buffer.from(`${process.env.CUSTOMERIO_SITEID}:${process.env.CUSTOMERIO_APIKEY}`).toString("base64")}`;
        const customerIoData = {
            name: "shopify_uninstall",
            data: message,
        };
        const customerIoData2 = {
            name: "free_trial_ended",
            data: {
                reason: "uninstall"
            }
        }

        const makeEndpoint = `https://hook.us1.make.com/o3a2o3u5d6uf975xxovgh0p30o32iniy`;
        const makeData = message;

        const [customerIoResponse, makeResponse, freeTrialEndedResponse] = await Promise.all([
            fetch(customerIoEndpoint, {
                method: "POST",
                headers: {
                    "Authorization": customerIoAuth,
                    "content-type": "application/json",
                },
                body: JSON.stringify(customerIoData),
            }),
            fetch(makeEndpoint, {
                method: "POST",
                headers: {
                    "content-type": "application/json",
                },
                body: JSON.stringify(makeData),
            }),
            freeTrialEnded ? fetch(customerIoEndpoint, {
                method: "POST",
                headers: {
                    "Authorization": customerIoAuth,
                    "content-type": "application/json",
                },
                body: JSON.stringify(customerIoData2),
            }) : Promise.resolve('no-free-trial-end'),
        ]).catch();

        console.log(customerIoResponse);
        console.log(makeResponse);
        console.log(freeTrialEndedResponse);
            /* 
            {
                "id": 548380009,
                "name": "Super Toys",
                "email": "<EMAIL>",
                "domain": null,
                "province": "Tennessee",
                "country": "US",
                "address1": "190 MacLaren Street",
                "zip": "37178",
                "city": "Houston",
                "source": null,
                "phone": "3213213210",
                "latitude": null,
                "longitude": null,
                "primary_locale": "en",
                "address2": null,
                "created_at": null,
                "updated_at": null,
                "country_code": "US",
                "country_name": "United States",
                "currency": "USD",
                "customer_email": "<EMAIL>",
                "timezone": "(GMT-05:00) Eastern Time (US & Canada)",
                "iana_timezone": null,
                "shop_owner": "John Smith",
                "money_format": "${{amount}}",
                "money_with_currency_format": "${{amount}} USD",
                "weight_unit": "kg",
                "province_code": "TN",
                "taxes_included": null,
                "auto_configure_tax_inclusivity": null,
                "tax_shipping": null,
                "county_taxes": null,
                "plan_display_name": "Shopify Plus",
                "plan_name": "enterprise",
                "has_discounts": false,
                "has_gift_cards": true,
                "myshopify_domain": null,
                "google_apps_domain": null,
                "google_apps_login_enabled": null,
                "money_in_emails_format": "${{amount}}",
                "money_with_currency_in_emails_format": "${{amount}} USD",
                "eligible_for_payments": true,
                "requires_extra_payments_agreement": false,
                "password_enabled": null,
                "has_storefront": true,
                "finances": true,
                "primary_location_id": *********,
                "checkout_api_supported": true,
                "multi_location_enabled": true,
                "setup_required": false,
                "pre_launch_enabled": false,
                "enabled_presentment_currencies": [
                    "USD"
                ],
                "transactional_sms_disabled": false,
                "marketing_sms_consent_enabled_at_checkout": false
            }
            */

        }

        return {
            body: "Complete",
            statusCode: 200,
        };
    }
}

export const handler = new Handler();
export const main = handler.main.bind(handler);

import { getOrgsWithEmailEventConfigured, getRaleonApiKey } from "../utils/raleon-helper";
import fetch from 'node-fetch';
const WEBAPP_API_URL = process.env.WEBAPP_API_URL || 'https://app.raleon.io/api/v1';


export class Handler {

	async main(event: any) {
		const apiKey = await getRaleonApiKey();
		await fetch(`${WEBAPP_API_URL}/grant-birthday-rewards`,{
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'Authorization': `ApiKey ${apiKey}`
			},
		});

		return;
	}
}

export const handler = new Handler();
export const main = handler.main.bind(handler);

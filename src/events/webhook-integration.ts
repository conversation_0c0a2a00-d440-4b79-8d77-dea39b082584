import { APIGatewayProxyEvent, APIGatewayProxyResultV2 } from "aws-lambda/trigger/api-gateway-proxy";
import { BaseIntegration } from "./integration/base-integration";
import { Lambda } from "aws-sdk";
import { JudgeMeIntegration } from "./integration/integration-judgeme";
import { StampedReviewsIntegration } from "./integration/integration-stampedreviews";
import { SkioIntegration } from "./integration/integration-skio";
import { StayIntegration } from "./integration/integration-stay";
import { PriveIntegration } from "./integration/integration-prive";
import { WholescaleIntegration } from "./integration/integration-wholescale";
import { LoopIntegration } from "./integration/integration-loop";

type IntegrationConstructor = new (payload: any) => BaseIntegration;

const IntegrationClasses: Record<string, IntegrationConstructor> = {
	"judgeme": JudgeMeIntegration,
	"stamped-reviews": StampedReviewsIntegration,
	"skio": SkioIntegration,
	"stay": StayIntegration,
	"prive": PriveIntegration,
	"wholescale": WholescaleIntegration,
	"loop": LoopIntegration,
};

export class Handler {
	async main(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResultV2> {
		console.log('Event: ', event);

		//1. Find the integration
		let queryParameters = event.queryStringParameters;
		let integrationName = queryParameters?.name;

		if (!integrationName) {
			return {
				statusCode: 400,
				body: 'Missing integration name',
			};
		}

		let integrationClass = IntegrationClasses[integrationName];
		if (!integrationClass) {
			return {
				statusCode: 400,
				body: 'Invalid integration name',
			};
		}

		//2. Instantiate the integration
		let integration = new integrationClass(event.body);

		//3. Validate the payload
		let isValid = await integration.validate(event);
		if (!isValid) {
			return {
				statusCode: 400,
				body: 'Invalid payload',
			};
		}

		//4. Evaluate the payload
		let isConditionMet = await integration.evaluate(event);
		if (!isConditionMet) {
			return {
				statusCode: 200,
				body: 'Condition not met',
			};
		}

		//5. Award the effects
		await integration.award(event);

		return {
			statusCode: 200,
			body: 'Successfully invoked worker' + JSON.stringify(event),
		};
	}
}

export const handler = new Handler();
export const main = handler.main.bind(handler);
import { BaseIntegration } from "./base-integration";
import fetch from "node-fetch";
import { getAuthToken, getShopInfoByShopDomain } from "../../utils/shopify-helper";
import { ShopifyPointEffect } from "../effects/shopify-point-effect";

const LOOP_API_KEY_NAME = 'Loop-API-Key';

export class LoopIntegration extends BaseIntegration {
    private subscriptionPurchaseCount: number = 0;

    constructor(protected payload: any) {
        super(payload);
        this.conditionTypeToMeet = [
            'milestone-subscription-purchase',
            'first-subscription-purchase',
            'subscription-purchase'
        ];
    }

    async validate(event: any): Promise<boolean> {
        const body = JSON.parse(event.body);
        console.log('BODY 👉 ', body);

        if (!body.metaData?.myshopifyDomain) {
            throw new Error('Missing shop domain in payload');
        }

        this.subscriptionPurchaseCount = (body as any)?.payload?.subscription?.completedOrdersCount || await this.getSubscriptionCountFromLoop(body);
        console.log('subscriptionPurchaseCount 👉 ', this.subscriptionPurchaseCount);

        const topic = 'order/processed';

        const storeInfo = await getShopInfoByShopDomain(body.metaData.myshopifyDomain);
        this.orgId = storeInfo.orgId;
        const sessionToken = await getAuthToken(storeInfo.accessToken);
        this.setSessionToken(sessionToken);

        const customerId = body?.payload?.customer?.shopifyId;
        this.setCustomerId(customerId);
        const raleonUserId = await this.getRaleonUserId(sessionToken, customerId);

        if (!raleonUserId) {
            console.log('raleonUserId not found');
            return false;
        }
        this.setRaleonUserId(raleonUserId);

        return true;
    }

    async evaluate(event: any): Promise<boolean> {
        const loyaltyProgram = await this.getLoyaltyProgramForEnvironment(
            this.sessionToken,
            this.raleonUserId,
            this.customerId
        );

        this.loyaltyProgramData = loyaltyProgram;
        console.log('loyaltyProgram', loyaltyProgram);

        return true;
    }

    async award(event: any): Promise<boolean> {
        // const body = JSON.parse(event.body);
        const topic = 'order/processed';

        console.log('Integration: Basic award');
        if (this.loyaltyProgramData && this.loyaltyProgramData.loyaltyCampaigns) {
            for (let campaign of this.loyaltyProgramData.loyaltyCampaigns) {
                if (campaign.loyaltyEarns) {
                    for (let earn of campaign.loyaltyEarns) {
                        if (earn.earnConditions) {
                            let condition = earn.earnConditions[0];
                            if (condition && this.conditionTypeToMeet.includes(condition.type)) {
                                let conditionMet = false;

                                if (condition.type === 'subscription-purchase') {
                                    conditionMet = topic.toLowerCase() === 'order/processed';
                                } else if (condition.type === 'first-subscription-purchase') {
                                    conditionMet = this.subscriptionPurchaseCount === 1;
                                } else if (condition.type === 'milestone-subscription-purchase') {
                                    conditionMet = this.subscriptionPurchaseCount === condition.amount;
                                }

                                if (conditionMet) {
                                    console.log("Integration: Condition met, awarding effects");
                                    for (let effect of earn.earnEffects) {
                                        console.log("Integration: Awarding effect", effect);
                                        const effectClass = new ShopifyPointEffect();
                                        await effectClass.processEffect(this.sessionToken, this.raleonUserId, this.customerId, this.orgId, effect, 0, earn);
                                        console.log("effect 👉", effect);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return true;
    }

    private async   getSubscriptionCountFromLoop(eventData: any) {
        const apiKey = await this.getOrganizationKey(LOOP_API_KEY_NAME);
        if (!apiKey) {
            throw new Error('Invalid API key');
        }

        const subscriptionId = eventData.subscription.id;
        if (!subscriptionId) {
            throw new Error('No subscription ID in webhook payload');
        }

        const response = await fetch(
            `https://api.loopsubscriptions.com/admin/2023-10/subscription/${subscriptionId}`,
            {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`,
                },
            }
        );

        const subscriptionData = await response.json();
        console.log('Subscription Data 👉 ', JSON.stringify(subscriptionData));

        if (!subscriptionData || !(subscriptionData as any).data) {
            this.subscriptionPurchaseCount = 0;
            return;
        }

        const subscriptionPurchaseCount = (subscriptionData as any).data.completedOrdersCount || 0;
        console.log(`subscriptionPurchaseCount: ${subscriptionPurchaseCount}`);

        return subscriptionPurchaseCount;
    }
}
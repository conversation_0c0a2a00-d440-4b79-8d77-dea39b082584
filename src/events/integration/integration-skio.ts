import { BaseIntegration } from "./base-integration";
import fetch from "node-fetch";
import { getAuthToken, getShopInfoByShopDomain } from "../../utils/shopify-helper";
import { ShopifyPointEffect } from "../effects/shopify-point-effect";

const SKIO_API_KEY_NAME = 'Skio-API-Key';
const SKIO_WEBHOOK_TOKEN_NAME = 'Skio-Webhook-Token';

export class SkioIntegration extends BaseIntegration {

	private subscriptionPurchaseCount: number = 0;

	constructor(protected payload: any) {
		super(payload);
		this.conditionTypeToMeet = [
			'milestone-subscription-purchase',
			'first-subscription-purchase',
			'subscription-purchase'
		];
	}

	async validate(event: any): Promise<boolean> {
		const body = JSON.parse(event.body);
		console.log('BODY 👉 ', body);
		if (!body.domain || !body.skioWebhookToken || !body.eventType) {
			throw new Error('Invalid request');
		}

		const supportedEventTypes = ['subscription activated', 'subscription created', 'orderplaced'];

		if (!supportedEventTypes.includes(body.eventType.toLowerCase())) return false;

		const storeInfo = await getShopInfoByShopDomain(body.domain);
		this.orgId = storeInfo.orgId;
		const sessionToken = await getAuthToken(storeInfo.accessToken);
		this.setSessionToken(sessionToken);

		await this.validateSkioWebhookToken(body.skioWebhookToken);

		const customerId = await this.getCustomerIdFromSkio(body.eventData);
		this.setCustomerId(customerId);
		const raleonUserId = await this.getRaleonUserId(sessionToken, customerId);

		if (!raleonUserId) {
			console.log('raleonUserId not found');
			return false;
		}
		this.setRaleonUserId(raleonUserId);
		return true;
	}

	async evaluate(event: any): Promise<boolean> {
		const loyaltyProgram = await this.getLoyaltyProgramForEnvironment(
			this.sessionToken,
			this.raleonUserId,
			this.customerId
		);

		this.loyaltyProgramData = loyaltyProgram;
		console.log('loyaltyProgram', loyaltyProgram);

		return true;
	}

	async award(event: any): Promise<boolean> {
		const body = JSON.parse(event.body);
		if (this.loyaltyProgramData && this.loyaltyProgramData.loyaltyCampaigns) {
			for (let campaign of this.loyaltyProgramData.loyaltyCampaigns) {
				if (campaign.loyaltyEarns) {
					for (let earn of campaign.loyaltyEarns) {
						if (earn.earnConditions) {
							let condition = earn.earnConditions[0];
							if (condition && this.conditionTypeToMeet.includes(condition.type)) {
								let conditionMet = false;

								if (condition.type === 'subscription-purchase') {
									conditionMet = body.eventType.toLowerCase() === 'subscription created';
								} else if (condition.type === 'first-subscription-purchase') {
									conditionMet = this.subscriptionPurchaseCount === 1;
								} else if (condition.type === 'milestone-subscription-purchase') {
									conditionMet = this.subscriptionPurchaseCount === condition.amount;
								}

								if (conditionMet) {
									console.log("Integration: Condition met, awarding effects");
									for (let effect of earn.earnEffects) {
										console.log("Integration: Awarding effect", effect);
										const effectClass = new ShopifyPointEffect();
										await effectClass.processEffect(this.sessionToken, this.raleonUserId, this.customerId, this.orgId, effect, 0, earn);
										console.log("effect 👉", effect);
									}
								}
							}
						}
					}

				}
			}
		}
		return true;
	}

	private async validateSkioWebhookToken(token: string) {
		const webhookToken = await this.getOrganizationKey(SKIO_WEBHOOK_TOKEN_NAME);

		if (!this.safeCompare(token, webhookToken)) {
			throw new Error('Invalid webhook token');
		}
	}

	private async getCustomerIdFromSkio(eventData: any) {
		const subscriptionId = eventData.subscriptionId;
		const customerEmail = eventData.userEmail;
		const apiKey = await this.getOrganizationKey(SKIO_API_KEY_NAME);
		if (!apiKey) {
			throw new Error('Invalid API key');
		}
		const response = await fetch(
			'https://graphql.skio.com/v1/graphql',
			{
				method: 'POST',
				headers: {
					'Content-Type': 'application/graphql',
					'Authorization': `API ${apiKey}`,
				},
				body: JSON.stringify({
					query: `{
						SubscriptionByPk(id: "${subscriptionId}") {
							id
							createdAt
							originOrderId
							cyclesCompleted
							StorefrontUser {
								id
								firstName
								lastName
								email
								platformId
								Subscriptions {
									id
									originOrderId
									cyclesCompleted
								}
							}
						}
					}`
				})
			}
		);
		const subscriptionData: any = await response.json();

		console.log('Subscription Data 👉 ', JSON.stringify(subscriptionData));
		if (!subscriptionData || !subscriptionData.data || !subscriptionData.data.SubscriptionByPk) {
			throw new Error('Invalid Subscription ID on fetch');
		}

		if (subscriptionData.data.SubscriptionByPk.StorefrontUser.email !== customerEmail) {
			if (eventData.userType == 'StorefrontUser') {
				throw new Error('Customer email does not match');
			}
			//This happens when the person who placed the order was not the storefront user. 
			//Could be an admin in skio that placed it on their behalf. We should still grant reward.
			console.log('Customer email does not match but is not a StorefrontUser');
		}

		const globalCustomerId = subscriptionData.data.SubscriptionByPk.StorefrontUser.platformId;
		if (!globalCustomerId) {
			throw new Error('No Customer Id returned from Skio');
		}

		const subscriptions = subscriptionData.data.SubscriptionByPk.StorefrontUser.Subscriptions;
		if (subscriptions && subscriptions.length > 0) {
			this.subscriptionPurchaseCount = subscriptions.reduce((total: number, sub: any) => total + sub.cyclesCompleted, 0);
		}
		console.log(`subscriptionPurchaseCount: ${this.subscriptionPurchaseCount}`);

		return globalCustomerId.split('/').pop();
	}

	safeCompare(a: string, b: string): boolean {
		if (a.length !== b.length) {
			return false;
		}

		let result = 0;
		for (let i = 0; i < a.length; i++) {
			result |= a.charCodeAt(i) ^ b.charCodeAt(i);
		}

		return result === 0;
	}
}
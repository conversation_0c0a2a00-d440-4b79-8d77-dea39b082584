import { BaseIntegration } from "./base-integration";
import fetch from "node-fetch";
import { getAuthToken, getShopInfoByShopDomain } from "../../utils/shopify-helper";
import { ShopifyPointEffect } from "../effects/shopify-point-effect";
import * as jwt from 'jsonwebtoken';

const STAY_API_KEY_NAME = 'Stay-API-Key';

export class StayIntegration extends BaseIntegration {

	private subscriptionPurchaseCount: number = 0;

	constructor(protected payload: any) {
		super(payload);
		this.conditionTypeToMeet = [
			'milestone-subscription-purchase',
			'first-subscription-purchase',
			'subscription-purchase'
		];
	}

	async validate(event: any): Promise<boolean> {
		const body = JSON.parse(event.body);
		console.log('BODY 👉 ', body);

		const webhookEvent = event.headers[`X-RETEXTION-WEBHOOK-EVENT`];
		const shop = event.headers[`X-RETEXTION-WEBHOOK-SHOP`];
		const webhookToken = event.headers[`X-RETEXTION-WEBHOOK-TOKEN`];

		if (!webhookEvent || !shop || !webhookToken) {
			throw new Error('Invalid request');
		}

		const supportedEventTypes = ['subscription', 'subscription_update'];

		if (!supportedEventTypes.includes(webhookEvent.toLowerCase())) return false;

		const storeInfo = await getShopInfoByShopDomain(shop);
		this.orgId = storeInfo.orgId;
		const sessionToken = await getAuthToken(storeInfo.accessToken);
		this.setSessionToken(sessionToken);

		const apiKey = await this.getOrganizationKey(STAY_API_KEY_NAME);
		await this.validateStayWebhookToken(webhookToken, apiKey);

		const customerId = await this.getCustomerIdFromStay(body, apiKey);
		this.setCustomerId(customerId);
		const raleonUserId = await this.getRaleonUserId(sessionToken, customerId);

		if (!raleonUserId) {
			console.log('raleonUserId not found');
			return false;
		}
		this.setRaleonUserId(raleonUserId);
		return true;
	}

	async evaluate(event: any): Promise<boolean> {
		const loyaltyProgram = await this.getLoyaltyProgramForEnvironment(
			this.sessionToken,
			this.raleonUserId,
			this.customerId
		);

		this.loyaltyProgramData = loyaltyProgram;
		console.log('loyaltyProgram', loyaltyProgram);

		return true;
	}

	async award(event: any): Promise<boolean> {
		const body = JSON.parse(event.body);
		const webhookEvent = event.headers[`X-RETEXTION-WEBHOOK-EVENT`];
		const shop = event.headers[`X-RETEXTION-WEBHOOK-SHOP`]; 
		if (this.loyaltyProgramData && this.loyaltyProgramData.loyaltyCampaigns) {
			for (let campaign of this.loyaltyProgramData.loyaltyCampaigns) {
				if (campaign.loyaltyEarns) {
					for (let earn of campaign.loyaltyEarns) {
						if (earn.earnConditions) {
							let condition = earn.earnConditions[0];
							if (condition && this.conditionTypeToMeet.includes(condition.type)) {
								let conditionMet = false;

								if (condition.type === 'subscription-purchase') {
									conditionMet = webhookEvent.toLowerCase() === 'subscription';
								} else if (condition.type === 'first-subscription-purchase') {
									conditionMet = this.subscriptionPurchaseCount === 1;
								} else if (condition.type === 'milestone-subscription-purchase') {
									conditionMet = this.subscriptionPurchaseCount === condition.amount;
								}

								if (conditionMet) {
									console.log("Integration: Condition met, awarding effects");
									for (let effect of earn.earnEffects) {
										console.log("Integration: Awarding effect", effect);
										const effectClass = new ShopifyPointEffect();
										await effectClass.processEffect(this.sessionToken, this.raleonUserId, this.customerId, this.orgId, effect, 0, earn);
										console.log("effect 👉", effect);
									}
								}
							}
						}
					}

				}
			}
		}
		return true;
	}

	private async validateStayWebhookToken(token: string, apiKey: string) {
		if (!apiKey) {
			throw new Error('Missing Stay AI API key');
		}
		const decoded = jwt.verify(token, apiKey);
		return true;
	}

	private async getCustomerIdFromStay(eventData: any, apiKey: string) {
		const customerEmail = eventData.customer.email;
		const customerId = eventData.shopifyPayload.customer_id;
		if (!apiKey) {
			throw new Error('Invalid API key');
		}
		const allOrders = await fetch(
			`https://api.retextion.com/api/v2/subscriptions/?email=${encodeURIComponent(customerEmail)}`,
			{
				method: 'GET',
				headers: {
					'Content-Type': 'application/json',
					'X-RETEXTION-ACCESS-TOKEN': `${apiKey}`,
				},
			}
		);
		const allSubscriptionOrders: any = await allOrders.json();

		console.log('All Subscriptions 👉 ', JSON.stringify(allSubscriptionOrders));

		if (!allSubscriptionOrders || !allSubscriptionOrders.data || allSubscriptionOrders.total <= 0) {
			this.subscriptionPurchaseCount = 0;
			console.log(`no subscriptions for customer ${customerEmail}`);
			return eventData.shopifyPayload.customer_id;
		}

		const subscriptionIds = allSubscriptionOrders.data.map((s: any) => s.id);

		const allSubscriptionsWithCounts = Promise.all(subscriptionIds.map(async (id: string) => {
			const subscriptionData = await fetch(
				`https://api.retextion.com/api/v2/subscriptions/${id}`,
				{
					method: 'GET',
					headers: {
						'Content-Type': 'application/json',
						'X-RETEXTION-ACCESS-TOKEN': `${apiKey}`,
					},
				}
			);
			return subscriptionData.json();
		}));

		const subscriptionData: any = await allSubscriptionsWithCounts;
		console.log('Subscription Data 👉 ', JSON.stringify(subscriptionData));
		if (!subscriptionData || subscriptionData?.shopifyOrders?.length <= 0) {
			this.subscriptionPurchaseCount = 0;
		}

		this.subscriptionPurchaseCount = subscriptionData.reduce((acc: number, s: any) => {
			return acc + s.shopifyOrders.length;
		}, 0);
		console.log(`subscriptionPurchaseCount: ${this.subscriptionPurchaseCount}`);

		return customerId;
	}
}
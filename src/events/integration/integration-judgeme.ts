import { BaseIntegration } from "./base-integration";
import * as crypto from "crypto"
import { getAuthToken, getShopInfoByShopDomain } from "../../utils/shopify-helper";


export class JudgeMeIntegration extends BaseIntegration {
    hasPhotos: boolean = false;
    constructor(protected payload: any) {
        super(payload);
        this.conditionTypeToMeet = ['product-review'];
    }

    async validate(event: any): Promise<boolean> {
        let headers = event.headers;
        let body = event.body;
        let verified = await this.verifyWebhookRequest(headers, body);
        if(!verified.status) {
            return false;
        }
        return true;
    }

    async evaluate(event: any): Promise<boolean> {
        //Typical payload looks like this
        /*{
            body: {
                review: {
                    id: 123,
                    title: 'Great product',
                    body: 'I love this product',
                    rating: 5,
                    product_external_id: '123456789', (SHOPIFY PRODUCT ID)
                    reviewer: {
                        id: 123,
                        external_id: '123456789', (SHOPIFY CUSTOMER ID)
                        email: '<EMAIL>',
                        name: '<PERSON>'
                    }
                    created_at: '2021-01-01T00:00:00.000Z',
                    updated_at: '2021-01-01T00:00:00.000Z',
                    has_published_pictures: false,
                    has_published_videos: false,
                    pictures: [],
                    product_title: 'Product Title',
                },
                shop_domain: 'example.myshopify.com',
                platform: 'shopify',
                event: 'review/created',
            }
        }*/

        //We only support review/created event
        console.log(JSON.stringify(event.body, null, 2));
        let body = JSON.parse(event.body);
        if(body.event !== 'review/created') {
            console.log('event not supported', body.event);
            return true;
        }

        //We only support shopify platform
        if(body.platform !== 'shopify') {
            console.log('platform not supported', body.platform);
            return true;
        }

        //we only support if reviewer has a valid external_id
        if(!body.review.reviewer.external_id) {
            console.log('reviewer external_id not found');
            return true;
        }

        this.hasPhotos = body.review?.pictures?.length > 0;

        const shopInfo = await getShopInfoByShopDomain(body.shop_domain);
        const sessionToken = await getAuthToken(shopInfo.accessToken);
        const raleonUserId = await this.getRaleonUserId(
            sessionToken,
            body.review.reviewer.external_id
        );

        if (raleonUserId == null) {
            console.log(`No raleon user found for customer ${body.review.reviewer.external_id}`);
            return true;
        }

        //Lets grab the loyalty Program for this customer
        const loyaltyProgram = await this.getLoyaltyProgramForEnvironment(
            sessionToken,
            raleonUserId,
            body.review.reviewer.external_id
        );

        //Save the data for awarding
        this.loyaltyProgramData = loyaltyProgram;
        this.raleonUserId = raleonUserId;
        this.sessionToken = sessionToken;
        this.customerId = body.review.reviewer.external_id;
        this.orgId = shopInfo.orgId;

        console.log('loyaltyProgram', loyaltyProgram);

        return true;
    }

    async award(event: any): Promise<boolean> {
        //Judgeme sends photo information as soon as review is created
        //In the future we probably need more complex handling of cases where reviews need to be approved before awarding.
        this.conditionTypeToMeet = ['product-review'];
        await super.award(event);
        if(this.hasPhotos) {
            console.log('has photos')
            this.conditionTypeToMeet = ['product-photo-review'];
            await super.award(event);
        }
        return true;
    }

    private async verifyWebhookRequest(headers: any, body: any) {
        try {
            console.log('verifying hmac for webhook route', body, headers);
            //TODO we need a way to store and manage secrets
            let secrets = await this.getSecrets();
            let apiSecret = secrets.judgeme_secret;
            const generatedHash = crypto.createHmac('SHA256', apiSecret).update(body, 'utf8').digest('hex');
            const hmac = headers['Judgeme-Hmac-Sha256'] || "no hmac";
            console.log('generatedHash', generatedHash);
            console.log('hmac', hmac);
    
            const safeCompareResult = this.safeCompare(generatedHash, hmac);
    
            if (!!safeCompareResult) {
                console.log('hmac verified for webhook route, proceeding');
                return {
                    status: true,
                    code: 200,
                    message: 'Authorized'
                }
            } else {
                console.log('Shopify hmac verification for webhook failed, aborting');
                return {
                    status: false,
                    code: 401,
                    message: 'Not Authorized'
                }
            }
        } catch (error) {
            console.log(error);
            return {
                status: false,
                code: 500,
                message: error
            };
        }
    }

    safeCompare(a: string, b: string): boolean {
        if (a.length !== b.length) {
            return false;
        }
    
        let result = 0;
        for (let i = 0; i < a.length; i++) {
            result |= a.charCodeAt(i) ^ b.charCodeAt(i);
        }
    
        return result === 0;
    }
}
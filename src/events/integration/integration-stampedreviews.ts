import { BaseIntegration } from "./base-integration";
import { getAuthToken, getShopInfoByShopDomain } from "../../utils/shopify-helper";
const generatedSecretKeyId = 'stamped-reviews-secret';
const storeHashKeyId = 'stamped-reviews-store-hash';
const apiKeyKeyId = 'stamped-reviews-api-key';

export class StampedReviewsIntegration extends BaseIntegration {
    isUpdate: boolean;
    isCreate: boolean;
    constructor(protected payload: any) {
        super(payload);
        this.conditionTypeToMeet = ['product-review', 'product-photo-review'];
    }

    async validate(event: any): Promise<boolean> {
        let headers = event.headers;
        let body = JSON.parse(event.body);
        //Verify if X-Stamped-Topic is present and equals 'review/created' or 'review/updated'
        this.isUpdate = headers['X-Stamped-Topic'] === 'review/updated';
        this.isCreate = headers['X-Stamped-Topic'] === 'review/created';
        //If its an update, we only care about photo reviews
        if(this.isUpdate) {
            this.conditionTypeToMeet = ['product-photo-review'];
            if(!body.reviewUserPhotos || body.reviewUserPhotos.length <= 1) {
                console.log('invalid photo review');
                return false;
            }
        }
        else {
            this.conditionTypeToMeet = ['product-review'];
        }
        if(!headers['X-Stamped-Topic'] || (!this.isUpdate && !this.isCreate)) {
            console.log('invalid topic');
            return false;
        }

        let externalDomain = headers['X-Stamped-Store-Url'];
        const shopInfo = await getShopInfoByShopDomain(externalDomain);
        this.orgId = shopInfo.orgId;
        console.log('shopInfo', shopInfo);
        const sessionToken = await getAuthToken(shopInfo.accessToken);
        console.log('sessionToken', sessionToken);
        this.setSessionToken(sessionToken);
        let userEmail = body.customerEmail;
        console.log('Body', body)
        console.log('userEmail', userEmail);

        //Grab keys
        let generatedSecret = await this.getOrganizationKey(generatedSecretKeyId);
        let storeHash = await this.getOrganizationKey(storeHashKeyId);
        let apiKey = await this.getOrganizationKey(apiKeyKeyId);

        //For some we have to hit the api to get the shopifyId
        let url = `https://stamped.io/api/v3/merchant/shops/${storeHash}/customers/lookup?email=${userEmail}`;
        console.log('url', url)
        let customerResponse = await fetch(url, {
            method: 'GET',
            headers: {
                'stamped-api-key': apiKey,
            }
        });
        console.log('customerResponse', customerResponse);
        console.log('customerResponse', JSON.stringify(customerResponse, null, 2));
        const customer = await customerResponse.json();
        console.log('customer', customer);
        if(!customer.shopifyId) {
            console.log('customer not found');
            return false;
        }
  
        const shopifyCustomerId = customer.shopifyId;
        this.setCustomerId(shopifyCustomerId);
        const raleonUserId = await this.getRaleonUserId(
            sessionToken,
            shopifyCustomerId
        );
        console.log('raleonUserId', raleonUserId);

        if(!raleonUserId) {
            console.log('raleonUserId not found');
            return false;
        }

        this.setRaleonUserId(raleonUserId);

        console.log('keys', generatedSecret, storeHash, apiKey);
        if(!headers['X-Stamped-Webhook-Secret'] || headers['X-Stamped-Webhook-Secret'] !== generatedSecret) {
            console.log('invalid secret');
            return false;
        }
        return true;
    }

    async evaluate(event: any): Promise<boolean> {
        //Lets grab the loyalty Program for this customer
        const loyaltyProgram = await this.getLoyaltyProgramForEnvironment(
            this.sessionToken,
            this.raleonUserId,
            this.customerId 
        );

        //Save the data for awarding
        this.loyaltyProgramData = loyaltyProgram;
        console.log('loyaltyProgram', loyaltyProgram);
        return true;
    }

    async award(event: any): Promise<boolean> {
        return super.award(event);
    }
}
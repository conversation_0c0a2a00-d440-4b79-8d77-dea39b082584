import { BaseIntegration } from "./base-integration";
import fetch from "node-fetch";
import { getAuthToken, getShopInfoByShopDomain } from "../../utils/shopify-helper";
import { ShopifyPointEffect } from "../effects/shopify-point-effect";

const WHOLESCALE_API_KEY = '';

export class WholescaleIntegration extends BaseIntegration {

	constructor(protected payload: any) {
		super(payload);
		this.conditionTypeToMeet = [
			'product-review',
			'product-photo-review',
		];
	}

	async validate(event: any): Promise<boolean> {
		const body = JSON.parse(event.body);
		console.log('Wholescale Integration BODY 👉 ', body);

		//TODO: add validation logic here once integration is built
		return false;
	}

	async evaluate(event: any): Promise<boolean> {
		const loyaltyProgram = await this.getLoyaltyProgramForEnvironment(
			this.sessionToken,
			this.raleonUserId,
			this.customerId
		);

		this.loyaltyProgramData = loyaltyProgram;
		console.log('loyaltyProgram', loyaltyProgram);

		return true;
	}

	async award(event: any): Promise<boolean> {
		return true;
	}
}
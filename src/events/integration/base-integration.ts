import fetch from "node-fetch";
export const WEBAPP_API_URL = process.env.WEBAPP_API_URL || 'https://dev.raleon.io/api/v1';
import { ShopifyPointEffect } from "../effects/shopify-point-effect";
import { GetSecretValueCommand, SecretsManagerClient } from '@aws-sdk/client-secrets-manager';

export abstract class BaseIntegration {
    //These are filled in as calls to get them are made
    //Used primarily to help derived classes not have to worry about getting these
    protected loyaltyProgramData: any;
    protected eventPayload: any;
    protected raleonUserId: string;
    protected customerId: string;
    protected sessionToken: string;
    protected conditionTypeToMeet: string[] = []; //This is an array such that we can support multiple conditions
    protected orgId: string;

	constructor(protected payload: any) {
        this.eventPayload = payload;
    }

    //This should validate the payload to make sure its coming from the right source
	abstract validate(event: any): Promise<boolean> ;

    //This should evaluate the payload to see if it meets the condition
    abstract evaluate(event: any): Promise<boolean>;

    //This should award the effects
    public async award(event: any): Promise<boolean> {
        console.log('Integration: Basic award -> Override for specific integrations if necessary');
        if(this.loyaltyProgramData && this.loyaltyProgramData.loyaltyCampaigns) {
            for(let campaign of this.loyaltyProgramData.loyaltyCampaigns) {
                if(campaign.loyaltyEarns) {
                    for(let earn of campaign.loyaltyEarns) {
                        if(earn.earnConditions) {
                            //Only need to check the first condition type here instead of looping
                            let condition = earn.earnConditions[0];
                            if(condition && this.conditionTypeToMeet.includes(condition.type)) {
                                console.log("Integration: Condition met, awarding effects");
								for (let effect of earn.earnEffects) {
                                    console.log("Integration: Awarding effect", effect);
									const effectClass = new ShopifyPointEffect();
									await effectClass.processEffect(this.sessionToken, this.raleonUserId, this.customerId, this.orgId, effect, 0, earn);
									console.log("effect 👉", effect);
								}
                            }
                        }
                    }
                
                }
            }
        }
        return true;
    }

    public setRaleonUserId(raleonUserId: string) {
        this.raleonUserId = raleonUserId;
    }

    public setSessionToken(sessionToken: string) {
        this.sessionToken = sessionToken;
    }

    public setCustomerId(customerId: string) {
        this.customerId = customerId;
    }

    protected async getRaleonUserId(sessionToken: string, customerId: string) {
		console.log(`getting raleon user`);
		const response = await this.fetch(
			sessionToken,
			`${WEBAPP_API_URL}/raleon-users/from-customer/${customerId}`,
			"GET"
		);
		console.log("raleon user 👉", JSON.stringify(response, null, 2));
        this.raleonUserId = response?.raleonUserId;
		return response?.raleonUserId;
	}

    protected async getSecrets() {
        const client = new SecretsManagerClient({
            endpoint: "https://vpce-060670840daa313c8-mcqyvy4b.secretsmanager.us-east-1.vpce.amazonaws.com",
            region: "us-east-1",
        });

        const command = new GetSecretValueCommand({
            SecretId: process.env.INTEGRAION_SECRET_ARN,
        });
        const response = await client.send(command);
        const secret = JSON.parse(response.SecretString!);
        return secret;
    }

    protected async getOrganizationSecrets() {
        const client = new SecretsManagerClient({
            endpoint: "https://vpce-060670840daa313c8-mcqyvy4b.secretsmanager.us-east-1.vpce.amazonaws.com",
            region: "us-east-1",
        });

        const command = new GetSecretValueCommand({
            SecretId: process.env.ORGANIZATION_KEYS_SECRET_ARN,
        });
        const response = await client.send(command);
        const secret = JSON.parse(response.SecretString!);
        return secret;
    }

    async getLoyaltyProgramForEnvironment(sessionToken: string, raleonUserId: any, customerId: any) {
		console.log(`getting loyaltyPrograms`);
		const response = await this.fetch(
			sessionToken,
			`${WEBAPP_API_URL}/loyalty-programs/live/${raleonUserId}/${customerId}`,
			"GET"
		);
		console.log("loyaltyProgramData 👉", JSON.stringify(response, null, 2));
        this.loyaltyProgramData = response[0];
        this.customerId = customerId;
		return response[0];
	}

    async getOrganizationKey(key: string) {
        const response = await this.fetch(
			this.sessionToken,
			`${WEBAPP_API_URL}/organizations/organization-keys/${key}`,
			"GET"
		);
		return response && response.length > 0 ? response[0].value : undefined;
    }

    private async fetch(
		authToken: string,
		url: string,
		method: string,
		body?: string
	): Promise<any> {
        console.log('fetching', url, method, body, authToken)
		let response;
		const options: any = {
			method,
			headers: {
				"Content-Type": "application/json",
				Authorization: `Bearer ${authToken}`,
				'ngrok-skip-browser-warning': true,
			},
		};
		if (body) options.body = body;
		try {
			response = await fetch(url, options);
		} catch (e) {
			console.log("error 👉", e);
		}

        console.log('response', response)

		return await response!.json();
	}
}

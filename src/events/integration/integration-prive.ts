import { BaseIntegration } from "./base-integration";
import fetch from "node-fetch";
import { getAuthToken, getShopInfoByOrgId, getShopInfoByShopDomain } from "../../utils/shopify-helper";
import { ShopifyPointEffect } from "../effects/shopify-point-effect";
import { getRaleonUserIdFromCustomerId } from "../../utils/raleon-helper";
const crypto = require('crypto');

const PRIVE_API_KEY_NAME = 'Prive-API-Key';

const Memcached = require('memcached');

const MEMCACHED_ENDPOINT = process.env.MEMCACHED_ENDPOINT;

let memcachedClient = new Memcached(MEMCACHED_ENDPOINT!);

export class PriveIntegration extends BaseIntegration {

	private subscriptionPurchaseCount: number = 0;

	constructor(protected payload: any) {
		super(payload);
		this.conditionTypeToMeet = [
			'milestone-subscription-purchase',
			'first-subscription-purchase',
			'subscription-purchase'
		];
	}

	async validate(event: any): Promise<boolean> {
		const body = JSON.parse(event.body);
		console.log('BODY 👉 ', body);

		//check cache
		const isAlreadyProcessed = await new Promise((resolve, reject) => {
			memcachedClient.get(body.idempotencyKey, (err: any, data: any) => {
				if (err) reject(err);
				else resolve(data);
			});
		});
		if (isAlreadyProcessed) {
			console.log('Event already processed');
			return false;
		}
		memcachedClient.set(body.idempotencyKey, 'true', 20, (err: any) => {
			if (err) {
				console.error(`Error setting memcached: ${err}`);
			}
		});

		const webhookEvent = body.topic;
		const hmac = event.headers[`X-Prive-Hmac-Sha256`];

		if (!webhookEvent || !hmac) {
			throw new Error('Invalid request');
		}

		const supportedEventTypes = ['subscriptions/created', 'subscriptions/status/updated'];

		if (!supportedEventTypes.includes(webhookEvent.toLowerCase())) return false;

		const customerShopifyId = body.data.subscriber.externalId;
		const customerId = customerShopifyId.split('/').pop();
		this.setCustomerId(customerId);
		const userInfo = await getRaleonUserIdFromCustomerId(customerId);
		this.orgId = userInfo.orgid;

		const storeInfo = await getShopInfoByOrgId(this.orgId);
		const sessionToken = await getAuthToken(storeInfo.accessToken);
		this.setSessionToken(sessionToken);

		const apiKey = await this.getOrganizationKey(PRIVE_API_KEY_NAME);
		await this.validateWebhookToken(body, hmac, apiKey);

		await this.setSubscriptionPurchaseCount(body, apiKey);
		const raleonUserId = await this.getRaleonUserId(sessionToken, customerId);

		if (!raleonUserId) {
			console.log('raleonUserId not found');
			return false;
		}
		this.setRaleonUserId(raleonUserId);
		return true;
	}

	async evaluate(event: any): Promise<boolean> {
		const loyaltyProgram = await this.getLoyaltyProgramForEnvironment(
			this.sessionToken,
			this.raleonUserId,
			this.customerId
		);

		this.loyaltyProgramData = loyaltyProgram;
		console.log('loyaltyProgram', loyaltyProgram);

		return true;
	}

	async award(event: any): Promise<boolean> {
		const webhookEvent = JSON.parse(event.body).topic;
		if (this.loyaltyProgramData && this.loyaltyProgramData.loyaltyCampaigns) {
			for (let campaign of this.loyaltyProgramData.loyaltyCampaigns) {
				if (campaign.loyaltyEarns) {
					for (let earn of campaign.loyaltyEarns) {
						if (earn.earnConditions) {
							let condition = earn.earnConditions[0];
							if (condition && this.conditionTypeToMeet.includes(condition.type)) {
								let conditionMet = false;

								if (condition.type === 'subscription-purchase') {
									conditionMet = webhookEvent.toLowerCase() === 'subscriptions/created';
								} else if (condition.type === 'first-subscription-purchase') {
									conditionMet = this.subscriptionPurchaseCount === 1;
								} else if (condition.type === 'milestone-subscription-purchase') {
									conditionMet = this.subscriptionPurchaseCount === condition.amount;
								}

								if (conditionMet) {
									console.log("Integration: Condition met, awarding effects");
									for (let effect of earn.earnEffects) {
										console.log("Integration: Awarding effect", effect);
										const effectClass = new ShopifyPointEffect();
										await effectClass.processEffect(this.sessionToken, this.raleonUserId, this.customerId, this.orgId, effect, 0, earn);
										console.log("effect 👉", effect);
									}
								}
							}
						}
					}

				}
			}
		}
		return true;
	}

	private async validateWebhookToken(body: any, hmac: string, webhookSecret: string) {
		if (!hmac) {
			throw new Error('Invalid HMAC');
		}
		const genHmac = crypto.createHmac('sha256', webhookSecret).update(JSON.stringify(body), 'utf8', 'hex').digest('base64');
		return genHmac === hmac;
	}

	private async setSubscriptionPurchaseCount(eventData: any, apiKey: string) {
		const customerEmail = eventData.data.subscriber.email;
		if (!apiKey) {
			throw new Error('Invalid API key');
		}
		const allOrders = await fetch(
			`https://subs.api.tryprive.com/api/v1/subscriptions/?subscriberEmail=${encodeURIComponent(customerEmail)}&limit=250`,
			{
				method: 'GET',
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${apiKey}`,
				},
			}
		);
		const allSubscriptionOrders: any = await allOrders.json();

		console.log('All Subscriptions 👉 ', JSON.stringify(allSubscriptionOrders));

		if (!allSubscriptionOrders || !allSubscriptionOrders.subscriptions?.length || allSubscriptionOrders.totalItems <= 0) {
			this.subscriptionPurchaseCount = 0;
			console.log(`no subscriptions for customer ${customerEmail}`);
			return;
		}

		this.subscriptionPurchaseCount = allSubscriptionOrders.subscriptions.length;
	}
}
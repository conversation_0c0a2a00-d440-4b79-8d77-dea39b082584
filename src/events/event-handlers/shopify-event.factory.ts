import { CustomerCreate } from "./customer-create";
import { OrderCreate } from "./order-create";
import { RefundCreate } from "./refund-create";
import { CheckoutCreate } from "./checkout-create";

export class ShopifyEventFactory {
    static constructEventHandler(eventName: string) {
        switch (eventName) {
            case OrderCreate.eventName:
                return new OrderCreate();
            case RefundCreate.eventName:
                return new RefundCreate();
            case CustomerCreate.eventName:
                return new CustomerCreate();
			case CheckoutCreate.eventName:
				return new CheckoutCreate();
            default:
                console.log(`Event Handler not defined`);
                return undefined;
        }
    }
}
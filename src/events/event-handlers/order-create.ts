import { SHOPIFY_API_VERSION, getAPIaccessToken } from "../../utils/shopify-helper";
import { WEBAPP_API_URL } from "../webhook-worker";
import { EventData, ShopifyEvent } from "./shopify-event";
import fetch from 'node-fetch';
import { SQS } from "aws-sdk";
import { EarnCondition } from "../conditions/earn-condition";
import { DollarSpent } from "../conditions/dollar-spent";
import { ShopifyPointEffect } from "../effects/shopify-point-effect";
import { ShopifySegmentCondition } from "../conditions/shopify-segment";
import { ShopifyNthPurchaseCondition } from "../conditions/nth-purchase";
import { BuyProduct } from "../conditions/buy-product";
import { BuyCollectionProduct } from "../conditions/buy-collection-product";
import { BuySpecificProduct } from "../conditions/buy-specific-product";
import { Client } from "pg"; // Use the 'pg' library for PostgreSQL
import { checkActiveLoyalty, getDatabaseInfo } from '../../utils/raleon-helper';

type ConditionConstructor = new (payload: any, campaign: any) => EarnCondition;

const ConditionClasses: Record<string, ConditionConstructor> = {
	"dollar-spent": DollarSpent,
	"shopify-segment": ShopifySegmentCondition,
	"nth-purchase": ShopifyNthPurchaseCondition,
	"timed-purchase": ShopifyNthPurchaseCondition,
	"first-loyalty-purchase": ShopifyNthPurchaseCondition,
	"buy-product": BuyProduct,
	"collection-product-purchase": BuyCollectionProduct,
	"specific-product-purchase": BuySpecificProduct,
	"default": DollarSpent,
};


export class OrderCreate extends ShopifyEvent {
	sqs = new SQS();

	static eventName: string = 'orders/create';

	protected async getCustomerIdAndDataFromMessage(message: any): Promise<{ customerId: string; checkoutId: string } & Partial<EventData>> {
		return {
			customerId: message.customer?.id,
			checkoutId: message.checkout_id,
		};
	}

	private async updateCheckoutStatus(checkoutId: string, customerId: string) {
		const databaseInfo = await getDatabaseInfo();
		const client = new Client({
			user: databaseInfo.username,
			host: databaseInfo.host,
			database: databaseInfo.dbname,
			password: databaseInfo.password,
			port: parseInt(databaseInfo.port, 10),
		});
	
		try {
			await client.connect();
			console.log("Connected to PostgreSQL");
	
			// Use UPDATE to set the status to 'completed'
			const query = `
				UPDATE cartdata
				SET status = $1, customerid = $2
				WHERE checkoutid = $3;
			`;
	
			await client.query(query, [
				'completed',
				customerId,
				checkoutId,
			]);
	
			console.log(`Checkout status updated to 'completed' for checkout ${checkoutId}`);
		} catch (error) {
			console.error(`Error updating checkout status: ${error}`);
		} finally {
			await client.end();
			console.log("PostgreSQL connection closed");
		}
	}

	async handlePreprocessedEvent(data: EventData): Promise<any> {
		if(data.checkoutId ) {
			console.log("updating checkout status for checkoutId", data.checkoutId);
			await this.updateCheckoutStatus(data.checkoutId, data.customerId);
		}
		else {
			console.log("No checkoutId found in data");
		}

		data.message.raleon = {};
		if(data.customerId == null || data.message.source_name == 'shopify_draft_order') {
			await this.sendToDataQueue(data.message);
			return;
		}

		if (data.raleonUserId == null) {
			console.log(`No raleon user found for customer ${data.customerId}`);
			const userIdentity: any = await this.createRaleonUser(data);
			data.raleonUserId = userIdentity.raleonUserId;
		}
		const hasActiveLoyalty = await checkActiveLoyalty(data.shopInfo.orgId);
		await this.resolveDiscounts(data);
		if (hasActiveLoyalty) {
			await this.grantRewards(data);
		}
		await this.sendToDataQueue(data.message);
	}

	private async resolveDiscounts(data: EventData) {
		console.log(`resolving discounts`);
		const discounts = data.message.discount_codes;
		if (!discounts || !discounts.length) {
			console.log(`No discounts applied`);
			return;
		}
		let totalLoyaltySpend = 0;
		for (let discount of discounts) {
			console.log(`discount: ${JSON.stringify(discount)}`);
			console.log(data.raleonUserId);
			try {
				const response = await this.fetch(
					data.sessionToken,
					`${WEBAPP_API_URL}/inventory-coupons/used`,
					'POST',
					JSON.stringify({
						discountCode: discount.code,
						amount: discount.amount,
						amountType: this.getAmountType(discount.type),
						raleonUserId: data.raleonUserId
					})
				);
				console.log(`response: ${JSON.stringify(response)}`);
				if (response && response.statusCode === 200) { 
					totalLoyaltySpend += discount.amount;
				}
			} catch (e) {
				console.log(`Error resolving discount`, e);
			}
		}
		if(totalLoyaltySpend > 0) {
			data.message.raleon.totalLoyaltySpend = totalLoyaltySpend;
			await this.saveMetaField(data, totalLoyaltySpend, data.message.currency);
		}
	}

	private async grantRewards(data: EventData) {
		const {sessionToken, raleonUserId, customerId, message, shopInfo} = data;

		const loyaltyProgram = await this.getLoyaltyProgramForEnvironment(
			sessionToken,
			raleonUserId,
			customerId
		);

		try {
			await this.processLoyaltyPrograms([loyaltyProgram], message, sessionToken, raleonUserId, customerId, shopInfo.orgId);
		} catch (e) {
			console.error(`Error processing loyalty program`, e);
			return {
				body: "Error processing loyalty program",
				statusCode: 404,
			};
		}

		try {
			if (message["total_price"]) {
				let referralresponse = await this.grantReferralBonusAndKickback(
					sessionToken,
					raleonUserId,
					shopInfo.orgId,
					customerId
				)
				if (referralresponse) {
					console.log(`Referral bonus and kickback granted`);
					message.raleon.referral = true;
				}
			}
		} catch (e) {
			console.error(`Error granting referral bonus and kickback`, e);
		}
	}

	private async sendToDataQueue(message: any) {
		const params = {
			MessageBody: JSON.stringify(message),
			QueueUrl: process.env.QUEUE_URL as string,
		};

		try {
			await this.sqs.sendMessage(params).promise();
			console.log('Message added to the- data queue:', message);
		} catch (error) {
			console.error('Error sending message to the queue:', error);
		}
	}


	private async saveMetaField(data: EventData, discountAmount: any, currency: any) {
		const apitoken = await getAPIaccessToken(data.message.shopDomain);
		
		const mutation = `
			mutation MetafieldsSet($metafields: [MetafieldsSetInput!]!) {
				metafieldsSet(metafields: $metafields) {
					metafields {
						key
						namespace
						value
						createdAt
						updatedAt
					}
					userErrors {
						field
						message
						code
					}
				}
			}`;
	
		const variables = {
			metafields: [
				{
					key: 'loyalty_spend',
					namespace: 'raleonInfo',
					ownerId: `gid://shopify/Order/${data.message.id}`,
					type: "money",
					value: JSON.stringify({
						amount: parseFloat(discountAmount),
						currency_code: currency 
					})
				}
			]
		};
		console.log(`variables: ${JSON.stringify(variables)}`);
		
		const response = await fetch(`https://${data.message.shopDomain}/admin/api/${SHOPIFY_API_VERSION}/graphql.json`, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				"X-Shopify-Access-Token": apitoken,
			},
			body: JSON.stringify({
				query: mutation,
				variables: variables
			})
		});
		
		const jsonResponse: any = await response.json();
		console.log(`jsonResponse: ${JSON.stringify(jsonResponse)}`);
		if (jsonResponse.data.metafieldsSet.userErrors.length) {
			throw new Error(`Failed to set metafield: ${jsonResponse.data.metafieldsSet.userErrors[0].message}`);
		}
		
		return jsonResponse;
	}

	private getAmountType(shopifyAmountType: string): string {
		if (shopifyAmountType == 'fixed_amount') {
			return '$'
		}
		if (shopifyAmountType == 'percentage') {
			return '%'
		}
		return '$';
	}


	private async processLoyaltyPrograms(
		loyaltyPrograms: any[],
		payload: any,
		sessionToken: string,
		raleonUserId: string,
		customerId: string,
		orgId: string
	): Promise<void> {
		console.log("processing loyalty programs");

		let pointsMultiplier = 1;

		try {
			const staticEffects = loyaltyPrograms.map((program) => (program?.loyaltyCampaigns || [])).flat().map((campaign) => (campaign?.staticEffects || [])).flat();
			console.log(`static effects 👉`, staticEffects);
			const pointMultipliers = staticEffects.filter(x => x && x.loyaltyRewardDefinition?.rewardCoupon?.amountType === 'points-multiplier');
			console.log(`point multipliers 👉`, pointMultipliers);
			pointsMultiplier = Math.max(1, ...pointMultipliers.map(x => (x?.loyaltyRewardDefinition?.rewardCoupon?.amount || 1)));
		} catch (e) {
			console.error(`Error getting points multiplier`, e);
		}

		for (let program of loyaltyPrograms) {
			if (program.loyaltyCampaigns) {
				console.log("processing loyalty campaigns");
				const excludedTags = program.excludedTags ? program.excludedTags.split(',').map((tag: string) => tag.toLowerCase()) : [];
				const customerTags = payload.customer?.tags ? payload.customer.tags.split(',').map((tag: string) => tag.toLowerCase()) : [];

				for (const excludedTag of excludedTags) {
					if (customerTags.includes(excludedTag)) {
						console.log(`Customer is excluded from loyalty program due to tag: ${excludedTag}`);
					}
				}
				for (let campaign of program.loyaltyCampaigns) {
					if (campaign.loyaltyEarns) {
						console.log("processing loyalty earns");
						for (let earn of campaign.loyaltyEarns) {
							let result = true;
							if (earn.earnConditions) {
								console.log("processing loyalty conditions");
								const groupedConditions = this.groupByType(earn.earnConditions, payload.topic);
								if(Object.keys(groupedConditions).length === 0) {
									result = false; 
									console.log("ERROR: no conditions found for topic");
									continue;
								}
								for (let type in groupedConditions) {
									const conditionsOfType = groupedConditions[type];
									const earnCondition = this.createCondition(type, payload, campaign);
									if (earnCondition) {
										let evaluationResult = await earnCondition?.evaluate(conditionsOfType);
										console.log(`evaluationResult 👉`, evaluationResult);
										if (!evaluationResult) {
											result = false;
											break;
										}
									}
									else {
										console.log(`No condition found for type: ${type}`);
										result = false;
									}
								}
							}	
							if (earn.earnEffects && result) {
								for (let effect of earn.earnEffects) {
									const effectClass = new ShopifyPointEffect();
									await effectClass.processEffect(sessionToken, raleonUserId, customerId, orgId, effect, payload, earn, pointsMultiplier, payload.id);
									console.log("effect 👉", effect);
								}
							}
						}
					}
				}
			}
		}
	}

	private async grantReferralBonusAndKickback(
		sessionToken: string,
		raleonUserId: string,
		orgId: string,
		customerId: string
	) {
		console.log(`giving referral bonus and kickbacks, if applicable`)
		console.log(`orgCust \u{1F449}`, orgId + "-" + customerId);
		
		let response = await this.fetch(
			sessionToken,
			`${WEBAPP_API_URL}/loyalty-grant-referral-bonus-and-kickback`,
			"POST",
			JSON.stringify({
				raleonUserId,
			})
		);
		console.log(`response 👉`, response);
		return response;
	}


	private groupByType(conditions: any[], topic: string) {
		return conditions.reduce((acc, condition) => {
			if (condition.triggeredEvent == topic) {
				if (!acc[condition.type]) {
					acc[condition.type] = [];
				}
				acc[condition.type].push(condition);
			}
			return acc;
		}, {});
	}

	private createCondition(type: string, payload: any, campaign: any): EarnCondition | null {
		const ConditionClass = ConditionClasses[type];
		if (ConditionClass) {
			return new ConditionClass(payload, campaign);
		}
		console.log(`No condition class found for type: ${type}`);
		return null;
	}

	private async getLoyaltyProgramForEnvironment(sessionToken: string, raleonUserId: any, customerId: any) {
		console.log(`getting loyaltyPrograms`);
		const response = await this.fetch(
			sessionToken,
			`${WEBAPP_API_URL}/loyalty-programs/live/${raleonUserId}/${customerId}`,
			"GET"
		);
		console.log("loyaltyProgramData 👉", JSON.stringify(response, null, 2));
		return response[0];
	}
}
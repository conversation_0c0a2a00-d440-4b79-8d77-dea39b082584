import { EventData, ShopifyEvent } from "./shopify-event";
import fetch from "node-fetch";
import { SHOPIFY_API_VERSION, getAPIaccessToken, getAuthToken, getShopInfoByShopDomain } from "../../utils/shopify-helper";
import { WEBAPP_API_URL } from "../webhook-worker";
import { addAthenaPartitionsOrg } from "../../utils/athena-helpers";
import { PutObjectCommand, S3Client } from "@aws-sdk/client-s3";
import { ParquetWriter } from "@dsnp/parquetjs";
import { refundSchema } from '../../ingestion/order-schema';
import { checkActiveLoyalty } from "../../utils/raleon-helper";

const SHOPIFY_ENDPOINT = `https://[domain]/admin/api/${SHOPIFY_API_VERSION}/graphql.json`;
const fs = require('fs');

const s3Client = new S3Client({});

export class RefundCreate extends ShopifyEvent {
    static eventName = "refunds/create";

    protected async getCustomerIdAndDataFromMessage(message: any): Promise<{ customerId: string; } & Partial<EventData>> {
		const refund = message as Refund;
		const orderId = refund.transactions[0].order_id;
		console.log(`orderId 👉`, orderId);

		let shopifyURL = SHOPIFY_ENDPOINT.replace(
            "[domain]",
            message.shopDomain
        );
        console.log(`shopifyURL 👉`, shopifyURL);
        const query = `
			query {
				node(id: "gid://shopify/Order/${orderId}") {
					id
					... on Order {
                        customer {
                            id
                        }
						totalPriceSet {
							shopMoney {
								amount
								currencyCode
							}
						}
					}
				}
			}
        `;

        const apitoken = await getAPIaccessToken(message.shopDomain);
        const response = await fetch(shopifyURL, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "X-Shopify-Access-Token": apitoken,
            },
            body: JSON.stringify({ query }),
        });

        const queryData: any = await response.json();
        const fullCustomerId = queryData.data.node.customer.id;
        const customerId = fullCustomerId.split("/").pop();
        const originalAmount = queryData.data.node.totalPriceSet.shopMoney.amount;

        return {
            customerId,
            additionalData: {
                originalAmount
            }
        }
    }

    protected async handlePreprocessedEvent(data: EventData): Promise<any> {
        console.log(`handling ${RefundCreate.eventName}`);

        const { message, customerId, raleonUserId } = data;

		if(customerId == null || message.source_name == 'shopify_draft_order') {
			return;
		}
		
		if (raleonUserId == null) {
			console.log(`No raleon user found for customer ${customerId}`);
			return;
		}

        return this.processRefundCreate(data);
    }

	private async processRefundCreate(data: EventData) {
        const refund = data.message as Refund;
        const originalAmount = data.additionalData?.originalAmount;
        const orderId = refund.transactions[0].order_id;
        let storeInfo = await getShopInfoByShopDomain(data.message.shopDomain);
        const hasActiveLoyalty = await checkActiveLoyalty(storeInfo.orgId);

		let transactionKind = '';

		const totalRefund = refund.transactions.reduce((acc, transaction) => {
			if(transaction.kind === 'refund') {
				const amount = Number(transaction.amount);
				if (!isNaN(amount)) {
					acc += amount;
				}
			}
			transactionKind = transaction.kind;
			return acc;
		}, 0);
		console.log(`totalRefund 👉`, totalRefund);
		if(totalRefund === 0 && transactionKind == 'refund') {
			return;
		}	
		if(hasActiveLoyalty) {
			let referralresponse = await this.processRefundBalanceChange(
				data,
				originalAmount,
				totalRefund,
				orderId,
				transactionKind
			)
			if (referralresponse) {
				console.log(`Refund balance change processed`);
			}
		}
		else {
			console.log(`No active loyalty program found for orgId: ${storeInfo.orgId}`);
		}
		await this.saveRefundData(data.message, totalRefund, storeInfo);
	}

	async saveRefundData(message: any, totalRefund: number, storeInfo: any) {
		console.log(`saving refund data`);
		if (totalRefund === 0) {
			return;
		}
		let writer = await ParquetWriter.openFile(refundSchema, `/tmp/refund_${message.id}.parquet`);
		await writer!.appendRow({order_id: message.order_id, refund_amount: totalRefund});
		await writer!.close();
		const params = {
			Bucket: process.env.OUTPUT_BUCKET,
			Key: `shopify_order_refunds/organization=${storeInfo.orgId}/${message.id}.parquet`,
			Body: fs.createReadStream(`/tmp/refund_${message.id}.parquet`),
		};

		const command = new PutObjectCommand(params);
		await s3Client.send(command);
		fs.unlinkSync(`/tmp/refund_${message.id}.parquet`);
		const database = process.env.ATHENA_DB || 'ecommerce';
		if (!process.env.OUTPUT_BUCKET) {
			throw new Error(
				"CURATED_OUTPUT_BUCKET environment variable is not set"
			);
		}
		console.log(`refund data saved`);
	}

	private async processRefundBalanceChange(
		data: EventData,
		originalAmount: number,
		totalRefund: number,
		orderId: number,
		kind: string
	) {
        const { sessionToken, raleonUserId, customerId, orgId } = data;

		console.log(`processing balance change for refund`)
		console.log(`orgCust \u{1F449}`, orgId + "-" + customerId);
		
        console.log('posting: ', {
            raleonUserId,
            orderId,
            originalAmount,
            totalRefund,
			kind
        });

		let response = await this.fetch(
			sessionToken,
			`${WEBAPP_API_URL}/loyalty-currencies/refund-balance-change`,
			"POST",
			JSON.stringify({
				raleonUserId,
				orderId,
				originalAmount,
				totalRefund,
				kind
			})
		);
		console.log(`response 👉`, response);
		return response;
	}
}

interface Refund {
	"created_at": string,
	"duties": {
	  "duties": Array<
		{
		  "duty_id": number,
		  "amount_set": {
			"shop_money": {
			  "amount": string,
			  "currency_code": string
			},
			"presentment_money": {
			  "amount": string,
			  "currency_code": string
			}
		  }
		}
	  >
	},
	"id": number,
	"note": string,
	"order_adjustments": Array<
	  {
		"id": number,
		"order_id": number,
		"refund_id": number,
		"amount": string,
		"tax_amount": string,
		"kind": "shipping_refund" | string,
		"reason": string,
		"amount_set": {
		  "shop_money": {
			"amount": number,
			"currency_code": string
		  },
		  "presentment_money": {
			"amount": number,
			"currency_code": string
		  }
		},
		"tax_amount_set": {
		  "shop_money": {
			"amount": number,
			"currency_code": string
		  },
		  "presentment_money": {
			"amount": number,
			"currency_code": string
		  }
		}
	  }
	>,
	"processed_at": string,
	"refund_duties": Array<
	  {
		"duty_id": number,
		"refund_type": "FULL"|"PROPORTIONAL"
	  }
	>,
	"refund_line_items": [
	  {
		"id": number,
		"line_item": any,
		"line_item_id": number,
		"quantity": number,
		"location_id": number,
		"restock_type": "no_restock"|"cancel"|"return"|"legacy_restock",
		"subtotal": number,
		"total_tax": number,
		"subtotal_set": {
		  "shop_money": {
			"amount": number,
			"currency_code": string
		  },
		  "presentment_money": {
			"amount": number,
			"currency_code": string
		  }
		},
		"total_tax_set": {
		  "shop_money": {
			"amount": number,
			"currency_code": string
		  },
		  "presentment_money": {
			"amount": number,
			"currency_code": string
		  }
		}
	  }
	],
	"restock": boolean,
	"transactions": Array<Transaction>,
	"user_id": number
  }
  
  interface Transaction {
	  "id": number,
	  "order_id": number,
	  "amount": string,
	  "kind": "authorization"|"capture"|"sale"|"void"|"refund",
	  "gateway": "shopify_payments",
	  "status": "pending"|"failure"|"success"|"error",
	  "message": string|null,
	  "created_at": string,
	  "test": boolean,
	  "authorization": string,
	  "currency": string,
	  "location_id": string|null,
	  "user_id": string|null,
	  "parent_id": number,
	  "device_id": string|null,
	  "receipt": any,
	  "error_code": "incorrect_number"|"invalid_number"|"invalid_expiry_date"|"invalid_cvc"|"expired_card"|"incorrect_cvc"|"incorrect_zip"|"incorrect_address"|"card_declined"|"processing_error"|"call_issuer"|"pick_up_card",
	  "source_name": "web"|"pos"|"iphone"|"android"
	}

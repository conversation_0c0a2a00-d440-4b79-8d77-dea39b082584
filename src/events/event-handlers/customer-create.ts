import { EventData, ShopifyEvent } from "./shopify-event";
import fetch from "node-fetch";
import { getShopInfoByShopDomain } from "../../utils/shopify-helper";

export class CustomerCreate extends ShopifyEvent {
    static eventName = "customers/create";

	protected async getCustomerIdAndDataFromMessage(message: any): Promise<{ customerId: string; } & Partial<EventData>> {
		return { customerId: message.id };
	}

	async handleEvent(message: any): Promise<any> {
		const data = await this.getCustomerIdAndDataFromMessage(message);
		data.message = data.message || message;
		
		data.shopInfo = data.shopInfo || await getShopInfoByShopDomain(data.message.shopDomain);
		data.orgId = data.orgId || data.shopInfo.orgId;			

		return this.handlePreprocessedEvent(data as EventData);
	}

    protected async handlePreprocessedEvent(data: EventData): Promise<any> {
		console.log(`Data: ${JSON.stringify(data, null, 2)}`);

		const payload: any = {
			eventName: 'raleon_new_customer',
			friendlyName: 'Raleon - New Customer',
			orgId: data.orgId,
			customerId: `${data.customerId}`,
			timestamp: new Date().toISOString(),
			data: {
				customerId: `${data.customerId}`,
			},
		};

		const response = await fetch(process.env.STREAMING_API_URL!, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify(payload),
		});

		if (!response.ok) {
			throw new Error(`Error posting data to API: ${response.statusText}`);
		}

		return {
			statusCode: 200,
			body: JSON.stringify("Event processed successfully!"),
		};
    }
}
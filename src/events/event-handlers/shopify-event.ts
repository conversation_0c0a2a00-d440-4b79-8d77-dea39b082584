import { getShopInfoByShopDomain, getAuthToken, fetchShopifyAdmin, getShopifyCustomerJwt } from "../../utils/shopify-helper";
import { WEBAPP_API_URL } from "../webhook-worker";

export abstract class ShopifyEvent {
	static eventName: string;

	async handleEvent(message: any): Promise<any> {
		const data = await this.getCustomerIdAndDataFromMessage(message);
		data.message = data.message || message;
		
		try {
			data.shopInfo = data.shopInfo || await getShopInfoByShopDomain(data.message.shopDomain);
			data.orgId = data.orgId || data.shopInfo.orgId;
			data.sessionToken = data.sessionToken || await getAuthToken(data.shopInfo.accessToken);
			
			data.raleonUserId = data.raleonUserId || await this.getRaleonUserId(
				data.sessionToken!,
				data.customerId
			);
			console.log("raleonUserId 👉", data.raleonUserId);

			const shopData = await fetchShopifyAdmin(
				data.message.shopDomain,
				'get-shop-info',
				'GET',
			);

			if (!data.additionalData) {
				data.additionalData = {};
			}
			data.additionalData.loginRequiredAtCheckout = shopData.shop.customerAccountsV2 && shopData.shop.customerAccountsV2.loginRequiredAtCheckout;

			return this.handlePreprocessedEvent(data as EventData);
		} catch(e) {
			console.error("Error getting raleon user id", e);
			return;
		}
	}

	protected abstract getCustomerIdAndDataFromMessage(message: any): Promise<{ customerId: string } & Partial<EventData>>;
	protected abstract handlePreprocessedEvent(data: EventData): Promise<any>;

	protected async getRaleonUserId(sessionToken: string, customerId: string) {
		console.log(`getting raleon user`);
		try{
			const response = await this.fetch(
				sessionToken,
				`${WEBAPP_API_URL}/raleon-users/from-customer/${customerId}`,
				"GET"
			);

			console.log("raleon user 👉", JSON.stringify(response, null, 2));
			return response?.raleonUserId;
		}
		catch(e) {
			console.error("Error getting raleon user id", e);
			return;
		}
	}

	protected async fetch(
		authToken: string,
		url: string,
		method: string,
		body?: string
	): Promise<any> {
		let response;
		const options: any = {
			method,
			headers: {
				"Content-Type": "application/json",
				Authorization: `Bearer ${authToken}`,
				'ngrok-skip-browser-warning': true,
			},
		};
		if (body) options.body = body;


		console.log('fetching', url, options)

		try {
			response = await fetch(url, options);
			if (!response.ok || response.status >= 300 || response.status < 200) {
				console.error(`HTTP error! Status: ${response.status}`);
				throw new Error(`HTTP error! Status: ${response.status}`);
			}

            console.log('fetch response', response)

			if (response.headers.get('content-type')?.includes('application/json')) {
				const result = await response.json();
                console.log('JSON result', result)
                return result;
			} else {
				console.warn("Response is not JSON");
				return null;
			}
		} catch (e) {
			console.error("error 👉", e);
            throw e;
		}
	}

	protected async createRaleonUser(data: EventData): Promise<any> {
		const jwt = await getShopifyCustomerJwt(data.message.shopDomain, data.customerId)
		const response = await this.fetch(
			jwt,
			`${WEBAPP_API_URL}/raleon-user`,
			"POST",
		);
		console.log("create raleon user 👉", JSON.stringify(response, null, 2));
		return response;
	}
}

export interface EventData {
	message: any,
	shopInfo: any,
	customerId: string,
	sessionToken: string,
	orgId: string,
	raleonUserId: string,
	checkoutId?: string,
	additionalData?: any,
}
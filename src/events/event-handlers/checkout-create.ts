import { EventData, ShopifyEvent } from "./shopify-event";
import { Client } from "pg"; // Use the 'pg' library for PostgreSQL
import { getDatabaseInfo } from '../../utils/raleon-helper';
import { SHOPIFY_API_VERSION, getShopInfoByShopDomain, getAPIaccessToken } from "../../utils/shopify-helper";
import fetch from 'node-fetch';

export class CheckoutCreate extends ShopifyEvent {
    static eventName = "checkouts/create";

    protected async getCustomerIdAndDataFromMessage(message: any): Promise<{ customerId: string } & Partial<EventData>> {
        console.log(`Getting customer ID and additional data from message for ${JSON.stringify(message)}`);
        const checkout = message;
        const checkoutId = checkout.id;

        const customerId = checkout.customer?.id;

        if (customerId) {
            console.log("Customer ID:", customerId);
        } else {
            console.log("No customer data found.");
        }

        return {
            customerId: customerId || '',
            additionalData: {
                checkoutId,
                createdAt: new Date().toISOString()
            },
        };
    }

    protected async handlePreprocessedEvent(data: EventData): Promise<any> {
        console.log(`handling ${CheckoutCreate.eventName}`);
        console.log(`Handle Preprocessed Event Data: ${JSON.stringify(data)}`);

        let { message, customerId, raleonUserId, additionalData, orgId } = data;

        if (customerId == null) {
            console.log(`No customer found for checkout ${message.payload.id}`);
            customerId = '';
        }

        if (raleonUserId == null) {
            console.log(`No raleon user found for customer ${customerId}`);
            raleonUserId = '';
        }

        const status = 'active';

        await this.storeCheckoutDataInPostgres({
            checkoutId: additionalData?.checkoutId,
            customerId,
            lastUpdateTime: additionalData?.createdAt,
            raleonUserId: Number(raleonUserId),
            orgId: Number(orgId),
            status
          });
    }

    private async storeCheckoutDataInPostgres(checkoutData: {
        checkoutId: string;
        customerId: string;
        lastUpdateTime: string;
        raleonUserId: number;
        orgId: number;
        status: string;
      }) {
        const { checkoutId, customerId, lastUpdateTime, status, orgId, raleonUserId } = checkoutData;

        // PostgreSQL connection configuration
        const databaseInfo = await getDatabaseInfo();
        const client = new Client({
            user: databaseInfo.username,
            host: databaseInfo.host,
            database: databaseInfo.dbname,
            password: databaseInfo.password,
            port: parseInt(databaseInfo.port, 10),
        });

        try {
            await client.connect();
            console.log("Connected to PostgreSQL");

            // Use INSERT ... ON CONFLICT to handle duplicates
            const query = `
                INSERT INTO cartdata (checkoutid, customerid, lastupdatetime, status, orgid, raleonuserid)
                VALUES ($1, $2, $3, $4, $5, $6)
                ON CONFLICT (checkoutid)
                DO UPDATE SET
                customerid = EXCLUDED.customerid,
                lastupdatetime = EXCLUDED.lastupdatetime,
                orgid = EXCLUDED.orgid,
                raleonuserid = EXCLUDED.raleonuserid,
                status = EXCLUDED.status;`

            await client.query(query, [
                checkoutId,
                customerId,
                lastUpdateTime,
                status,
                orgId,
                raleonUserId
            ]);
            console.log(`Checkout data stored/updated for checkout ${checkoutId}`);
        } catch (error) {
            console.error(`Error storing/updating checkout data: ${error}`);
        } finally {
            await client.end();
            console.log("PostgreSQL connection closed");
        }
    }
}
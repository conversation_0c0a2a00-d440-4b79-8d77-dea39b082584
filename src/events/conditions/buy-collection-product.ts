import { EarnCondition } from "./earn-condition";
import { getAPIaccessToken, getCollectionProducts, ShopifyCollectionProduct } from "../../utils/shopify-helper";
export class BuyCollectionProduct extends EarnCondition {
  
    async evaluate(condition: any[]): Promise<boolean>  {
        //find the collection id
        let collectionId = condition.find(cond => cond.variable === 'collectionId')?.textValue;
        let purchaseCount = condition.find(cond => cond.variable === 'purchaseCount')?.amount || 1;

        if (!collectionId) {
            console.error('Collection ID not found in the condition');
            return false;
        }

        // let shopifyURL = `https://${this.payload.shopDomain}/admin/api/${SHOPIFY_API_VERSION}/collections/${collectionId}/products.json?limit=250`;
        // const apitoken = await getAPIaccessToken(this.payload.shopDomain);
        // const response = await fetch(shopifyURL, {
        //     method: "GET",
        //     headers: {
        //         "Content-Type": "application/json",
        //         "X-Shopify-Access-Token": apitoken,
        //     }
        // });

        // const data: any = await response.json();
        // console.log('data.products', data.products)
        let products = await this.getAllProducts(collectionId);

        //Loop through each line item in the payload and check if the product is in the collection
        let count = 0;
        for (const lineItem of this.payload.line_items) {
            for (const product of products) {
                console.log('product.id', product.id, 'lineItem.product_id', lineItem.product_id);
                if (String(lineItem.product_id) === String(product.id)) {
                    count++;
                }
            }
        }

        console.log('count', count, 'purchaseCount', purchaseCount);
        if(count >= purchaseCount) {
            return true;
        }
        return false;    
    }

    async getAllProducts(collectionId: string): Promise<ShopifyCollectionProduct[]> {
        const apitoken = await getAPIaccessToken(this.payload.shopDomain);
        const products = await getCollectionProducts(
            this.payload.shopDomain,
            apitoken,
            collectionId
        );
        
        console.log("allProducts", products);
        return products;
    }
}

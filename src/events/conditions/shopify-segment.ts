import { EarnCondition } from "./earn-condition";
import fetch from "node-fetch";
import { getAPIaccessToken, SHOPIFY_API_VERSION } from "../../utils/shopify-helper";
export class ShopifySegmentCondition extends EarnCondition {
    private SHOPIFY_ENDPOINT = `https://[domain]/admin/api/${SHOPIFY_API_VERSION}/graphql.json`;

    async evaluate(conditions: any[]): Promise<boolean> {
        let shopifyURL = this.SHOPIFY_ENDPOINT.replace(
            "[domain]",
            this.payload.shopDomain
        );
        const customerId = this.payload.customer?.id;
        console.log(`shopifyURL 👉`, shopifyURL);
        let apitoken = await getAPIaccessToken(this.payload.shopDomain);

        const segmentIds = conditions.map(condition => `gid://shopify/Segment/${condition.variable}`);

        const query = `
            query {
                customerSegmentMembership(customerId:"gid://shopify/Customer/${customerId}", segmentIds:[${segmentIds.join(",")}]) {
                    memberships{isMember}
                }
            }
        `;

        const response = await fetch(shopifyURL, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "X-Shopify-Access-Token": apitoken,
            },
            body: JSON.stringify({ query }),
        });

        const data: any = await response.json();
        const memberships = data.data.customerSegmentMembership.memberships;

        // Check if customer is a member of all segments
        const isMemberOfAll = memberships.every((membership: any) => membership.isMember);

        console.log(`data 👉`, isMemberOfAll);
        return isMemberOfAll;
    }
}

import { EarnCondition } from "./earn-condition";
export class BuySpecificProduct extends EarnCondition {
  
    async evaluate(condition: any[]): Promise<boolean>  {
        for (const lineItem of this.payload.line_items) {

            console.log(`lineItem.id 👉`, lineItem.product_id, 'condition.textValue', condition[0]?.textValue, 'condition', condition);
            if (lineItem.product_id.toString() == condition[0]?.textValue) {
                return true;
            }
        }
        return false;    
    }
}
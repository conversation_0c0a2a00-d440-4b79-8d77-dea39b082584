import { EarnCondition } from "./earn-condition";
export class DollarSpent extends EarnCondition {
    private value: number;
  
    async evaluate(condition: any[]): Promise<boolean>  {
      this.value = this.payload["total_price"];
      console.log(`this.value 👉`, this.value);
      console.log(`condition 👉`, condition);
      switch (condition[0].operator) {
        case '>':
          return this.value > condition[0].amount;
        case '<':
          return this.value < condition[0].amount;
        case '==':
          return this.value === condition[0].amount;
        case '>=':
          return this.value >= condition[0].amount;
        case '<=':
          return this.value <= condition[0].amount;
        default:
          return false;
      }
    }
  }
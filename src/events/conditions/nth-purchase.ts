import { EarnCondition } from "./earn-condition";
import fetch from "node-fetch";
import { getAPIaccessToken, SHOPIFY_API_VERSION } from "../../utils/shopify-helper";

export class ShopifyNthPurchaseCondition extends EarnCondition {
    private value: number;

    async evaluate(conditions: any[]): Promise<boolean> {
        const customerId = this.payload.customer?.id;
        let shopifyURL = `https://${this.payload.shopDomain}/admin/api/${SHOPIFY_API_VERSION}/orders.json?customer_id=${customerId}[createdat]&fields=id,cancelled_at,metafields&status=ANY`;
        const apitoken = await getAPIaccessToken(this.payload.shopDomain);

        let dateCondition = conditions.find(cond => cond.type === 'timed-purchase');
        if (dateCondition && this.campaign.startdate) {
            const startDate = new Date(this.campaign.startdate);
            const dateStr = startDate.toISOString();
            shopifyURL = shopifyURL.replace("[createdat]", `&created_at_min=${dateStr}`);
        }
        else {
            shopifyURL = shopifyURL.replace("[createdat]", '');
        }
        console.log(`ShopifyNthPurchaseCondition: shopifyURL: ${shopifyURL}`);
        const response = await fetch(shopifyURL, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                "X-Shopify-Access-Token": apitoken,
            }
        });

        const data: any = await response.json();
        const nonCancelledOrders = data.orders.filter((order: any) => !order.cancelled_at);
        this.value = nonCancelledOrders.length;

        for (let condition of conditions) {
            if (condition.variable === 'purchaseCount' &&
                !this.evaluateCondition(this.value, condition.operator, condition.amount)) {
                return false;
            }
        }

        return true;
    }

    private evaluateCondition(value: number, operator: string, amount: number): boolean {
        console.log(`value: ${value}, operator: ${operator}, amount: ${amount}`);
        switch (operator) {
            case '>':
                return value > amount;
            case '<':
                return value < amount;
            case '==':
                return value === amount;
            case '>=':
                return value >= amount;
            case '<=':
                return value <= amount;
            default:
                return false;
        }
    }
}

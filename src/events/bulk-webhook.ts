import { APIGatewayProxyResultV2, SQSEvent } from "aws-lambda";
import fetch from "node-fetch";
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { Upload } from "@aws-sdk/lib-storage";
import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import { DynamoDBDocumentClient, GetCommand } from "@aws-sdk/lib-dynamodb";
import { SFNClient, SendTaskSuccessCommand } from "@aws-sdk/client-sfn";
import { getAPIaccessToken, getShopInfoByShopDomain, SHOPIFY_API_VERSION } from "../utils/shopify-helper";
import { Readable, Transform } from 'stream';

const s3Client = new S3Client({});
const ddbClient = new DynamoDBClient({});
const ddbDocClient = DynamoDBDocumentClient.from(ddbClient);
const sfnClient = new SFNClient({});

export class Handler {
    async main(event: SQSEvent): Promise<APIGatewayProxyResultV2> {
        const messages = event.Records.map((record: any) => {
            try {
                const message = JSON.parse(record.body);
                console.log(`webhook event ${JSON.stringify(message)}`);
                const payload = message.detail.payload;
                return {
                    ...payload,
                    topic: message.detail.metadata["X-Shopify-Topic"],
                    shopDomain: message.detail.metadata["X-Shopify-Shop-Domain"],
                    bulkOperationId: message.detail.payload.admin_graphql_api_id.split('/').pop(),
                    adminGraphqlApiId: message.detail.payload.admin_graphql_api_id,
                };
            } catch (error) {
                console.error(`Error parsing record: ${record.body}`, error);
                return null;
            }
        }).filter(m => m !== null);

        for (let message of messages) {
            const shopInfo = await getShopInfoByShopDomain(message.shopDomain);
            const apitoken = await getAPIaccessToken(message.shopDomain);

            try {
                const graphqlQuery = JSON.stringify({
                    query: `{
                        node(id: "${message.adminGraphqlApiId}") {
                            ... on BulkOperation {
                                url
                                partialDataUrl
                            }
                        }
                    }`
                });
                console.log('GraphQL query:', graphqlQuery);

                const shopifyUrl = `https://${message.shopDomain}/admin/api/${SHOPIFY_API_VERSION}/graphql.json`;
                const response = await fetch(shopifyUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Shopify-Access-Token': apitoken
                    },
                    body: graphqlQuery
                });

                const responseJson = await response.json() as any;
                console.log('Bulk operation response:', responseJson);
                const bulkOperationUrl = responseJson.data.node.url;

                let s3Location = null;
                
                if (bulkOperationUrl) {
                    // Stream download
                    const bulkDataResponse = await fetch(bulkOperationUrl);
                    if (!bulkDataResponse.ok || !bulkDataResponse.body) {
                        throw new Error('Failed to download bulk data');
                    }

                    // Set up error checking transform stream
                    let hasErrors = false;
                    let lineBuffer = '';
                    const errorCheckStream = new Transform({
                        transform(chunk, encoding, callback) {
                            const data = chunk.toString();
                            lineBuffer += data;

                            // Process complete lines
                            let newlineIndex;
                            while ((newlineIndex = lineBuffer.indexOf('\n')) !== -1) {
                                const line = lineBuffer.slice(0, newlineIndex);
                                lineBuffer = lineBuffer.slice(newlineIndex + 1);

                                try {
                                    const jsonData = JSON.parse(line);
                                    if (jsonData.data?.metafieldsSet?.userErrors?.length > 0) {
                                        hasErrors = true;
                                    }
                                } catch (e) {
                                    // Invalid JSON line - ignore
                                }
                            }

                            // Push the chunk through
                            this.push(chunk);
                            callback();
                        }
                    });

                    // Determine target bucket based on presence of errors
                    const targetBucket = hasErrors ?
                        process.env.ERROR_BUCKET :
                        process.env.RAW_BUCKET;

                    const objectKey = `organization=${shopInfo.orgId}/bulk-operation/${message.bulkOperationId}.jsonl`;

                    // Use multipart upload for large files
                    const upload = new Upload({
                        client: s3Client,
                        params: {
                            Bucket: targetBucket,
                            Key: objectKey,
                            Body: Readable.from(bulkDataResponse.body.pipe(errorCheckStream))
                        }
                    });

                    upload.on('httpUploadProgress', (progress: any) => {
                        console.log(`Upload progress: ${progress.loaded}/${progress.total}`);
                    });

                    await upload.done();
                    s3Location = `s3://${targetBucket}/${objectKey}`;
                    console.log(`Bulk operation data stored in ${s3Location}`);
                } else {
                    console.log('No URL available for bulk operation, skipping download');
                }

                // Look up task token from DynamoDB
                const bulkOpRecord = await ddbDocClient.send(new GetCommand({
                    TableName: process.env.BULK_OPERATIONS_TABLE!,
                    Key: {
                        bulkOperationId: message.adminGraphqlApiId,  // Use extracted ID instead of full API ID
                        organizationId: String(shopInfo.orgId)  // Ensure organizationId is a string
                    }
                }));

                if (bulkOpRecord.Item?.taskToken) {
                    // Notify Step Function of success (with potentially null s3Location)
                    await sfnClient.send(new SendTaskSuccessCommand({
                        taskToken: bulkOpRecord.Item.taskToken,
                        output: JSON.stringify({
                            status: 'COMPLETED',
                            s3Location,
                            bulkOperationId: message.adminGraphqlApiId,  // Use extracted ID
                            organizationId: String(shopInfo.orgId),  // Ensure organizationId is a string
                            errorMessage: '',
                        })
                    }));
                    console.log('Successfully notified Step Function of completion');
                }

            } catch (error: any) {
                console.error('Error processing bulk operation:', error);
                // Store error information in error bucket
                const errorKey = `organization=${shopInfo.orgId}/bulk-operation/errors/${message.bulkOperationId}_error.json`;
                await s3Client.send(new PutObjectCommand({
                    Bucket: process.env.ERROR_BUCKET,
                    Key: errorKey,
                    Body: JSON.stringify({
                        error: error.message,
                        message: message,
                        timestamp: new Date().toISOString()
                    })
                }));
            }
        }

        return {
            statusCode: 200,
            body: "Complete"
        };
    }
}

export const handler = new Handler();
export const main = handler.main.bind(handler);
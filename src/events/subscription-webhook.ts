import { APIGatewayProxyResultV2, SQSEvent } from "aws-lambda";
import fetch from "node-fetch";
import { updateDatabase, queryDatabase } from "../utils/raleon-helper";
import { getOrgsWithEmailEventConfigured, getRaleonApiKey } from "../utils/raleon-helper";
const WEBAPP_API_URL = process.env.WEBAPP_API_URL || 'https://app.raleon.io/api/v1';

export class Handler {
    async main(event: SQSEvent): Promise<APIGatewayProxyResultV2> {
        const messages = event.Records.map((record: any) => {
            try {
                const message = JSON.parse(record.body);
                console.log(`webhook event ${JSON.stringify(message)}`);
                const payload = message.detail.payload;
                return {
                    ...payload,
                    topic: message.detail.metadata["X-Shopify-Topic"],
                    shopDomain: message.detail.metadata["X-Shopify-Shop-Domain"],
                };
            } catch (error) {
                console.error(`Error parsing record: ${record.body}`, error);
                return null; 
            }
        }).filter(m => m !== null);

    for (let message of messages) {
        console.log(`message ${JSON.stringify(message)}`);
        const customerQuery = `
            SELECT id 
            FROM organization 
            WHERE externaldomain = $1
        `;
        const customerValues = [message.shopDomain];

        const customerResult = await queryDatabase(customerQuery, customerValues);
        if (customerResult.length === 0) {
            console.error(`Organization not found for ${message.shopDomain}`);
            return {
                body: "Organization not found",
                statusCode: 404,
            };
        }   
    
        const query =  message.app_subscription.status === 'ACTIVE' || message.app_subscription.status === 'PENDING' 
            ? `UPDATE organizationplan SET "status" = $1, confirmationurl = null WHERE orgid = $2 and subscriptionid = $3`
            : `UPDATE organizationplan SET "status" = $1, confirmationurl = null, priceoverride = null WHERE orgid = $2 and subscriptionid = $3`
                
        const values = [message.app_subscription.status, customerResult[0].id, message.app_subscription.admin_graphql_api_id];
        
        try {
            await updateDatabase(query, values);
            console.log(`Subscription updated to ${message.app_subscription.status} for ${message.shopDomain} organization ${customerResult[0].id}`);
        } catch (err) {
            console.error("Error updating Subscription", err);
            throw err;
        }

        if (message.app_subscription.status === 'ACTIVE') {
            const query2 = `UPDATE organizationplan SET "status" = 'CANCELLED', priceoverride = null WHERE orgid = $1 AND subscriptionid IS DISTINCT FROM $2`;
            const values2 = [customerResult[0].id, message.app_subscription.admin_graphql_api_id];
            
            try {
                await updateDatabase(query2, values2);
                console.log(`Old Subscriptions updated to CANCELLED for ${message.shopDomain} organization ${customerResult[0].id}`);
            } catch (err) {
                console.error("Error updating existing Subscriptions", err);
                throw err;
            }
        }

        const apiKey = await getRaleonApiKey();
        // await fetch(`${WEBAPP_API_URL}/feature-cache?orgId=${customerResult[0].id}`,{
        //     method: 'DELETE',
        //     headers: {
        //         'Content-Type': 'application/json',
        //         'Authorization': `ApiKey ${apiKey}`
        //     },
        // });
        await fetch(`${WEBAPP_API_URL}/update-feature-state?orgId=${customerResult[0].id}`,{
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `ApiKey ${apiKey}`
            },
        });
            /* 
            {
                "app_subscription": {
                    "admin_graphql_api_id": "gid:\/\/shopify\/AppSubscription\/1029266954",
                    "name": "Webhook Test",
                    "status": "PENDING",
                    "admin_graphql_api_shop_id": "gid:\/\/shopify\/Shop\/548380009",
                    "created_at": "2021-12-31T19:00:00-05:00",
                    "updated_at": "2021-12-31T19:00:00-05:00",
                    "currency": "USD",
                    "capped_amount": "20.0"
                }
                }
            */

    }

        return {
            body: "Complete",
            statusCode: 200,
        };
    }
}

export const handler = new Handler();
export const main = handler.main.bind(handler);

import { APIGatewayProxyResultV2, SQSEvent } from "aws-lambda";
import fetch from "node-fetch";
import { SHOPIFY_API_VERSION, getAPIaccessToken, getAuthToken, getShopInfoByShopDomain } from "../utils/shopify-helper";
import { ShopifyEventFactory } from "./event-handlers/shopify-event.factory";

const Memcached = require('memcached');

const MEMCACHED_ENDPOINT = process.env.MEMCACHED_ENDPOINT;

let memcachedClient = new Memcached(MEMCACHED_ENDPOINT!);

export const WEBAPP_API_URL = process.env.WEBAPP_API_URL || 'https://app.raleon.io/api/v1';
// export const WEBAPP_API_URL = 'https://mikelocal.raleon.io/webapp/api/v1';
// export const WEBAPP_API_URL = 'https://raleon.shib.net/api/v1';
// export const WEBAPP_API_URL = 'https://6237-173-93-104-68.ngrok-free.app/api/v1';

export class Handler {
	async main(event: SQSEvent): Promise<APIGatewayProxyResultV2> {

		const messages = event.Records.map((record: any) => {
			const message = JSON.parse(record.body);
			console.log(`webhook event ${JSON.stringify(message)}`);
			const payload = message.detail.payload;
			
			return {
				...payload,
				topic: message.detail.metadata["X-Shopify-Topic"],
				shopDomain: message.detail.metadata["X-Shopify-Shop-Domain"],
				webhookId: message.detail.metadata["X-Shopify-Webhook-Id"],
			};
		});
		
		for(let message of messages) {
			// Read from Cache
			const isAlreadyProcessed = await new Promise((resolve, reject) => {
				memcachedClient.get(message.webhookId, (err: any, data: any) => {
					if (err) reject(err);
					else resolve(data);
				});
			});

			if (isAlreadyProcessed) {
				console.warn(`Event already processed: ${message.topic}`);
				continue;
			}

			console.log('Event not already processed, processing: ', message.topic);
			const twelveHours = 86400 / 2;
			memcachedClient.set(message.webhookId, 'true', twelveHours, (err: any) => {
				if (err) {
					console.error(`Error setting memcached: ${err}`);
				}
			});

			console.log(`webhook event ${JSON.stringify(message)}`);
			console.log(`matching topic to handler: ${message.topic}`);

			const eventHandler = ShopifyEventFactory.constructEventHandler(message.topic);
			if (eventHandler) {
				console.log(`handling ${message.topic}`);
				await eventHandler.handleEvent(message);
			} else {
				console.error(`No handler for topic ${message.topic}`);
				throw new Error(`No handler for topic ${message.topic}`);
			}
			
		}
		
		return {
			body: "Complete",
			statusCode: 200,
		};
	}
}

export const handler = new Handler();
export const main = handler.main.bind(handler);

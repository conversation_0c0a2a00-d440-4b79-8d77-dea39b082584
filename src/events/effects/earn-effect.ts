export abstract class EarnEffect {
    constructor() {}
    abstract processEffect(sessionToken: string,
        raleonUserId: string,
        customerId: string,
        orgId: string,
        effect: any,
        payload?: any,
        earn?: any,
        pointsMultiplier?: number,
        orderId?: any,
        itemId?: any
    ): Promise<void>;


	protected async fetch(
		authToken: string,
		url: string,
		method: string,
		body?: string
	): Promise<any> {
		let response;
		const options: any = {
			method,
			headers: {
				"Content-Type": "application/json",
				Authorization: `Bearer ${authToken}`,
				'ngrok-skip-browser-warning': true,
			},
		};
		if (body) options.body = body;


		console.log('fetching', url, options)

		try {
			response = await fetch(url, options);
			if (!response.ok || response.status >= 300 || response.status < 200) {
				console.error(`HTTP error! Status: ${response.status}`);
				throw new Error(`HTTP error! Status: ${response.status}`);
			}

            console.log('fetch response', response)

			if (response.headers.get('content-type')?.includes('application/json')) {
				const result = await response.json();
                console.log('JSON result', result)
                return result;
			} else {
				console.warn("Response is not JSON");
				return null;
			}
		} catch (e) {
			console.error("error 👉", e);
            throw e;
		}
	}
}

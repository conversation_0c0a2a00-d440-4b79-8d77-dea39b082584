import { WEBAPP_API_URL } from "../webhook-worker";
import { EarnEffect } from "./earn-effect";
import fetch from "node-fetch";

export class ShopifyPointEffect extends EarnEffect {
	private value: number;

	async processEffect(
		sessionToken: string,
		raleonUserId: string,
		customerId: string,
		orgId: string,
		effect: any,
		payload?: any,
		earn?: any,
		pointsMultiplier: number = 1,
		orderId?: any,
		itemId?: any
	): Promise<void> {
		await this.grantPoints(
			sessionToken,
			raleonUserId,
			customerId,
			orgId,
			effect,
			payload,
			earn,
			pointsMultiplier,
			orderId,
			itemId
		);
		await this.grantloyaltyReward(
			sessionToken,
			raleonUserId,
			effect,
			orgId,
			customerId,
			earn,
			payload,
			orderId,
			itemId
		);

		let response = await this.fetch(
			sessionToken,
			`${WEBAPP_API_URL}/loyalty-earn-log`,
			"POST",
			JSON.stringify({
				earnId: earn?.id,
				raleonUserId,
			})
		);
		console.log(`Loyalty Log response 👉`, response);

	}

	private async grantloyaltyReward(
		sessionToken: string,
		raleonUserId: string,
		effect: any,
		orgId: string,
		customerId: string,
		earn?: any,
		payload?: any,
		orderId?: any,
		itemId?: any
	) {
		console.log(`giving loyalty reward1`);
		console.log(
			`effect.loyaltyRewardDefinitionId 👉`,
			effect.loyaltyRewardDefinitionId
		);
		console.log(`orgCust \u{1F449}`, orgId + "-" + customerId);
		
		if (effect.loyaltyRewardDefinitionId > 0) {
			console.log(`giving loyalty reward`);

			let response = await this.fetch(
				sessionToken,
				`${WEBAPP_API_URL}/loyalty-grant`,
				"POST",
				JSON.stringify({
					rewardDefinitionId: effect.loyaltyRewardDefinitionId,
					raleonUserId,
					orderId,
					itemId,
					earnEffectId: effect.id,
				})
			);
			console.log(`response 👉`, response);
		}
	}

	private async grantPoints(
		sessionToken: string,
		raleonUserId: string,
		customerId: string,
		orgId: string,
		effect: any,
		payload?: any,
		earn?: any,
		pointsMultiplier: number = 1,
		orderId?: any,
		itemId?: any
	) {
		let balanceChange = 0;
		if (effect.points > 0) {
			balanceChange = effect.points;
		}

		console.log(`checkoutValue 👉`, payload["total_price"]);
		console.log(`points multiplier 👉 ${pointsMultiplier}X ${pointsMultiplier == 1 ? '(NONE)' : ''} `);

		if (effect.pointsPerDollar > 0) {
			console.log("test1");
			let totalPrice = parseFloat(payload["total_price"]); 
			console.log(totalPrice);
			let subtotal = parseFloat(payload["current_subtotal_price"]); 
			console.log(subtotal);
			let shipping = parseFloat(payload["total_shipping_price_set"]["shop_money"]["amount"]); 
			console.log(shipping);
			let taxes = parseFloat(payload["total_tax"]); 
			console.log(taxes);
			if (effect.includeTaxes && effect.includeShipping) {
				console.log("includes shipping and taxes");
				balanceChange = Math.floor(effect.pointsPerDollar * totalPrice * (pointsMultiplier || 1));
			} else if (effect.includeTaxes && !effect.includeShipping) {
				console.log("includes taxes");
				balanceChange = Math.floor(effect.pointsPerDollar * (subtotal + taxes) * (pointsMultiplier || 1));
			} else if (!effect.includeTaxes && effect.includeShipping) {
				console.log("includes shipping");
				balanceChange = Math.floor(effect.pointsPerDollar * (subtotal + shipping) * (pointsMultiplier || 1));
			} else {
				console.log("no shipping or taxes");
				balanceChange = Math.floor(effect.pointsPerDollar * subtotal * (pointsMultiplier || 1));
			}
		}


		console.log(`orgCust 👉`, orgId + "-" + customerId);
		console.log(`giving ${balanceChange} points`);
		if (isNaN(balanceChange) || balanceChange <= 0) return;
		let earnMessage = "";
		if(earn && earn.name && earn.name.trim() !== "") {
			earnMessage = ` for ${earn.name}`;
		}
		return await this.fetch(
			sessionToken,
			`${WEBAPP_API_URL}/loyalty-currencies/${effect.loyaltyCurrencyId}/balance-change`,
			"POST",
			JSON.stringify({
				info: `Granting ${balanceChange} points${earnMessage}`,
				balanceChange: balanceChange,
				raleonUserId,
				orderId,
				itemId,
				earnEffectId: effect.id,
			})
		);
	}
}

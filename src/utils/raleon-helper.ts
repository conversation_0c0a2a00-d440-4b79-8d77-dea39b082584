import { Client } from "pg";
import {
	GetSecretValueCommand,
	SecretsManagerClient,
} from "@aws-sdk/client-secrets-manager";
import * as crypto from "crypto";

interface DatabaseSecret {
	username: string;
	password: string;
	host: string;
	dbname: string;
	port: string;
}

const secretsManagerClient = new SecretsManagerClient({ region: "us-east-1" });

export async function getDatabaseInfo(): Promise<DatabaseSecret> {
	const command = new GetSecretValueCommand({ SecretId: process.env.RALEON_DB! });

	try {
		const data = await secretsManagerClient.send(command);

		if (data.SecretString) {
			const secret = JSON.parse(data.SecretString) as DatabaseSecret;
			return secret;
		} else {
			throw new Error("Secret string is undefined");
		}
	} catch (error) {
		console.error(error);
		throw new Error("Error retrieving secret");
	}
}

export async function queryDatabase(query: string, values: any[]): Promise<any[]> {
	const databaseInfo = await getDatabaseInfo();

	const client = new Client({
		user: databaseInfo.username,
		host: databaseInfo.host,
		database: databaseInfo.dbname,
		password: databaseInfo.password,
		port: parseInt(databaseInfo.port, 10),
	});

	try {
		await client.connect();

		const res = await client.query(query, values);
		return res.rows;
	} catch (err) {
		console.error(err);
		throw err;
	} finally {
		await client.end();
	}
}

export async function updateDatabase(query: string, values: any[]): Promise<void> {
	const databaseInfo = await getDatabaseInfo();

	const client = new Client({
		user: databaseInfo.username,
		host: databaseInfo.host,
		database: databaseInfo.dbname,
		password: databaseInfo.password,
		port: parseInt(databaseInfo.port, 10),
	});

	try {
		await client.connect();

		await client.query(query, values);
	} catch (err) {
		console.error(err);
		throw err;
	} finally {
		await client.end();
	}
}

export async function getRaleonApiKey(): Promise<any> {
	const query = `SELECT "key", "createdat", "scopes" FROM "public"."apikey" WHERE "name" = $1`;
	const values = ['Raleon Service API'];
	const results = await queryDatabase(query, values);
	if (results[0]) {
		return results[0].key;
	}
	throw new Error("Raleon API Key not found");
}

export async function getKlaviyoIntegrationByOrg(orgId: number): Promise<any> {
	const query = `SELECT "value", "secretkeyid" FROM "public"."organizationkeys" WHERE "organizationid" = $1 AND key = $2`
	const values = [orgId, 'Klaviyo-API-Key'];
	const results = await queryDatabase(query, values);
	return results[0];
}

export async function getSendlaneIntegrationByOrg(orgId: number): Promise<any> {
	const query = `SELECT "value", "secretkeyid" FROM "public"."organizationkeys" WHERE "organizationid" = $1 AND key = $2`
	const values = [orgId, 'Sendlane-API-Key'];
	const results = await queryDatabase(query, values);
	return results[0];
}

export async function getSendlaneCustomIntegrationByOrg(orgId: number): Promise<any> {
	const query = `SELECT "value", "secretkeyid" FROM "public"."organizationkeys" WHERE "organizationid" = $1 AND key = $2`
	const values = [orgId, 'Sendlane-Custom-Integration-Token'];
	const results = await queryDatabase(query, values);
	return results[0];
}

export async function getEnabledIntegrationsByOrg(orgId: number): Promise<any> {
	const query = `SELECT "integrationid" FROM "public"."organizationintegrationdetails" WHERE "orgid" = $1 AND enabled = true`
	const values = [orgId];
	const results = await queryDatabase(query, values);
	return results[0];
}

export async function getOrgIdByShopifyDomain(shopifyDomain: string): Promise<number> {
	const query = `SELECT "id" FROM "public"."organization" WHERE "externaldomain" = $1`
	const values = [shopifyDomain];
	const results = await queryDatabase(query, values);
	if (results.length === 0) {
		throw new Error(`Organization not found for domain: ${shopifyDomain}`);
	}
	return results[0].id;
}

export async function getLoyaltyEvents(): Promise<any> {
	const query = `SELECT "id", "name", "friendlyname", "datastructure" FROM "public"."loyaltyevent"`
	const results = await queryDatabase(query, []);
	if (results.length === 0) {
		throw new Error(`No loyalty events found`);
	}
	return results;
}

export async function getConfiguredRaleonEmails(orgId: number, eventName: string): Promise<any> {
	const query = `SELECT lee.*, e.name, e.datastructure FROM "public"."loyaltyeventemail" lee JOIN "public"."loyaltyevent" e ON lee.loyaltyeventid = e.id WHERE lee.orgid = $1 AND e.name = $2 AND active = true`
	const results = await queryDatabase(query, [orgId, eventName]);
	if (results.length === 0) {
		throw new Error(`No loyalty events found`);
	}
	return results;
}

export async function getOrgsWithEmailEventConfigured(eventName: string): Promise<any> {
	const query = `SELECT DISTINCT orgid, lee.*, e.name, e.datastructure FROM "public"."loyaltyeventemail" lee JOIN "public"."loyaltyevent" e ON lee.loyaltyeventid = e.id WHERE e.name = $1 AND active = true`
	console.log(`query: ${query}`);
	console.log(`eventName: ${eventName}`);
	const results = await queryDatabase(query, [eventName]);
	if (results.length === 0) {
		throw new Error(`No orgs have the loyalty event: ${eventName} configured`);
	}
	console.log(`row: ${JSON.stringify(results[0])}`);
	return results;
}

export async function getOrgsWithActivePlan(): Promise<any> {
	const query = `SELECT DISTINCT orgid from organizationplan join organization o on orgid = o.id where status='ACTIVE' and (o.externalPlanDetails::jsonb->>'partnerDevelopment')::boolean = false`
	console.log(`query: ${query}`);
	const results = await queryDatabase(query,[]);
	if (results.length === 0) {
		throw new Error(`No orgs have an active plan`);
	}
	console.log(`row: ${JSON.stringify(results[0])}`);
	return results;
}

export async function getAllConfiguredRaleonEmails(orgId: number): Promise<any> {
	const query = `SELECT lee.*, e.name, e.datastructure FROM "public"."loyaltyeventemail" lee JOIN "public"."loyaltyevent" e ON lee.loyaltyeventid = e.id WHERE lee.orgid = $1 AND active = true`
	const results = await queryDatabase(query, [orgId]);
	if (results.length === 0) {
		throw new Error(`No loyalty events found`);
	}
	return results;
}

export async function getRaleonUserIdFromCustomerId(customerId: number): Promise<any> {
	const query = `SELECT "raleonuserid", "orgid" FROM "public"."raleonuseridentity" WHERE "identityvalue" = $1 AND "identitytype" = 'customer_id'`
	const values = [customerId];
	const results = await queryDatabase(query, values);
	if (results.length === 0) {
		throw new Error(`No customer found with id: ${customerId}`);
	}
	return results[0];
}

export async function saveLastEmailSentTime(identities: any[]): Promise<void> {
	const query = `UPDATE "public"."raleonuseridentity" SET "lastemailsent" = current_timestamp WHERE "id" = ANY(ARRAY[${identities}])`;
	await updateDatabase(query, []);
}

export async function saveHistoricalDataComplete(orgId: number): Promise<void> {
	const query = `UPDATE "public"."organization" SET "historicaldatacomplete" = true WHERE "id" = ${orgId}`;
	await updateDatabase(query, []);
}

export async function getRaleonIdentity(customerId: number): Promise<any> {
	const query = `SELECT "id", "unsubscribed", "lastemailsent" FROM "public"."raleonuseridentity" WHERE "identityvalue" = $1 AND "identitytype" = 'customer_id'`
	const results = await queryDatabase(query, [customerId]);
	if (results.length === 0) {
		throw new Error(`No customer found with id: ${customerId}`);
	}
	return results[0];
}

export async function getInventoryCouponsWithIdentity(orgIds: number[]): Promise<any[]> {
	const query = `
        SELECT 
			ic.id AS inventorycouponid,
            ic.*, 
            rui.identityvalue, 
            rui.id AS rui_id,
			rui.orgid,
			rui.lastemailsent,
			rc.name as rewardname,
			rc.amount as rewardamount,
			rc.amounttype as rewardamounttype,
        FROM 
            "public"."inventorycoupon" ic
        JOIN 
            "public"."raleonuseridentity" rui 
        ON 
            ic.raleonuserid = rui.raleonuserid
		JOIN 
			"public"."rewardcoupon" rc
		ON
			ic.rewardcouponid = rc.id
        WHERE
			rui.orgid = ANY($1)
		AND (
			rui.unsubscribed = false
			OR
			rui.unsubscribed IS NULL
		)
		AND
			used = false
		AND
			(
   				(ic.expiration >= current_timestamp AND ic.expiration < current_timestamp + INTERVAL '1 day')
				OR
				ic.expiration::date = current_date + INTERVAL '30 days'
			)
    `;
	
	const results = await queryDatabase(query, [orgIds]);//, couponId]);
	if (results.length === 0) {
		throw new Error("No inventory coupons found with valid expiration.");
	}
	return results;
}

export function decrypt(text: string, key: string) {
	let textParts = text.split(':');
	if (textParts.length === 0) {
		throw new Error('Invalid encrypted text');
	}
	let iv = Buffer.from(textParts.shift()!, 'hex');
	let encryptedText = Buffer.from(textParts.join(':'), 'hex');
	let decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(key), iv);
	let decrypted = decipher.update(encryptedText);

	decrypted = Buffer.concat([decrypted, decipher.final()]);

	return decrypted.toString();
}

export async function checkActiveLoyalty(orgId: string): Promise<boolean> {
	const queryLoyalty = `
		SELECT 1
		FROM loyaltyprogram
		WHERE orgid = $1 AND active = TRUE AND activationdate IS NOT NULL
	`;
	const valuesLoyalty = [orgId];

	try {
		const resultLoyalty = await queryDatabase(queryLoyalty, valuesLoyalty);
		return resultLoyalty.length > 0;
	} catch (error) {
		console.error('Error querying loyalty program:', error);
		return false;
	}
}

export async function checkActiveLoyaltyOrGWP(orgId: string): Promise<boolean> {
	const queryLoyalty = `
		SELECT 1
		FROM loyaltyprogram
		WHERE orgid = $1 AND active = TRUE AND activationdate IS NOT NULL
	`;
	const valuesLoyalty = [orgId];

	const queryGWP = `
		SELECT 1
		FROM promotionalcampaign
		WHERE orgid = $1 AND active = TRUE AND startdate IS NOT NULL
	`;
	const valuesGWP = [orgId];

	try {
		const resultLoyalty = await queryDatabase(queryLoyalty, valuesLoyalty);
		const resultGWP = await queryDatabase(queryGWP, valuesGWP);
		return resultLoyalty.length > 0 || resultGWP.length > 0;
	} catch (error) {
		console.error('Error querying loyalty program:', error);
		return false;
	}
}
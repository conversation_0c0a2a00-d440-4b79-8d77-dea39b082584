import { BaseRouterTarget } from "./base-router-target.service";
import { CompletionMessage, RouterParams, StreamingCallback, ToolCall } from "./types";

interface OpenRouterResponse {
    id?: string;
    choices: Array<{
        message?: {
            content: string;
            tool_calls?: Array<{
                id: string;
                type: 'function';
                function: {
                    name: string;
                    arguments: string;
                };
            }>;
        };
        delta?: {
            content: string;
            tool_calls?: Array<{
                id: string;
                type: 'function';
                function: {
                    name: string;
                    arguments: string;
                };
            }>;
        };
    }>;
}

interface OpenRouterGenerationDetails {
	id: string;
	total_cost: number;
    created_at: string;
    model: string;
    origin: string;
    usage: number;
    is_byok: boolean;
    upstream_id: string;
    cache_discount: number;
    app_id: number;
    streamed: boolean;
    cancelled: boolean;
    provider_name: string;
    latency: number;
    moderation_latency: number;
    generation_time: number;
    finish_reason: string;
    native_finish_reason: string;
    tokens_prompt: number;
    tokens_completion: number;
    native_tokens_prompt: number;
    native_tokens_completion: number;
    native_tokens_reasoning: number;
    num_media_prompt: number;
    num_media_completion: number;
    num_search_results: number;
}

export class OpenRouterTarget extends BaseRouterTarget {
	id = 'openrouter';
	systemId = 'OpenRouter';
	// Support all major providers through OpenRouter
	supportedModels = ['*'];
	supportedProviders = ['*'];

	private readonly apiKey: string;
	private readonly baseURL = 'https://openrouter.ai/api/v1';

	constructor() {
		super();
		this.apiKey = '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'; //process.env.OPENROUTER_API_KEY || '';
		if (!this.apiKey) {
			console.warn('OpenRouter API key not found in environment variables');
		}
	}

	protected supportsStreaming(): boolean {
		return true;
	}

	private async fetchGenerationDetails(id: string, retries = 3, delay = 1000): Promise<{
		provider: string;
		model: string;
		totalCost?: number;
		promptTokens?: number;
		completionTokens?: number;
		totalTokens?: number;
		tokenRatePerSec?: number;
		firstTokenMs?: number;
		latencyMs?: number;
		generationTimeMs?: number;
		finishReason?: string;
		additional?: Record<string, unknown>;
	}> {
		// Initial delay to allow the generation details to be available
		await new Promise(resolve => setTimeout(resolve, delay));

		for (let attempt = 0; attempt < retries; attempt++) {
			try {
				const response = await fetch(`${this.baseURL}/generation?id=${id}`, {
					headers: {
						'Authorization': `Bearer ${this.apiKey}`,
						'HTTP-Referer': 'https://raleon.ai',
						'X-Title': 'Raleon AI'
					}
				});

				if (response.status === 404) {
					if (attempt < retries - 1) {
						// Wait before retrying
						await new Promise(resolve => setTimeout(resolve, delay));
						continue;
					}
					return {
						provider: 'unknown',
						model: 'unknown',
						additional: { error: 'Generation details not yet available' }
					};
				}

				if (!response.ok) {
					throw new Error('Failed to fetch generation details' + response.statusText + ' ' + response.status);
				}

				const data = (await response.json() as any).data as OpenRouterGenerationDetails;
				return {
					provider: data.provider_name || 'unknown',
					model: data.model || 'unknown',
					totalCost: data.total_cost,
					promptTokens: data.tokens_prompt,
					completionTokens: data.tokens_completion,
					totalTokens: data.tokens_prompt + data.tokens_completion,
					tokenRatePerSec: undefined, // OpenRouter doesn't provide this directly
					firstTokenMs: undefined, // OpenRouter doesn't provide this directly
					latencyMs: data.latency,
					generationTimeMs: data.generation_time,
					finishReason: data.finish_reason,
					additional: {
						origin: data.origin,
						isByok: data.is_byok,
						upstreamId: data.upstream_id,
						cacheDiscount: data.cache_discount,
						appId: data.app_id,
						streamed: data.streamed,
						cancelled: data.cancelled,
						moderationLatency: data.moderation_latency,
						nativeFinishReason: data.native_finish_reason,
						nativeTokensPrompt: data.native_tokens_prompt,
						nativeTokensCompletion: data.native_tokens_completion,
						nativeTokensReasoning: data.native_tokens_reasoning,
						numMediaPrompt: data.num_media_prompt,
						numMediaCompletion: data.num_media_completion,
						numSearchResults: data.num_search_results
					}
				};
			} catch (error) {
				if (attempt === retries - 1) {
					console.error('Failed to fetch generation details after retries:', error);
					return {
						provider: 'unknown',
						model: 'unknown',
						additional: { error: error instanceof Error ? error.message : 'Unknown error' }
					};
				}
				await new Promise(resolve => setTimeout(resolve, delay));
			}
		}

		return {
			provider: 'unknown',
			model: 'unknown',
			additional: { error: 'Failed to fetch after retries' }
		};
	}

	protected async executeCompletion(
		messages: CompletionMessage[],
		params: RouterParams,
		onChunk?: StreamingCallback
	): Promise<{
		content: string;
		provider: string;
		model: string;
		totalCost?: number;
		promptTokens?: number;
		completionTokens?: number;
		totalTokens?: number;
		tokenRatePerSec?: number;
		firstTokenMs?: number;
		latencyMs?: number;
		generationTimeMs?: number;
		finishReason?: string;
		additional?: Record<string, unknown>;
	}> {
		return this.makeRequest(messages, params, params.stream || false, onChunk);
	}

	protected async executeStreamingCompletion(
		messages: CompletionMessage[],
		params: RouterParams,
		onChunk: StreamingCallback
	): Promise<{content: string; provider: string; model: string}> {
		return this.makeRequest(messages, params, true, onChunk);
	}

	private async makeRequest(
		messages: CompletionMessage[],
		params: RouterParams,
		stream: boolean,
		onChunk?: StreamingCallback
	): Promise<{
		content: string;
		provider: string;
		model: string;
		totalCost?: number;
		promptTokens?: number;
		completionTokens?: number;
		totalTokens?: number;
		tokenRatePerSec?: number;
		firstTokenMs?: number;
		latencyMs?: number;
		generationTimeMs?: number;
		finishReason?: string;
		additional?: Record<string, unknown>;
		toolCalls?: ToolCall[];
	}> {
		if (!this.apiKey) {
			throw new Error('OpenRouter API key not configured');
		}

		// Convert tool definitions to OpenRouter format if provided
		let tools;
		if (params.tools?.length) {
			tools = params.tools.map((tool: any) => ({
				type: tool.type,
				function: {
					name: tool.function.name,
					description: tool.function.description,
					parameters: tool.function.parameters
				}
			}));
		}

		const requestBody: any = {
			messages: messages.map(msg => ({
				role: msg.role,
				content: msg.content
			})),
			stream,
			tools
		};

		// Add web search options if provided
		if (params.web_search_options) {
			requestBody.web_search_options = params.web_search_options;
		}

		if (params.models?.[0]) {
			requestBody.models = params.models;
		} else {
			requestBody.models = ['openrouter/auto'];
		}

		if (params.providers?.[0] && params.providers[0] !== '*') {
			requestBody.provider = {
				order: params.providers
			};
		}

		const fetchResponse = await fetch(`${this.baseURL}/chat/completions`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'Authorization': `Bearer ${this.apiKey}`,
				'HTTP-Referer': 'https://raleon.ai',
				'X-Title': 'Raleon AI'
			},
			body: JSON.stringify(requestBody)
		});

		if (!fetchResponse.ok) {
			const error = await fetchResponse.text();
			throw new Error(`OpenRouter API error: ${error}`);
		}

		if (!stream) {
			console.log('Non-streaming response:');
			const data = await fetchResponse.json() as OpenRouterResponse & { id?: string };
			if (!data.choices?.[0]?.message) {
				throw new Error('Invalid response from OpenRouter API');
			}

			const message = data.choices[0].message;
			const content = message.content || '';
			const toolCalls = message.tool_calls?.map(tc => ({
				type: tc.type as 'function',
				id: tc.id,
				function: {
					name: tc.function.name,
					arguments: tc.function.arguments
				}
			}));

			const metadata = {
				provider: 'openrouter',
				model: 'pending'
			};

			// Start metadata fetch in background if we have an ID
			if (data.id) {
				this.fetchGenerationDetails(data.id)
					.then(details => {
						if (onChunk) {
							onChunk({
								content: '',
								done: true,
								metadata: {
									system: this.systemId,
									...details
								},
								toolCalls
							}).catch(console.error);
						}
					})
					.catch(console.error);
			}

			return {
				content,
				...metadata,
				toolCalls
			};
		}

		// Handle streaming response
		const reader = fetchResponse.body?.getReader();
		if (!reader) {
			throw new Error('Failed to get stream reader');
		}

		let fullResponse = '';
		let generationId: string | undefined;
		let currentToolCalls: ToolCall[] = [];
		const decoder = new TextDecoder();

		try {
			while (true) {
				const {done, value} = await reader.read();
				if (done) break;

				const chunk = decoder.decode(value);
				const lines = chunk.split('\n').filter(line => line.trim());

				for (const line of lines) {
					if (line.startsWith('data: ')) {
						const data = line.slice(6);
						if (data === '[DONE]') continue;

						try {
							const parsed = JSON.parse(data);
							if (!generationId && parsed.id) {
								generationId = parsed.id;
							}

							const content = parsed.choices[0]?.delta?.content || '';
							const toolCalls = parsed.choices[0]?.delta?.tool_calls?.map((tc: any) => ({
								type: tc.type as 'function',
								id: tc.id,
								function: {
									name: tc.function.name,
									arguments: tc.function.arguments
								}
							}));

							if (toolCalls) {
								currentToolCalls = [...currentToolCalls, ...toolCalls];
							}

							if ((content || toolCalls) && onChunk) {
								await onChunk({
									content,
									done: false,
									toolCalls
								});
							}
							fullResponse += content;
						} catch (e) {
							console.error('Error parsing streaming response:', e);
						}
					}
				}
			}
		} finally {
			reader.releaseLock();
		}

		const result = {
			content: fullResponse,
			provider: 'openrouter',
			model: 'pending',
			toolCalls: currentToolCalls
		};

		// Start metadata fetch in background if we have an ID
		if (generationId && onChunk) {
			this.fetchGenerationDetails(generationId)
				.then(details => {
					onChunk({
						content: '',
						done: true,
						metadata: {
							system: this.systemId,
							...details
						},
						toolCalls: currentToolCalls
					}).catch(console.error);
				})
				.catch(console.error);
		}

		return result;
	}

	protected async executeToolFunction(name: string, args: any): Promise<any> {
		// OpenRouter doesn't execute tools directly - it just returns tool calls
		// Tool execution is handled by BaseRouterTarget, which gets the tools from params
		throw new Error('OpenRouter does not execute tools directly');
	}
}

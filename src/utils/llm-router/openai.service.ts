import OpenAI from 'openai';
const openai = new OpenAI({apiKey: '***************************************************'});

export class OpenAiService {
	async getO3Completion(prompt: string) : Promise<string> {
		const completion = await openai.chat.completions.create({
			model: 'o3-mini',
			max_completion_tokens: 16384,
			messages: [
				{
					role: 'system',
					content: prompt
				}
			],
			response_format: {type: "json_object"}
		} as any) as any;
		// Log the completion for debugging purposes
		console.log(completion.choices[0].message.content);

		// Return the raw content as a string
		if (completion.choices[0].message.content) {
			return completion.choices[0].message.content;
		} else {
			throw new Error('No content received from OpenAI');
		}
	}

	async getImageDescription(prompt: string, imageUrl: string) : Promise<string> {
		const completion = await openai.chat.completions.create({
			model: 'gpt-4o-mini',
			max_tokens: 1000,
			messages: [
				{
					role: 'user',
					content: [{
						"type": "text", "text": prompt},
						{
							"type": "image_url",
							"image_url": {
								"url": imageUrl
							}
						}
					]
				}
			],
			response_format: {type: "json_object"}
		} as any) as any;
		// Log the completion for debugging purposes
		console.log(completion.choices[0].message.content);

		// Return the raw content as a string
		if (completion.choices[0].message.content) {
			return completion.choices[0].message.content;
		} else {
			throw new Error('No content received from OpenAI');
		}
	}

	async getCompletion(prompt: string, max_tokens?: number, model?: string, json = true, tools?: Array<any>): Promise<string> {
		try {
			const request: any = {
				model: model || 'gpt-4o',
				max_tokens: max_tokens || 4096,
				messages: [
					{
						role: 'system',
						content: prompt
					}
				]
			};
			if (tools) {
				request.tools = tools;
			}
			if (json) {
				request.response_format = {type: "json_object"};
			}
			const completion = await openai.chat.completions.create(request);
			// Log the completion for debugging purposes
			console.log(completion.choices[0].message.content);

			// Return the raw content as a string
			if (completion.choices[0].message.content) {
				return completion.choices[0].message.content;
			} else {
				throw new Error('No content received from OpenAI');
			}
		} catch (error) {
			console.error('OpenAI API Error:', error);
			throw new Error('Failed to get response from OpenAI');
		}
	}
}

import { BaseRouterTarget } from "./base-router-target.service";
import { OpenAiService } from "./openai.service";
import { CompletionMessage, RouterParams, ToolCall } from "./types";

export class OpenAiTarget extends BaseRouterTarget {
	id = 'openai';
	systemId = 'OpenAI';
	supportedModels = ['openai/*'];
	supportedProviders = ['OpenAI'];

    constructor(
        private readonly openAiService = new OpenAiService()
    ) {
        super();
    }

	protected supportsStreaming(): boolean {
		return false;
	}

	protected async executeCompletion(
		messages: CompletionMessage[],
		params: RouterParams
	): Promise<{content: string; provider: string; model: string; additional?: Record<string, unknown>; toolCalls?: ToolCall[]}> {
		return this.makeRequest(messages, params);
	}

	private getSelectedModel(params: RouterParams): string {
		const defaultModel = 'gpt-4';
		
		if (!params.models?.length) {
			return defaultModel;
		}

		// If * is provided, use default
		if (params.models.includes('*')) {
			return defaultModel;
		}

		// Filter to OpenAI models only
		const openaiModels = params.models.filter(m => 
			m.startsWith('openai/') || 
			this.supportedModels.some(pattern => this.matchesWildcard(pattern, m))
		);

		if (!openaiModels.length) {
			return defaultModel;
		}

		// If openai/* is provided, use default
		if (openaiModels.includes('openai/*')) {
			return defaultModel;
		}

		// Use first matching model, removing openai/ prefix
		return openaiModels[0].replace('openai/', '');
	}

	private async makeRequest(
		messages: CompletionMessage[],
		params: RouterParams
	): Promise<{content: string; provider: string; model: string; additional?: Record<string, unknown>; toolCalls?: ToolCall[]}> {
		// Get the last message which contains the actual prompt
		const lastMessage = messages[messages.length - 1];

		// Get the appropriate model
		const model = this.getSelectedModel(params);

		// Convert tool definitions to OpenAI format if provided
		const tools = params.tools?.map(tool => ({
			type: tool.type,
			function: {
				name: tool.function.name,
				description: tool.function.description,
				parameters: tool.function.parameters
			}
		}));

		// Call OpenAI service with the message content
		const response = await this.openAiService.getCompletion(
			lastMessage.content,
			params.maxTokens,
			model,
			false, // Don't force JSON response format
			tools
		);

		// For OpenAI, we can provide token counts and temperature from the response
		const additional: Record<string, unknown> = {};
		const toolCalls: ToolCall[] = [];

		if (typeof response === 'object' && response !== null) {
			const {content, usage, model_version, tool_calls} = response as any;
			if (usage) {
				additional.promptTokens = usage.prompt_tokens;
				additional.completionTokens = usage.completion_tokens;
				additional.totalTokens = usage.total_tokens;
			}
			additional.modelVersion = model_version;

			// Handle tool calls if present
			if (tool_calls?.length) {
				toolCalls.push(...tool_calls.map((tc: any) => ({
					type: tc.type as 'function',
					id: tc.id,
					function: {
						name: tc.function.name,
						arguments: tc.function.arguments
					}
				})));
			}

			return {
				content: content || response,
				provider: 'OpenAI',
				model: model,
				additional,
				toolCalls: toolCalls.length > 0 ? toolCalls : undefined
			};
		}

		return {
			content: response as string,
			provider: 'OpenAI',
			model: model,
			additional
		};
	}

	protected async executeToolFunction(name: string, args: any): Promise<any> {
		// Tool execution is handled by BaseRouterTarget using the tools from params
		throw new Error('OpenAI does not execute tools directly');
	}
}

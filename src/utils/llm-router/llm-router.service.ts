import { AnthropicTarget } from "./anthropic-target.service";
import { OpenRouterTarget } from "./open-router-target.service";
import { OpenAiTarget } from "./openai-target.service";
import { RouterTarget, RouterParams, CompletionMessage, StreamingCallback, RouterResponse } from "./types";

export class LLMRouterService {
  private targets: RouterTarget[] = [];

  constructor(
    private openRouterTarget = new OpenRouterTarget(),
    private anthropicTarget = new AnthropicTarget(),
    private openAiTarget = new OpenAiTarget()
  ) {
    
    this.initializeTargets();
  }

  private initializeTargets() {
    this.targets = [
      this.openRouterTarget,
      this.anthropicTarget,
      this.openAiTarget,
    ];

    // No sorting needed - order is based on providers array
  }

  private getTargetsInSystemOrder(params: RouterParams): RouterTarget[] {
    if (!params.systems?.length) {
      return this.targets;
    }

    // Create a map of system to target for quick lookup
    const systemTargetMap = new Map<string, RouterTarget>();
    this.targets.forEach(target => {
      systemTargetMap.set(target.systemId, target);
    });

    // Order targets based on systems array
    const orderedTargets: RouterTarget[] = [];
    const remainingTargets = new Set(this.targets);

    // First add targets in the order specified by systems
    params.systems.forEach(system => {
      const target = systemTargetMap.get(system);
      if (target && remainingTargets.has(target)) {
        orderedTargets.push(target);
        remainingTargets.delete(target);
      }
    });

    // Then add any remaining targets that weren't in the systems array
    remainingTargets.forEach(target => {
      orderedTargets.push(target);
    });

    return orderedTargets;
  }

  async executeCompletion(
    messages: CompletionMessage[],
    params: RouterParams,
    onChunk?: StreamingCallback
  ): Promise<RouterResponse> {
    const orderedTargets = this.getTargetsInSystemOrder(params);

    for (const target of orderedTargets) {
      if (target.canHandle(params)) {
        const response = await target.execute(messages, params, onChunk);
        return response;
      }
    }

    throw new Error('No suitable target found for the given parameters');
  }
}

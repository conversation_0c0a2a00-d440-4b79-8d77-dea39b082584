import { RouterTarget, RouterParams, ToolCall, ToolResult, CompletionMessage, StreamingCallback, RouterResponse } from "./types";


export abstract class BaseRouterTarget implements RouterTarget {
  abstract id: string;
  abstract systemId: string;
  abstract supportedModels: string[];
  abstract supportedProviders: string[];

  protected matchesWildcard(pattern: string, value: string): boolean {
    if (pattern === '*') return true;
    if (pattern.endsWith('/*')) {
      const prefix = pattern.slice(0, -2); // Remove /* from the end
      return value.startsWith(prefix + '/');
    }
    return pattern === value;
  }

  canHandle(params: RouterParams): boolean {
    // Check system match if systems are specified
    if (params.systems?.length) {
      const systemMatch = params.systems.includes('*') || params.systems.includes(this.systemId);
      if (!systemMatch) return false;
    }

    // Check model match if models are specified
    if (params.models?.length) {
      const modelMatch = params.models.some((requestedModel: any) =>
        this.supportedModels.some(supportedPattern => {
          // For exact matches, both should match exactly
          if (!supportedPattern.includes('*')) {
            return supportedPattern === requestedModel;
          }
          // For wildcard patterns, ensure proper prefix matching
          return this.matchesWildcard(supportedPattern, requestedModel);
        })
      );
      if (!modelMatch) return false;
    }

    // Check provider match if providers are specified
    if (params.providers?.length) {
      const providerMatch = params.providers.some((provider: any) =>
        this.supportedProviders.some(supported => this.matchesWildcard(supported, provider))
      );
      if (!providerMatch) return false;
    }

    return true;
  }

  protected async processToolCalls(toolCalls: ToolCall[], params: RouterParams): Promise<ToolResult[]> {
    if (!params.tools?.length || !toolCalls?.length) return [];

    const results: ToolResult[] = [];
    for (const toolCall of toolCalls) {
      if (toolCall.type === 'function') {
        const tool = params.tools.find((t: any) => t.function.name === toolCall.function.name);
        if (!tool) {
          console.warn(`Tool ${toolCall.function.name} not found in provided tools`);
          continue;
        }

        try {
          // Look for a tool executor in the router params
          const executor = params.toolExecutors?.[toolCall.function.name];
          if (!executor) {
            console.warn(`No executor found for tool ${toolCall.function.name}`);
            continue;
          }

          // Execute the tool function with the provided arguments
          const args = JSON.parse(toolCall.function.arguments);
          const result = await executor(args);

          results.push({
            toolCallId: toolCall.id,
            output: JSON.stringify(result)
          });
        } catch (error) {
          console.error(`Error executing tool ${toolCall.function.name}:`, error);
          results.push({
            toolCallId: toolCall.id,
            output: JSON.stringify({ error: error instanceof Error ? error.message : 'Tool execution failed' })
          });
        }
      }
    }
    return results;
  }

  // This is now the main entry point that handles streaming
  async execute(
    messages: CompletionMessage[],
    params: RouterParams,
    onChunk?: StreamingCallback
  ): Promise<RouterResponse> {
    try {
      const result = await this.executeCompletion(messages, params, onChunk);

      // Process any tool calls
      const toolResults = await this.processToolCalls(result.toolCalls || [], params);

      // If there are tool results and we have streaming enabled, send them
      if (toolResults.length > 0 && onChunk) {
        await onChunk({
          content: '',
          done: false,
          toolResults
        });
      }

      const response: RouterResponse = {
        content: result.content,
        system: this.systemId,
        provider: result.provider || params.providers?.[0] || this.supportedProviders[0],
        model: result.model || params.models?.[0] || this.supportedModels[0],
        totalCost: result.totalCost,
        promptTokens: result.promptTokens,
        completionTokens: result.completionTokens,
        totalTokens: result.totalTokens,
        tokenRatePerSec: result.tokenRatePerSec,
        firstTokenMs: result.firstTokenMs,
        latencyMs: result.latencyMs,
        generationTimeMs: result.generationTimeMs,
        finishReason: result.finishReason,
        additional: result.additional,
        toolCalls: result.toolCalls,
        toolResults
      };

      // If we have a streaming callback, send the full response with metadata
      if (!this.supportsStreaming() && onChunk) {
        await onChunk({
          content: result.content,
          done: true,
          metadata: {
            system: response.system,
            provider: response.provider,
            model: response.model,
            totalCost: response.totalCost,
            promptTokens: response.promptTokens,
            completionTokens: response.completionTokens,
            totalTokens: response.totalTokens,
            tokenRatePerSec: response.tokenRatePerSec,
            firstTokenMs: response.firstTokenMs,
            latencyMs: response.latencyMs,
            generationTimeMs: response.generationTimeMs,
            finishReason: response.finishReason,
            additional: response.additional
          },
          toolCalls: result.toolCalls,
          toolResults
        });
      }

      return response;
    } catch (error) {
      // Ensure we mark the stream as done even if there's an error
      if (onChunk) {
        await onChunk({
          content: error instanceof Error ? error.message : 'An error occurred',
          done: true,
          error: error instanceof Error ? error : new Error('An error occurred')
        });
      }
      throw error;
    }
  }

  protected abstract supportsStreaming(): boolean;

  // This is what concrete implementations should override - now returns metadata
  protected abstract executeCompletion(
    messages: CompletionMessage[],
    params: RouterParams,
    onChunk?: StreamingCallback
  ): Promise<{
    content: string;
    provider: string;
    model: string;
    totalCost?: number;
    promptTokens?: number;
    completionTokens?: number;
    totalTokens?: number;
    tokenRatePerSec?: number;
    firstTokenMs?: number;
    latencyMs?: number;
    generationTimeMs?: number;
    finishReason?: string;
    additional?: Record<string, unknown>;
    toolCalls?: ToolCall[];
  }>;
}

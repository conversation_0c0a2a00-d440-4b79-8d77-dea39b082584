import fetch from 'node-fetch';

export class ClaudeService {
	async getCompletion(prompt: string, max_tokens?: number, model?: string, tools?: Array<any>): Promise<string> {
		try {
			const request: any = {
				model: model || 'claude-3-5-sonnet-20241022',
				max_tokens: max_tokens || 4096,
				messages: [
					{
						role: 'user',
						content: prompt
					}
				],
				temperature: 0.7
			};
			if (tools) {
				request.tools = tools;
			}
			const response = await fetch('https://api.anthropic.com/v1/messages', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'x-api-key': '************************************************************************************************************',
					'anthropic-version': '2023-06-01'
				},
				body: JSON.stringify(request)
			});

			if (!response.ok) {
				const errorData: any = await response.json();
				throw new Error(`Claude API error: ${errorData.error?.message || 'Unknown error'}`);
			}

			const data: any = await response.json();
			return data.content[0].text;
		} catch (error) {
			console.error('Claude API Error:', error);
			throw new Error('Failed to get response from Claude');
		}
	}

}

import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { LLMRouterService } from './llm-router.service';
import { CompletionMessage, RouterParams, RouterResponse } from './types';

interface LLMRouterRequest {
  messages: CompletionMessage[];
  params: RouterParams;
}

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    console.log('Event body:', event.body);
    
    if (!event.body) {
      return {
        statusCode: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Credentials': true,
        },
        body: JSON.stringify({ 
          error: 'Missing request body' 
        }),
      };
    }

    const request: LLMRouterRequest = JSON.parse(event.body);
    
    // Validate required fields
    if (!request.messages || !Array.isArray(request.messages) || request.messages.length === 0) {
      return {
        statusCode: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Credentials': true,
        },
        body: JSON.stringify({ 
          error: 'Missing or invalid required field: messages' 
        }),
      };
    }

    if (!request.params) {
      return {
        statusCode: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Credentials': true,
        },
        body: JSON.stringify({ 
          error: 'Missing required field: params' 
        }),
      };
    }

    // Initialize the LLM Router service
    const llmRouter = new LLMRouterService();

    // Process the request
    const response: RouterResponse = await llmRouter.executeCompletion(
      request.messages,
      request.params
    );

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Credentials': true,
      },
      body: JSON.stringify(response),
    };
  } catch (error) {
    console.error('Error in LLM Router handler:', error);
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Credentials': true,
      },
      body: JSON.stringify({ 
        error: 'An internal error occurred', 
        details: error instanceof Error ? error.message : String(error) 
      }),
    };
  }
};
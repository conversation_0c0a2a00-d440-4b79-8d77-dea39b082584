
export interface ChatOptions {
  promptTemplateId?: number;
  organizationId: number;
  routerParams: RouterParams;
}

export interface RouterParams {
  systems?: string[];
  models?: string[];
  providers?: string[];
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
  tools?: ToolDefinition[];
  toolExecutors?: {
    [functionName: string]: (args: any) => Promise<any>;
  };
  web_search_options?: {
    search_context_size?: 'low' | 'medium' | 'high';
    max_results?: number;
    include_domains?: string[];
    exclude_domains?: string[];
  };
}

export interface LLMMetadata {
  system: string;
  provider: string;
  model: string;
  additional?: Record<string, unknown>;
}

export interface StreamingChunk {
  content: string;
  done: boolean;
  error?: Error;
  metadata?: {
    system: string;
    provider: string;
    model: string;
    totalCost?: number;
    promptTokens?: number;
    completionTokens?: number;
    totalTokens?: number;
    tokenRatePerSec?: number;
    firstTokenMs?: number;
    latencyMs?: number;
    generationTimeMs?: number;
    finishReason?: string;
    // For any provider-specific or non-standard metadata
    additional?: Record<string, unknown>;
  };
  toolCalls?: ToolCall[];
  toolResults?: ToolResult[];
}

export type StreamingCallback = (chunk: StreamingChunk) => Promise<void>;

export interface RouterTarget {
  id: string;
  systemId: string;
  supportedModels: string[];
  supportedProviders: string[];
  canHandle(params: RouterParams): boolean;
  execute(
    messages: CompletionMessage[],
    params: RouterParams,
    onChunk?: StreamingCallback
  ): Promise<RouterResponse>;
}

export interface RouterResponse {
  content: string;
  system: string;
  provider: string;
  model: string;
  totalCost?: number;
  promptTokens?: number;
  completionTokens?: number;
  totalTokens?: number;
  tokenRatePerSec?: number;
  firstTokenMs?: number;
  latencyMs?: number;
  generationTimeMs?: number;
  finishReason?: string;
  // For any provider-specific or non-standard metadata
  additional?: Record<string, unknown>;
  toolCalls?: ToolCall[];
  toolResults?: ToolResult[];
}

export interface OpenRouterParams {
  model: string;
  messages: {
    role: string;
    content: string;
  }[];
}

export interface CompletionMessage {
  role: "user" | "assistant" | "system" | string;
  content: string;
}

export interface ToolDefinition {
  type: string;
  function: {
    name: string;
    description: string;
    parameters: {
      type: string;
      properties?: Record<string, {
        type: string;
        description?: string;
        enum?: string[];
      }>;
      required?: string[];
    };
  };
}

export interface ToolCall {
  type: 'function';
  id: string;
  function: {
    name: string;
    arguments: string;
  };
}

export interface ToolResult {
  toolCallId: string;
  output: string;
}

import { WebsiteContextService, WebsiteContext, WebsiteContextOptions } from '../website-context/website-context.service';
import { SitemapService, SitemapURL } from '../website-context/sitemap.service';
import { LLMRouterService } from '../llm-router/llm-router.service';
import { CompletionMessage, RouterParams } from '../llm-router/types';

export interface WebsiteQAOptions extends WebsiteContextOptions {
  llmParams?: RouterParams;
  maxPagesToVisit?: number;
  useLLMForLinkSelection?: boolean;
  followUpQuestions?: string[]; // Added this to handle follow-up questions
  phase?: 'fast' | 'enhanced' | 'both'; // Added phase control
}

export interface QAResult {
  question: string;
  answer: string;
}

interface PageContext extends WebsiteContext {
  relevanceScore?: number;
}

const MAX_TOKENS = 180000; // Leave some buffer for the system message and other content
const AVERAGE_CHARS_PER_TOKEN = 4; // Approximate chars per token for English text
const MAX_CHARS = MAX_TOKENS * AVERAGE_CHARS_PER_TOKEN;

const URL_SCORING_MAX_TOKENS = 180000; // Lower limit for URL scoring task
const URL_SCORING_MAX_CHARS = URL_SCORING_MAX_TOKENS * AVERAGE_CHARS_PER_TOKEN;
const MAX_URLS_PER_BATCH = 1000000; // Maximum URLs to score in one request

export class WebsiteQAService {
  private readonly websiteContextService: WebsiteContextService;
  private readonly sitemapService: SitemapService;
  private readonly defaultOptions: WebsiteQAOptions = {
    maxPagesToVisit: 10,
    useLLMForLinkSelection: true
  };

  constructor(
    private readonly llmRouter: LLMRouterService
  ) {
    this.websiteContextService = new WebsiteContextService();
    this.sitemapService = new SitemapService();
  }

  /**
   * Fast method to answer questions using web search instead of scraping
   * @returns An array of question-answer pairs from web search
   */
  private async answerQuestionsWithWebSearch(
    url: string,
    mainQuestion: string,
    options?: WebsiteQAOptions
  ): Promise<QAResult[]> {
    const mergedOptions = { ...this.defaultOptions, ...options };
    const results: QAResult[] = [];
    const domain = new URL(url).origin;
    
    // Prepare all questions to process in parallel
    const allQuestions = [mainQuestion, ...(mergedOptions.followUpQuestions || [])];
    
    console.log('Processing questions with web search:', allQuestions.length);
    
    // Process all questions in parallel using web search
    const questionPromises = allQuestions.map(async (question, index) => {
      const contextualQuestion = `Based on the website ${url} (${domain}), ${question}`;
      
      try {
        const response = await this.llmRouter.executeCompletion(
          [{ role: 'user', content: contextualQuestion }],
          {
            models: ['openai/gpt-4.1:online'],
            systems: ['OpenRouter'],
            ...mergedOptions.llmParams
          }
        );
        
        console.log(`Web search question ${index + 1} completed`);
        return { question: allQuestions[index], answer: response.content };
      } catch (error) {
        console.error(`Error with web search for question ${index + 1}:`, error);
        // Return a placeholder result that can be filled later
        return { question: allQuestions[index], answer: 'Processing...' };
      }
    });
    
    // Wait for all questions to complete
    const webSearchResults = await Promise.all(questionPromises);
    console.log('All web search questions completed');
    
    return webSearchResults;
  }

  /**
   * Process a question and optional follow-up questions about a website
   * @returns An array of question-answer pairs
   */
  async processQuestionsAboutWebsite(
    url: string, 
    mainQuestion: string, 
    options?: WebsiteQAOptions
  ): Promise<QAResult[]> {
    const mergedOptions = { ...this.defaultOptions, ...options };
    const phase = mergedOptions.phase || 'both';
    
    console.log(`Starting website QA process - Phase: ${phase}`);
    
    // Handle phase-specific processing
    if (phase === 'fast') {
      console.log('Fast phase only: Running web search');
      return await this.answerQuestionsWithWebSearch(url, mainQuestion, mergedOptions);
    }
    
    if (phase === 'enhanced') {
      console.log('Enhanced phase only: Running traditional scraping');
      return await this.processWithTraditionalScraping(url, mainQuestion, mergedOptions);
    }
    
    // Default 'both' phase: Two-phase approach
    console.log('Both phases: Starting two-phase website QA process');
    
    try {
      // PHASE 1: Lightning-fast web search answers
      console.log('Phase 1: Starting web search for instant answers');
      const webSearchResults = await this.answerQuestionsWithWebSearch(url, mainQuestion, mergedOptions);
      
      // Check if all web search results are successful (no 'Processing...' placeholders)
      const allWebSearchSuccessful = webSearchResults.every(result => result.answer !== 'Processing...');
      
      if (allWebSearchSuccessful) {
        console.log('Phase 1 complete: All questions answered via web search successfully');
        console.log('Skipping Phase 2 scraping - web search provided complete answers');
        return webSearchResults;
      } else {
        console.log('Phase 1 partial: Some web search answers failed, proceeding with Phase 2');
      }
      
    } catch (error) {
      console.error('Phase 1 web search failed completely, falling back to Phase 2:', error);
    }
    
    // PHASE 2: Traditional scraping method (fallback or enhancement)
    return await this.processWithTraditionalScraping(url, mainQuestion, mergedOptions);
  }

  /**
   * Traditional scraping method - extracted for reuse in phase-specific calls
   */
  private async processWithTraditionalScraping(
    url: string,
    mainQuestion: string,
    options: WebsiteQAOptions
  ): Promise<QAResult[]> {
    console.log('Starting traditional website scraping');
    const domain = new URL(url).origin;
    const results: QAResult[] = [];
    
    // Get all available URLs from sitemap or initial page
    const urls = await this.gatherUrls(domain, url, options);
    console.log('Gathered URLs');
    
    // Select which URLs to visit based on the question
    const selectedUrls = await this.selectRelevantUrls(urls, mainQuestion, options);
    console.log('Selected URLs', selectedUrls);
    
    // Gather context from selected pages
    const pageContexts = await this.gatherPageContexts(selectedUrls, options);
    console.log('Page contexts gathered');
    
    // Format all contexts for the LLM
    const formattedContext = this.formatWebsiteContext(pageContexts);
    console.log('Formatted context for LLM');
    
    // Create system message with context
    const systemMessage: CompletionMessage = {
      role: 'system',
      content: `You are a helpful assistant that answers questions about websites. Use the following context from the website to answer questions. Only use information from the provided context:\n\n${formattedContext}`
    };
    
    // Process the main question
    const mainAnswer = await this.queryLLM(mainQuestion, systemMessage, options.llmParams);
    results.push({ question: mainQuestion, answer: mainAnswer });

    console.log('Main question processed'); 
    
    // Create conversation history with the main Q&A
    const conversationHistory: CompletionMessage[] = [
      systemMessage,
      { role: 'user', content: mainQuestion },
      { role: 'assistant', content: mainAnswer }
    ];
    
    console.log('Conversation history created');

    // Process follow-up questions in parallel if provided
    if (options.followUpQuestions && options.followUpQuestions.length > 0) {
      const followUpPromises = options.followUpQuestions.map(async (question) => {
        // Add the follow-up question to the conversation history
        const currentHistory = [...conversationHistory, { role: 'user', content: question }];
        console.log('Processing follow-up question:', question);
        // Process the follow-up question
        const answer = await this.llmRouter.executeCompletion(currentHistory, {
          models: ['openai/gpt-4.1'],
          systems: ['OpenRouter', '*'],
          ...options.llmParams
        });
        
        console.log('Follow-up answer to question', question);
        return { question, answer: answer.content };
      });
      
      console.log('Waiting for follow-up questions to be processed');
      // Wait for all follow-up questions to be processed
      const followUpResults = await Promise.all(followUpPromises);
      console.log('Follow-up questions processed');
      results.push(...followUpResults);
    }

    console.log('Traditional scraping complete: All questions processed', results);
    
    return results;
  }

  private async gatherUrls(domain: string, initialUrl: string, options?: WebsiteQAOptions): Promise<string[]> {
    // First try getting URLs from sitemap
    const sitemapUrls = await this.sitemapService.discoverSitemapUrls(domain);
    let urls: string[] = [];
    
    if (sitemapUrls.length > 0) {
        console.log('Sitemap URLs found:');
        urls = sitemapUrls.map(url => url.loc);
    } else {
        console.log('No sitemap URLs found, falling back to initial page links.');
        // Fallback to scraping links from the initial page
        const initialContext = await this.websiteContextService.generateContext(initialUrl, {
          includeLinks: true,
          includeScreenshot: false,
          storePassword: options?.storePassword
        });

        console.log('Initial page context:');
        urls = initialContext.links || [];
    }
    
    // Always include the initial URL if it's not already in the list
    if (!urls.includes(initialUrl)) {
      urls.unshift(initialUrl); // Add to the beginning of the array
      console.log('Added initial URL to the list of URLs to process');
    }

    return urls;
  }

  private async selectRelevantUrls(
    urls: string[],
    question: string,
    options: WebsiteQAOptions
  ): Promise<string[]> {
    // Store the initial URL (always the first one after our gatherUrls method)
    const initialUrl = urls[0];
    // Ensure we have a valid maxPagesToVisit value
    const maxPages = options.maxPagesToVisit || this.defaultOptions.maxPagesToVisit || 10;
    
    if (!options.useLLMForLinkSelection) {
      // If not using LLM, just return the first N URLs (which will include the initial URL)
      return urls.slice(0, maxPages);
    }

    // Use LLM to score URL relevance to the question
    const scoredUrls = await this.scoreUrlRelevance(urls, question);
    console.log('scoredUrls');
    
    // Sort all URLs by score, including the initial URL
    const sortedUrls = scoredUrls
      .sort((a, b) => (b.score || 0) - (a.score || 0));
    
    // Check if the initial URL is in the sorted results
    const hasInitialUrl = sortedUrls.some(item => item.url === initialUrl);
    
    if (hasInitialUrl) {
      // Initial URL is already in the top-scoring URLs, just take the top N
      return sortedUrls.slice(0, maxPages).map(item => item.url);
    } else {
      // Initial URL is not in the top-scoring URLs
      // Take (maxPages-1) top URLs and add the initial URL to the list
      const topUrls = sortedUrls.slice(0, maxPages - 1).map(item => item.url);
      topUrls.push(initialUrl);
      return topUrls;
    }
  }

  private async scoreUrlRelevance(urls: string[], question: string): Promise<Array<{ url: string; score: number }>> {
    // Simple relevance check based on URL text matching to pre-filter URLs
    const preFilteredUrls = this.preFilterUrls(urls, question);
    
    // Split URLs into smaller batches if needed
    const urlBatches = this.splitIntoBatches(preFilteredUrls, MAX_URLS_PER_BATCH);
    const allScores: Array<{ url: string; score: number }> = [];
    
    for (const batch of urlBatches) {
        const messages: CompletionMessage[] = [
            {
                role: 'system',
                content: `You are a URL relevance scorer. Given a question and a list of URLs, score each URL's likely relevance to answering the question from 0-100 based on the URL path and structure. Return only a JSON array of objects with 'url' and 'score' properties.`
            },
            {
                role: 'user',
                content: `Question: "${question}"\nURLs to score:\n${batch.join('\n')}`
            }
        ];

        // Verify total input size
        const totalInput = messages.reduce((acc, m) => acc + m.content.length, 0);
        if (totalInput > URL_SCORING_MAX_CHARS) {
            console.warn(`URL batch too large (${totalInput} chars), reducing batch size`);
            const reducedBatch = this.reduceBatchToFit(batch, URL_SCORING_MAX_CHARS - messages[0].content.length - question.length - 50);
            messages[1].content = `Question: "${question}"\nURLs to score:\n${reducedBatch.join('\n')}`;
        }

        try {
            const response = await this.llmRouter.executeCompletion(messages, {
                models: ['anthropic/claude-3.5-sonnet'],
                systems: ['OpenRouter'],
            });
            const scores = this.parseScores(response.content, batch);
            allScores.push(...scores);
        } catch (e) {
            console.warn('Error scoring URL batch:', e);
            // Fallback scoring for failed batch
            allScores.push(...batch.map(url => ({ url, score: 50 })));
        }
    }

    return allScores;
}

private preFilterUrls(urls: string[], question: string): string[] {
    // Convert question to lowercase and split into keywords
    const keywords = question.toLowerCase().split(/\W+/).filter(k => k.length > 3);
    
    // Score URLs based on keyword matches
    return urls.sort((a, b) => {
        const aScore = keywords.reduce((acc, k) => acc + (a.toLowerCase().includes(k) ? 1 : 0), 0);
        const bScore = keywords.reduce((acc, k) => acc + (b.toLowerCase().includes(k) ? 1 : 0), 0);
        return bScore - aScore;
    });
}

private splitIntoBatches(urls: string[], batchSize: number): string[][] {
    const batches: string[][] = [];
    for (let i = 0; i < urls.length; i += batchSize) {
        batches.push(urls.slice(i, i + batchSize));
    }
    return batches;
}

private reduceBatchToFit(urls: string[], maxChars: number): string[] {
    let totalChars = 0;
    return urls.filter(url => {
        if (totalChars + url.length + 1 <= maxChars) {
            totalChars += url.length + 1; // +1 for newline
            return true;
        }
        return false;
    });
}

private parseScores(content: string, originalUrls: string[]): Array<{ url: string; score: number }> {
    try {
        let jsonContent = content;
        const jsonMatch = content.match(/\[\s*\{.*\}\s*\]/s);
        if (jsonMatch) {
            jsonContent = jsonMatch[0];
        }
        jsonContent = jsonContent.replace(/```json|```/g, '').trim();
        
        const scores = JSON.parse(jsonContent);
        return scores.filter((score: any) => 
            typeof score.url === 'string' && 
            typeof score.score === 'number' &&
            originalUrls.includes(score.url)
        );
    } catch (e) {
        console.warn('Error parsing URL scores:', e);
        return originalUrls.map(url => ({ url, score: 50 }));
    }
}

  private async gatherPageContexts(urls: string[], options: WebsiteContextOptions): Promise<PageContext[]> {
    const contexts: PageContext[] = [];
    
    for (const url of urls) {
      try {
        const context = await this.websiteContextService.generateContext(url, {
          ...options,
          includeLinks: false, // We don't need links from subsequent pages
          includeScreenshot: false
        });
        contexts.push(context);
      } catch (error) {
        console.warn(`Error fetching context for ${url}:`, error);
      }
    }
    
    return contexts;
  }

  private formatWebsiteContext(contexts: PageContext[]): string {
    let combinedContext = 'Website content from multiple pages:\n\n';
    let currentLength = combinedContext.length;
    
    // Sort contexts by relevance score if available
    const sortedContexts = [...contexts].sort((a, b) => 
      (b.relevanceScore || 0) - (a.relevanceScore || 0)
    );

    for (const context of sortedContexts) {
      const pageHeader = `=== Content from ${context.url} ===\n`;
      const pageContent = context.text;
      const pageFooter = '\n\n';
      
      const totalPageLength = pageHeader.length + pageContent.length + pageFooter.length;
      
      // Check if adding this page would exceed the limit
      if (currentLength + totalPageLength > MAX_CHARS) {
        // If this is the first page, we need to include at least some content
        if (currentLength === combinedContext.length) {
          const remainingSpace = MAX_CHARS - currentLength - pageHeader.length - pageFooter.length;
          if (remainingSpace > 0) {
            combinedContext += pageHeader;
            combinedContext += pageContent.slice(0, remainingSpace);
            combinedContext += pageFooter;
            console.log(`Truncated first page content to ${remainingSpace} characters`);
          }
        }
        console.log(`Stopping context aggregation at ${currentLength} characters`);
        break;
      }

      combinedContext += pageHeader;
      combinedContext += pageContent;
      combinedContext += pageFooter;
      currentLength += totalPageLength;
    }
    
    console.log(`Total context length: ${currentLength} characters (approximately ${Math.round(currentLength/AVERAGE_CHARS_PER_TOKEN)} tokens)`);
    return combinedContext;
  }

  private async queryLLM(
    question: string, 
    systemMessage: CompletionMessage, 
    params?: RouterParams
  ): Promise<string> {
    const messages: CompletionMessage[] = [
      systemMessage,
      { role: 'user', content: question }
    ];

    const response = await this.llmRouter.executeCompletion(messages, {
      models: ['openai/gpt-4.1'],
      systems: ['OpenRouter', '*'],
      ...params
    });
    
    return response.content;
  }
}
import {
	AthenaClient,
	BatchGetNamedQueryCommand,
	ListNamedQueriesCommand,
	StartQueryExecutionCommand,
	GetNamedQueryCommand,
	StartQueryExecutionCommandOutput,
	GetQueryExecutionCommand,
	QueryExecutionState,
	GetQueryResultsCommand,
	GetQueryResultsCommandOutput,
} from "@aws-sdk/client-athena";
const client = new AthenaClient ({region: 'us-east-1'});
export enum AthenaWorkGroup {
	METRIC = 'metric',
    ADHOC  = 'adhoc'
}
export async function runAthenaQuery(
	queryString: string,
	outputLocation: string,
	failCount: number = 0
): Promise<StartQueryExecutionCommandOutput> {
	console.log("runAthenaQuery: " + queryString);
	const params = {
		QueryString: queryString,
		ResultConfiguration: {
			OutputLocation: outputLocation,
		},
	};
	const command = new StartQueryExecutionCommand(params);

	try {
		const data = await client.send(command);
		return data;
	} catch (error) {
		console.log("Failed to run Athena Query: " + error);
		if (
			(error as any).name.indexOf("TooManyRequestsException") > -1 ||
			(error as any).name.indexOf("ThrottlingException") > -1
		) {
			await waitForTimeout(failCount * 1000 + 1000);
			return await runAthenaQuery(
				queryString,
				outputLocation,
				failCount + 1
			);
		} else {
			throw error;
		}
	}
}

export async function runAthenaQueryV2(
	queryString: string,
	outputLocation: string,
	failCount: number = 0,
	workgroup: string = AthenaWorkGroup.ADHOC,
	catalog: string = 'AWSDataCatalog',
	database?: string
): Promise<StartQueryExecutionCommandOutput> {
	console.log("runAthenaQuery: " + queryString);
	const params: any = {
		QueryExecutionContext: {	
			Catalog: catalog,
			...(database && { Database: database })
		},
		QueryString: queryString,
		ResultConfiguration: {
			OutputLocation: outputLocation,
		},
		WorkGroup: workgroup,
	};
	
	if (database) {
		params.QueryExecutionContext.Database = database;
	}
	
	console.log("params: " + JSON.stringify(params));
	const command = new StartQueryExecutionCommand(params);

	try {
		const data = await client.send(command);
		console.log(
			"Got Some data from runAthenaQuery: " + JSON.stringify(data)
		);
		return data;
	} catch (error) {
		console.log("Failed to run Athena Query: " + error);
		if (
			(error as any).name.indexOf("TooManyRequestsException") > -1 ||
			(error as any).name.indexOf("ThrottlingException") > -1
		) {
			await waitForTimeout(failCount * 1000 + 1000);
			return await runAthenaQuery(
				queryString,
				outputLocation,
				failCount + 1
			);
		} else {
			throw error;
		}
	}
}

export async function waitForQueryExecution(queryId: string, waitMs = 3000) : Promise<boolean> {
    const command = new GetQueryExecutionCommand({
        QueryExecutionId: queryId
    })

    try {
        while(true) {
            const data = await client.send(command);

            switch(data.QueryExecution?.Status?.State) {
                case QueryExecutionState.SUCCEEDED:
                    return true;
                case QueryExecutionState.FAILED:
                    console.log("Execution failed")
                    return false;
                case QueryExecutionState.CANCELLED:
                    console.log("Execution canceled")
                    return false;
                default:
                    await new Promise(res=>setTimeout(res, waitMs));
            }
        }
    } catch (error) {
        console.log("Failed to run Athena Query: " + error);
    } finally {
        // finally.
    }
    return false;
}

export async function waitForTimeout(timeout: number) {
	return new Promise((resolve) => {
		setTimeout(() => {
			resolve("");
		}, timeout);
	});
}

export async function getQueryResults(
    queryId: string, 
    nextToken?: string, 
    allRows: any[] = []
): Promise<GetQueryResultsCommandOutput> {
    const command = new GetQueryResultsCommand({
        QueryExecutionId: queryId,
        NextToken: nextToken
    });

    try {
        const data = await client.send(command);
        const rows = data.ResultSet?.Rows || [];
        allRows.push(...rows);

        if (data.NextToken) {
            return getQueryResults(queryId, data.NextToken, allRows);
        } else {
            return {
                ...data,
                ResultSet: {
                    ...data.ResultSet,
                    Rows: allRows
                }
            };
        }
    } catch (error) {
        console.error("Failed to run Athena Query: " + error);
        throw error; // Rethrow the error to allow caller to handle it
    }
}

export async function updateAthenaPartitions(tableName: string, athenaOutputBucket: string, catalog: string = 'AWSDataCatalog') {
	const athenaQuery = `MSCK REPAIR TABLE ${tableName};`;
	console.log(`athenaQuery 👉`, athenaQuery);
	
	if (!athenaOutputBucket) {
		throw new Error(
			"ATHENA_OUTPUT_BUCKET environment variable is not set"
		);
	}
	const athenaResult = await runAthenaQueryV2(
		athenaQuery,
		athenaOutputBucket,
		0,
		AthenaWorkGroup.ADHOC,
		catalog
	);
	console.log(`athenaResult 👉`, athenaResult);
	let checkTableId = athenaResult.QueryExecutionId!;
	let checkTableQueryResult: boolean = await waitForQueryExecution(
		checkTableId
	);
	if (!checkTableQueryResult) {
		throw new Error("Athena query failed to execute");
	}
}

export async function addAthenaPartitionsOrg(tableName: string, orgId: string, folder: string, curatedBucket: string, athenaOutputBucket: string, catalog: string = 'AWSDataCatalog') {
	const athenaQuery = `ALTER TABLE ${tableName} ADD IF NOT EXISTS PARTITION (organization='${orgId}') LOCATION 's3://${curatedBucket}/${folder}/organization=${orgId}/';`;
	console.log(`athenaQuery 👉`, athenaQuery);
	
	if (!athenaOutputBucket) {
		throw new Error(
			"ATHENA_OUTPUT_BUCKET environment variable is not set"
		);
	}
	const athenaResult = await runAthenaQueryV2(
		athenaQuery,
		athenaOutputBucket,
		0,
		AthenaWorkGroup.ADHOC,
		catalog
	);
	console.log(`athenaResult 👉`, athenaResult);
	let checkTableId = athenaResult.QueryExecutionId!;
	let checkTableQueryResult: boolean = await waitForQueryExecution(
		checkTableId
	);
	if (!checkTableQueryResult) {
		throw new Error("Athena query failed to execute");
	}
}

export async function addAthenaPartitions(tableName: string, orgId: string, curatedBucket: string, athenaOutputBucket: string, catalog: string = 'AWSDataCatalog') {
	const athenaQuery = `ALTER TABLE ${tableName} ADD IF NOT EXISTS PARTITION (organization='${orgId}') LOCATION 's3://${curatedBucket}/shopify_orders/organization=${orgId}/';`;
	console.log(`athenaQuery 👉`, athenaQuery);
	
	if (!athenaOutputBucket) {
		throw new Error(
			"ATHENA_OUTPUT_BUCKET environment variable is not set"
		);
	}
	const athenaResult = await runAthenaQueryV2(
		athenaQuery,
		athenaOutputBucket,
		0,
		AthenaWorkGroup.ADHOC,
		catalog
	);
	console.log(`athenaResult 👉`, athenaResult);
	let checkTableId = athenaResult.QueryExecutionId!;
	let checkTableQueryResult: boolean = await waitForQueryExecution(
		checkTableId
	);
	if (!checkTableQueryResult) {
		throw new Error("Athena query failed to execute");
	}
}

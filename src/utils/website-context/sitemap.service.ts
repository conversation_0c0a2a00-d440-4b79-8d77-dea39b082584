import fetch, { Response as FetchResponse } from 'node-fetch';
import { parseStringPromise } from 'xml2js';
import robotsParser from 'robots-parser';

export interface SitemapURL {
  loc: string;
  lastmod?: string;
  changefreq?: string;
  priority?: string;
}

export class SitemapService {
  private async fetchWithTimeout(url: string, timeout = 5000): Promise<FetchResponse> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    try {
      const response = await fetch(url, { signal: controller.signal });
      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  private async findSitemapFromRobots(domain: string): Promise<string[]> {
    try {
      const robotsUrl = new URL('/robots.txt', domain).toString();
      const response = await this.fetchWithTimeout(robotsUrl);
      const robotsTxt = await response.text();
      
      const robots = robotsParser(robotsUrl, robotsTxt);
      const sitemaps = robots.getSitemaps();
      
      return sitemaps;
    } catch (error) {
      console.warn('Error fetching robots.txt:', error);
      return [];
    }
  }

  private async tryCommonSitemapLocations(domain: string): Promise<string | null> {
    const commonLocations = [
      '/sitemap.xml',
      '/sitemap_index.xml',
      '/sitemap/',
      '/sitemap/sitemap.xml',
      '/sitemapindex.xml'
    ];

    for (const location of commonLocations) {
      try {
        const url = new URL(location, domain).toString();
        const response = await this.fetchWithTimeout(url);
        if (response.ok && response.headers.get('content-type')?.includes('xml')) {
          return url;
        }
      } catch (error) {
        continue;
      }
    }
    return null;
  }

  private async parseSitemapXML(xml: string): Promise<SitemapURL[]> {
    try {
      const result = await parseStringPromise(xml, { explicitArray: false });
      
      if (result.sitemapindex) {
        // This is a sitemap index file
        const sitemaps = Array.isArray(result.sitemapindex.sitemap) 
          ? result.sitemapindex.sitemap 
          : [result.sitemapindex.sitemap];
        
        const allUrls: SitemapURL[] = [];
        for (const sitemap of sitemaps) {
          try {
            const response = await this.fetchWithTimeout(sitemap.loc);
            const subSitemapXml = await response.text();
            const subUrls = await this.parseSitemapXML(subSitemapXml);
            allUrls.push(...subUrls);
          } catch (error) {
            console.warn(`Error parsing sub-sitemap ${sitemap.loc}:`, error);
          }
        }
        return allUrls;
      } else if (result.urlset) {
        // This is a regular sitemap file
        const urls = Array.isArray(result.urlset.url) 
          ? result.urlset.url 
          : [result.urlset.url];
        
        return urls.map((url: any) => ({
          loc: url.loc,
          lastmod: url.lastmod,
          changefreq: url.changefreq,
          priority: url.priority
        }));
      }
      
      return [];
    } catch (error) {
      console.error('Error parsing sitemap XML:', error);
      return [];
    }
  }

  async discoverSitemapUrls(domain: string): Promise<SitemapURL[]> {
    // First try robots.txt
    const sitemapsFromRobots = await this.findSitemapFromRobots(domain);
    
    if (sitemapsFromRobots.length > 0) {
      for (const sitemapUrl of sitemapsFromRobots) {
        try {
          const response = await this.fetchWithTimeout(sitemapUrl);
          const xml = await response.text();
          const urls = await this.parseSitemapXML(xml);
          if (urls.length > 0) {
            return urls;
          }
        } catch (error) {
          console.warn(`Error fetching sitemap from robots.txt ${sitemapUrl}:`, error);
        }
      }
    }

    // Try common locations
    const sitemapUrl = await this.tryCommonSitemapLocations(domain);
    if (sitemapUrl) {
      try {
        const response = await this.fetchWithTimeout(sitemapUrl);
        const xml = await response.text();
        return await this.parseSitemapXML(xml);
      } catch (error) {
        console.warn('Error parsing sitemap from common location:', error);
      }
    }

    return [];
  }
}
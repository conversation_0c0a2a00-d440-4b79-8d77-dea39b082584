import { S3 } from 'aws-sdk';
import chromium from '@sparticuz/chromium';
import puppeteer, { PuppeteerLaunchOptions } from 'puppeteer-core';
import fetch from 'node-fetch';

export interface WebsiteContextOptions {
  maxTextLength?: number;
  includeScreenshot?: boolean;
  includeLinks?: boolean;
  timeout?: number;
  storePassword?: string;
  extraTimeout?: number;
  waitUntil?: 'domcontentloaded' | 'networkidle0' | 'load';
  maxLinksLength?: number;
}

export interface WebsiteContext {
  text: string;
  links?: string[];
  screenshot?: Buffer;
  url: string;
  faviconUrl?: string;
}

export class WebsiteContextService {
  private readonly defaultOptions: WebsiteContextOptions = {
    maxTextLength: 100000,
    includeScreenshot: false,
    includeLinks: true,
    timeout: 15000,
    waitUntil: 'domcontentloaded',
    extraTimeout: 10000,
    maxLinksLength: 50000
  };

  async generateContext(url: string, options?: WebsiteContextOptions): Promise<WebsiteContext> {
    try {
      return await this.scrape(url, options);
    } catch (e: any) {
      console.error(e);
      console.error('Retrying with alternate waiting strategy');
      return await this.scrape(url, options, true);
    }
  }

  private async scrape(url: string, options?: WebsiteContextOptions, usePuppeteerWaits = false): Promise<WebsiteContext> {
    const mergedOptions = { ...this.defaultOptions, ...options };

    console.log(`Scraping URL: ${url}`);
    
    const browser = await puppeteer.launch({
      executablePath: await chromium.executablePath(),
      headless: true,
      ignoreHTTPSErrors: true,
      defaultViewport: chromium.defaultViewport,
      args: [...chromium.args, "--hide-scrollbars", "--disable-web-security"],
    } as PuppeteerLaunchOptions);

    try {
      console.log('Browser launched');
      const page = await browser.newPage();
      
      // Set a custom user-agent and headers for better compatibility
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
      await page.setExtraHTTPHeaders({
        'Accept-Language': 'en-US,en;q=0.9',
        'Referer': 'https://www.google.com/'
      });

      let status;
      try {
        status = await page.goto(url, { 
          waitUntil: mergedOptions.waitUntil, 
          timeout: mergedOptions.timeout || 0 
        });
        const code = status?.status() || 0;
        if (code > 399 || code < 200) {
          throw new Error(`HTTP Error: ${code}`);
        }
        console.log(`Page loaded with status code: ${code}`);
      } catch (error: any) {
        console.error(`Failed to load the page: ${error.message}`);
        throw error;
      }

      console.log('ready to check')
      console.log('Options:', mergedOptions);
      
      // Handle password-protected pages - now occurs regardless of usePuppeteerWaits flag
      const passwordPrompt = await page.$('form[action="/password"] input.form-input#password[type="password"][autocomplete="nope"]');
      if (passwordPrompt && mergedOptions.storePassword) {
        console.log('Password prompt detected, entering password');
        await page.type('#password', mergedOptions.storePassword);
        await page.click('button[type=submit]');
        console.log('Password submitted, waiting for navigation');
        
        try {
          // Try multiple waiting strategies to handle different password page behaviors
          await Promise.race([
            // Wait for navigation event (traditional page load)
            page.waitForNavigation({ 
              waitUntil: mergedOptions.waitUntil, 
              timeout: mergedOptions.timeout || 0 
            }).catch(e => console.log('Navigation timeout, trying other methods')),
            
            // Wait for the password form to disappear (for sites that use DOM manipulation)
            page.waitForFunction(
              () => !document.querySelector('form[action="/password"]'),
              { timeout: mergedOptions.timeout || 0 }
            ).catch(e => console.log('Form disappearance timeout')),
            
            // Fallback timeout to prevent hanging
            new Promise(r => setTimeout(r, mergedOptions.extraTimeout || 5000))
          ]);
          
          // Additional short wait to ensure content is loaded
          await page.waitForTimeout(500);
          
          console.log('Navigation complete (or timeout reached)');
        } catch (error) {
          console.log(`Password navigation handling: ${error.message}`);
          // Continue execution even if there was an error with navigation
        }
      }

      // Apply different waiting strategies based on usePuppeteerWaits
      if (!usePuppeteerWaits) {
        await this.waitForLoad(page, mergedOptions.timeout);
        await new Promise(r => setTimeout(r, mergedOptions.extraTimeout));
      } else {
        await new Promise(r => setTimeout(r, mergedOptions.extraTimeout));
        await Promise.race([
          new Promise(r => setTimeout(r, mergedOptions.extraTimeout)),
          page.waitForNetworkIdle().catch(),
          page.waitForNavigation().catch()
        ]);
      }

      const [text, links, screenshot, faviconUrl] = await Promise.all([
        page.evaluate((maxLength) => {
          return document.body.innerText.slice(0, maxLength);
        }, mergedOptions.maxTextLength),
        mergedOptions.includeLinks ? page.evaluate(() => {
          return Array.from(document.querySelectorAll('a[href^=http]'))
            .map((x: any) => x.href);
        }) : Promise.resolve(undefined),
        mergedOptions.includeScreenshot ? page.screenshot({ fullPage: true }) : Promise.resolve(undefined),
        this.findBestFaviconURL(page, url)
      ]);

      console.log('Page evaluated');

      console.log('Text', text);
      console.log('Text length:', text.length);
      console.log('Links found:', links?.length);



      // Truncate links if needed
      const truncatedLinks = this.truncateLinks(links || [], mergedOptions);

      const context: WebsiteContext = {
        text,
        url,
      };

      if (truncatedLinks.length > 0) context.links = truncatedLinks;
      if (screenshot) context.screenshot = screenshot;
      if (faviconUrl) context.faviconUrl = faviconUrl;

      return context;
    } finally {
      await browser.close();
    }
  }

  private truncateLinks(links: string[], options: WebsiteContextOptions): string[] {
    const truncatedLinks: string[] = [];
    let totalLength = 0;
    const maxLength = options.maxLinksLength ?? this.defaultOptions.maxLinksLength!;

    for (const link of links) {
      const linkLength = link.length;
      if (totalLength + linkLength < maxLength) {
        truncatedLinks.push(link);
        totalLength += linkLength;
      } else {
        break;
      }
    }

    return truncatedLinks;
  }

  private async findBestFaviconURL(page: any, pageUrl: string): Promise<string> {
    try {
      const rootUrl = new URL(pageUrl).origin;
      const selectorsToTry = [
        'link[rel="icon"]',
        'link[rel="shortcut icon"]'
      ];

      let faviconUrlFromDocument = null;
      for (const selector of selectorsToTry) {
        const href = await this.getDOMElementHRef(page, selector);
        if (href) {
          faviconUrlFromDocument = href;
          break;
        }
      }

      if (!faviconUrlFromDocument) {
        const url = `${rootUrl}/favicon.ico`;
        try {
          const response = await fetch(url, { method: 'HEAD' });
          if (response.ok) {
            return url;
          }
        } catch (e) {
          console.warn('Favicon request failed', e);
        }
        return 'https://d3q4ufbgs1i4ak.cloudfront.net/raleon_icon.png';
      }

      if (faviconUrlFromDocument.startsWith('http') || faviconUrlFromDocument.startsWith('//')) {
        return faviconUrlFromDocument;
      } else if (faviconUrlFromDocument.startsWith('/')) {
        return rootUrl + faviconUrlFromDocument;
      } else {
        return `${pageUrl}/${faviconUrlFromDocument}`;
      }
    } catch (e) {
      console.warn('Error finding favicon', e);
      return 'https://d3q4ufbgs1i4ak.cloudfront.net/raleon_icon.png';
    }
  }

  private async getDOMElementHRef(page: any, query: string): Promise<string> {
    return page.evaluate((q: string) => {
      const elem = document.querySelector(q);
      return elem ? (elem.getAttribute('href') || '') : '';
    }, query);
  }

  private async waitForWindowEvent(page: any, eventName: string, timeout = 10000): Promise<void> {
    return Promise.race([
      page.evaluate((eventName: string) => {
        return new Promise<void>((resolve) => {
          window.addEventListener(eventName, () => resolve());
        });
      }, eventName),
      page.waitForTimeout(timeout)
    ]);
  }

  private async waitForLoad(page: any, timeout = 10000): Promise<void> {
    const readyState = await page.evaluate(() => document.readyState);
    if (readyState === 'complete') {
      return;
    }
    return this.waitForWindowEvent(page, 'load', timeout);
  }

  formatContextForLLM(context: WebsiteContext): string {
    let prompt = `Website URL: ${context.url}\n\n`;
    prompt += `Content from website:\n${context.text}\n\n`;
    
    if (context.links?.length) {
      prompt += `Links found on the website:\n${context.links.join('\n')}\n\n`;
    }
    
    return prompt;
  }
}
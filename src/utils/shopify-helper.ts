import { Client } from "pg";
import {
	GetSecretValueCommand,
	SecretsManagerClient,
} from "@aws-sdk/client-secrets-manager";
import fetch from "node-fetch";
import { WEBAPP_API_URL } from "../events/webhook-worker";
import * as crypto from "crypto"
const jwt = require('jsonwebtoken');

export const SHOPIFY_API_VERSION = '2024-07';

export async function getShopInfoByShopDomain(shopDomain: string): Promise<any> {
	const databaseInfo = await getDatabaseInfo();

	const client = new Client({
		user: databaseInfo.username,
		password: databaseInfo.password,
		host: databaseInfo.host,
		database: databaseInfo.dbname,
		port: databaseInfo.port,
	});

	await client.connect();

	const query = `SELECT "accessToken", "organizationId" FROM "RaleonInfo" WHERE "shopDomain" = $1 LIMIT 1`;
	const values = [shopDomain];

	try {
		const res = await client.query(query, values);
		if (res.rows.length) {
			console.log(`row: ${JSON.stringify(res.rows[0])}`);
			let shopInfo = {
				accessToken: res.rows[0].accessToken,
				orgId: res.rows[0].organizationId,
			};
			return shopInfo;
		} else {
			throw new Error(`No information found for domain: ${shopDomain}`);
		}
	} catch (err) {
		console.error("Error querying database", err);
		throw err;
	} finally {
		await client.end();
	}
}

export async function getShopInfoByOrgId(orgId: string): Promise<any> {
	const databaseInfo = await getDatabaseInfo();

	const client = new Client({
		user: databaseInfo.username,
		password: databaseInfo.password,
		host: databaseInfo.host,
		database: databaseInfo.dbname,
		port: databaseInfo.port,
	});

	await client.connect();

	const query = `SELECT "accessToken", "shopDomain", "email", "organizationId" FROM "RaleonInfo" WHERE "organizationId" = $1 LIMIT 1`;
	const values = [orgId];

	try {
		const res = await client.query(query, values);
		if (res.rows.length) {
			console.log(`row: ${JSON.stringify(res.rows[0])}`);
			let shopInfo = {
				accessToken: res.rows[0].accessToken,
				orgId: res.rows[0].organizationId,
				shopDomain: res.rows[0].shopDomain,
				email: res.rows[0].email,
			};
			return shopInfo;
		} else {
			console.log(`No information found for organization: ${orgId}`);
			return null;
		}
	} catch (err) {
		console.error("Error querying database", err);
		throw err;
	} finally {
		await client.end();
	}
}

export async function fetchShopifyAdmin(domain: string, path: string, method: string): Promise<any> {
	const token = await getShopifyAdminJwt(domain);
	const response = await fetchJSON(
		token,
		`${process.env.SHOPIFY_API_URL}${path}`,
		method,
	);
	console.log("data 👉", JSON.stringify(response, null, 2));
	return response;
}

export async function getShopifyCustomerJwt(domain: string, customerId: string): Promise<string> {
	const secret = await getShopifyAPIKeyAndSecret();
	const key = secret.SHOPIFY_API_KEY;
	const apiSecret = secret.SHOPIFY_API_SECRET;

	const tokenInfo = {
		shopDomain: domain,
		customerId: customerId,
		raleonApi: true,
	};
	return await jwt.sign(tokenInfo, apiSecret, {
		expiresIn: 3600,
	});
}

export async function getShopifyAdminJwt(domain: string): Promise<string> {
	const secret = await getShopifyAPIKeyAndSecret();
	const key = secret.SHOPIFY_API_KEY;
	const apiSecret = secret.SHOPIFY_API_SECRET;

	const tokenInfo = {
		domain,
		clientId: key,
	};
	return await jwt.sign(tokenInfo, apiSecret, {
		expiresIn: 3600,
	});
}

export async function getAuthToken(accessToken: string) {
	try {
		const response = await fetchJSON(
			accessToken,
			`${WEBAPP_API_URL}/create-session`,
			"POST"
		);
		console.log("data 👉", JSON.stringify(response, null, 2));

		if (response?.errors === "Not Found") {
			console.log("No session token found");
			return null;
		}

		if (!response.sessionToken) {
			throw new Error("No session token found");
		}

		const sessionToken = response.sessionToken;

		const loginResponse = await fetchJSON(
			sessionToken,
			`${WEBAPP_API_URL}/users/login/token`,
			"POST"
		);
		console.log("loginData 👉", JSON.stringify(loginResponse, null, 2));

		if (!loginResponse?.token) {
			throw new Error("Failed to retrieve login token");
		}

		return loginResponse.token;
	} catch (error) {
		console.error("getAuthToken error:", error);
		throw error;
	}
}


export async function getAPIaccessToken(shopDomain: string): Promise<any> {
	const databaseInfo = await getDatabaseInfo();

	const client = new Client({
		user: databaseInfo.username,
		password: databaseInfo.password,
		host: databaseInfo.host,
		database: databaseInfo.dbname,
		port: databaseInfo.port,
	});

	await client.connect();

	const query = `SELECT "accessToken" FROM "Session" WHERE "shop" = $1 LIMIT 1`;
	const values = [shopDomain];

	try {
		const res = await client.query(query, values);
		if (res.rows.length) {
			console.log(`row: ${JSON.stringify(res.rows[0])}`);
			return res.rows[0].accessToken;
		} else {
			console.log(
				`No access token found for domain: ${shopDomain}`
			);
			return null;
		}
	} catch (err) {
		console.error("Error querying database", err);
		throw err;
	} finally {
		await client.end();
	}
}

export async function getDatabaseInfo(): Promise<any> {
	const client = new SecretsManagerClient({
		endpoint:
			"https://vpce-060670840daa313c8-mcqyvy4b.secretsmanager.us-east-1.vpce.amazonaws.com",
		region: "us-east-1",
	});
	const command = new GetSecretValueCommand({
		SecretId: process.env.SECRET_ARN,
	});
	const response = await client.send(command);
	return JSON.parse(response.SecretString!);
}

export async function fetchJSON(
	authToken: string,
	url: string,
	method: string,
	body?: string,
	authOverride?: string
): Promise<any> {
	let response;
	const authHeader = authOverride ? `${authOverride} ${authToken}` : `Bearer ${authToken}`;
	const options: any = {
		method,
		headers: {
			"Content-Type": "application/json",
			Authorization: authHeader,
		},
	};
	if (body) options.body = body;
	
	try {
		response = await fetch(url, options);
		if (response.status === 204) {
			return {};
		}
		
		if (!response.ok) {
			const errorText = await response.text();
			console.error(`Request failed with status ${response.status}: ${errorText.substring(0, 500)}`);
			throw new Error(`Request failed with status ${response.status}`);
		}
		
		const contentType = response.headers.get('content-type');
		if (!contentType || !contentType.includes('application/json')) {
			const text = await response.text();
			console.error(`Expected JSON but got ${contentType}: ${text.substring(0, 500)}`);
			throw new Error(`Expected JSON response but got ${contentType}`);
		}
		
		return await response.json();
		
	} catch (e) {
		console.log("fetchJSON error 👉", e);
		throw e;
	}
}


export async function fetchUnauthenticatedJSON(
	url: string,
	method: string,
	body?: string
): Promise<any> {
	let response;
	const options: any = {
		method,
		headers: {
			"Content-Type": "application/json",
		},
	};
	if (body) options.body = body;
	try {
		response = await fetch(url, options);
	} catch (e) {
		console.log("error 👉", e);
	}

	if (response?.status === 204) {
		return {};
	}

	return await response!.json();
}


export async function getShopifyAPIKeyAndSecret() {
	const client = new SecretsManagerClient({
		endpoint: "https://vpce-060670840daa313c8-mcqyvy4b.secretsmanager.us-east-1.vpce.amazonaws.com",
		region: "us-east-1",
	});
	console.log("process.env.SHOPIFY_SECRET_KEY_ARN 👉", process.env.SHOPIFY_SECRET_KEY_ARN);
	const command = new GetSecretValueCommand({
		SecretId: process.env.SHOPIFY_SECRET_KEY_ARN,
	});
	const response = await client.send(command);
	return JSON.parse(response.SecretString!);
}

export async function getShopifyAPISecret() {
	const client = new SecretsManagerClient({
		endpoint: "https://vpce-060670840daa313c8-mcqyvy4b.secretsmanager.us-east-1.vpce.amazonaws.com",
		region: "us-east-1",
	});
	console.log("process.env.SHOPIFY_SECRET_KEY_ARN 👉", process.env.SHOPIFY_SECRET_KEY_ARN);
	const command = new GetSecretValueCommand({
		SecretId: process.env.SHOPIFY_SECRET_KEY_ARN,
	});
	const response = await client.send(command);
	const secret = JSON.parse(response.SecretString!);
	return secret.SHOPIFY_API_SECRET;
}

export async function verifyWebhookRequest(headers: any, body: any) {
	try {
		console.log('verifying hmac for webhook route', body, headers);
		let apiSecret = await getShopifyAPISecret();
		const generatedHash = crypto.createHmac('SHA256', apiSecret).update(body, 'utf8').digest('base64');
		//const hmac = headers.get('X-Shopify-Hmac-Sha256');
		const hmac = headers['X-Shopify-Hmac-Sha256'] || "no hmac";
		console.log('generatedHash', generatedHash);
		console.log('hmac', hmac);

		const safeCompareResult = safeCompare(generatedHash, hmac);

		if (!!safeCompareResult) {
			console.log('hmac verified for webhook route, proceeding');
			return {
				status: true,
				code: 200,
				message: 'Authorized'
			}
		} else {
			console.log('Shopify hmac verification for webhook failed, aborting');
			return {
				status: false,
				code: 401,
				message: 'Not Authorized'
			}
		}
	} catch (error) {
		console.log(error);
		return {
			status: false,
			code: 500,
			message: error
		};
	}
}

export async function getCustomerEmails(
	shopDomain: string,
	accessToken: string,
	customerIds: string[],
	limit: number = 50
) {
	if (!shopDomain || !accessToken || !customerIds) {
		return {
			statusCode: 400,
			body: JSON.stringify({ error: 'Missing required query parameters' }),
		};
	}

	const headers = {
		"X-Shopify-Access-Token": accessToken,
		"Content-Type": "application/json",
	};

	const chunkedList = <T>(arr: T[], chunkSize: number): T[][] => {
		const chunks: T[][] = [];
		for (let i = 0; i < arr.length; i += chunkSize) {
			chunks.push(arr.slice(i, i + chunkSize));
		}
		return chunks;
	};

	let customers: any[] = [];

	for (const idsChunk of chunkedList(customerIds, limit)) {
		const idsParam = idsChunk.join(',');

		try {
			const response = await fetch(
				`https://${shopDomain}/admin/api/${SHOPIFY_API_VERSION}/customers.json?ids=${idsParam}`,
				{ method: 'GET', headers }
			);
			const resp: any = await response.json();
			console.log('response', JSON.stringify(resp));
			customers = customers.concat(resp.customers || []);
		} catch (error: any) {
			console.error('Error fetching data:', error?.response?.status, error?.message);
			return {
				statusCode: error.response?.status || 500,
				body: JSON.stringify({ error: 'Failed to fetch customer data from Shopify' }),
			};
		}
	}

	return {
		statusCode: 200,
		body: JSON.stringify({ customers }),
	};
}

export interface ShopifyCollectionProduct {
    id: string;
}

interface ShopifyCollectionProductsResponse {
    data?: {
        collection?: {
            products: {
                pageInfo: {
                    hasNextPage: boolean;
                    endCursor: string;
                };
                edges: Array<{
                    node: {
                        id: string;
                    };
                }>;
            };
        };
    };
    errors?: Array<{
        message: string;
    }>;
    extensions?: {
        cost?: {
            throttleStatus?: {
                currentlyAvailable: number;
                maximumAvailable: number;
            };
        };
    };
}

export async function getCollectionProducts(
	shopDomain: string,
	accessToken: string,
	collectionId: string,
	maxRetries: number = 5,
	currentAttempt: number = 0,
	after?: string
): Promise<ShopifyCollectionProduct[]> {
	const endpointUrl = `https://${shopDomain}/admin/api/${SHOPIFY_API_VERSION}/graphql.json`;
	
	const query = `
		query getCollectionProducts($id: ID!, $after: String) {
			collection(id: $id) {
				products(first: 250, after: $after) {
					pageInfo {
						hasNextPage
						endCursor
					}
					edges {
						node {
							id
						}
					}
				}
			}
		}
	`;

	const headers = {
		'X-Shopify-Access-Token': accessToken,
		'Content-Type': 'application/json'
	};

	const variables = {
		id: `gid://shopify/Collection/${collectionId}`,
		after: after
	};

	const data = {
		query,
		variables
	};

	if (currentAttempt >= maxRetries) {
		throw new Error(`Exceeded max retries for collection ${collectionId}`);
	}

	try {
		const response = await fetch(endpointUrl, {
			method: 'POST',
			headers: headers,
			body: JSON.stringify(data)
		});

		const responseJson = await response.json() as ShopifyCollectionProductsResponse;
		
		if (responseJson.errors && responseJson.errors.length > 0) {
			console.error('GraphQL errors:', responseJson.errors);
			throw new Error('GraphQL query failed');
		}

		if (!responseJson.data?.collection?.products) {
			throw new Error('Invalid response structure');
		}

		const products = responseJson.data.collection.products;
		let allProducts = products.edges.map((edge: any) => ({
			id: edge.node.id.split('/').pop() // Extract ID from GraphQL ID
		}));

		// Handle pagination
		if (products.pageInfo.hasNextPage) {
			const nextProducts = await getCollectionProducts(
				shopDomain,
				accessToken,
				collectionId,
				maxRetries,
				0,
				products.pageInfo.endCursor
			);
			allProducts = allProducts.concat(nextProducts);
		}

		return allProducts;
	} catch (e) {
		console.error(`Error occurred: ${e}. Attempt ${currentAttempt} of ${maxRetries}`);
		return getCollectionProducts(shopDomain, accessToken, collectionId, maxRetries, currentAttempt + 1, after);
	}
}

export function safeCompare(a: string, b: string): boolean {
	if (a.length !== b.length) {
		return false;
	}

	let result = 0;
	for (let i = 0; i < a.length; i++) {
		result |= a.charCodeAt(i) ^ b.charCodeAt(i);
	}

	return result === 0;
}

export async function removeTagsCustomer(
	shopDomain: string,
	accessToken: string,
	customer: string,
	segments: string[],
	maxRetries: number = 5,
	currentAttempt: number = 0
): Promise<any> {

	const endpointUrl = `https://${shopDomain}/admin/api/2024-07/graphql.json`;
	const customerId = `gid://shopify/Customer/${customer}`;
	const tagsToRemove = segments;

	const mutation = `
		mutation customerTagsRemove($id: ID!, $tags: [String!]!) {
			tagsRemove(id: $id, tags: $tags) {
				userErrors {
					field
					message
				}
				node {
					id
				}
			}
		}
    `;

	const headers = {
		'X-Shopify-Access-Token': accessToken,
		'Content-Type': 'application/json'
	};

	const data = {
		query: mutation,
		variables: {
			id: customerId,
			tags: tagsToRemove
		}
	};

	if (currentAttempt >= maxRetries) {
		throw new Error(`Exceeded max retries for customer ${customer}`);
	}
	try {
		const response = await fetch(endpointUrl, {
			method: 'POST',
			headers: headers,
			body: JSON.stringify(data)
		});

		const responseJson = await response.json();
		const shouldRetry = await checkAPIResponse(responseJson, true);

		if (shouldRetry) {
			await new Promise(resolve => setTimeout(resolve, 1000 * (currentAttempt + 1)));
			return removeTagsCustomer(shopDomain, accessToken, customer, segments, maxRetries, currentAttempt + 1);
		}
	} catch (e) {
		console.error(`Error occurred: ${e}. Attempt ${currentAttempt} of ${maxRetries}`);
		return removeTagsCustomer(shopDomain, accessToken, customer, segments, maxRetries, currentAttempt + 1);
	}
}

export async function addTagsCustomer(
	shopDomain: string,
	accessToken: string,
	customer: string,
	segment: string,
	maxRetries: number = 5,
	currentAttempt: number = 0
): Promise<any> {

	const endpointUrl = `https://${shopDomain}/admin/api/2024-07/graphql.json`;
	const customerId = `gid://shopify/Customer/${customer}`;
	const tagsToAdd = [segment];

	const mutation = `
    mutation customerTagsAdd($id: ID!, $tags: [String!]!) {
        tagsAdd(id: $id, tags: $tags) {
            userErrors {
                field
                message
            }
            node {
                id
            }
        }
    }
    `;

	const headers = {
		'X-Shopify-Access-Token': accessToken,
		'Content-Type': 'application/json'
	};

	const data = {
		query: mutation,
		variables: {
			id: customerId,
			tags: tagsToAdd
		}
	};

	if (currentAttempt >= maxRetries) {
		throw new Error(`Exceeded max retries for customer ${customer}`);
	}

	try {
		const response = await fetch(endpointUrl, {
			method: 'POST',
			headers: headers,
			body: JSON.stringify(data)
		});

		const responseJson = await response.json();
		const shouldRetry = await checkAPIResponse(responseJson, false);

		if (shouldRetry) {
			await new Promise(resolve => setTimeout(resolve, 1000 * (currentAttempt + 1)));
			return addTagsCustomer(shopDomain, accessToken, customer, segment, maxRetries, currentAttempt + 1);
		}

		return responseJson;
	} catch (e) {
		if ((e as any).message.includes)
			console.error(`Error occurred: ${e}. Attempt ${currentAttempt} of ${maxRetries}`);
		return addTagsCustomer(shopDomain, accessToken, customer, segment, maxRetries, currentAttempt + 1);
	}
}

export async function checkAPIResponse(response: any, removingTags: boolean) {
	console.log(JSON.stringify(response));
	const throttleStatus = response.extensions?.cost?.throttleStatus || {};
	const currentlyAvailable = throttleStatus.currentlyAvailable || 0;
	const maximumAvailable = throttleStatus.maximumAvailable || 1000;

	if (currentlyAvailable < 0.25 * maximumAvailable) {
		console.log("Reached 75% of API capacity. Waiting for reset...");
		return true;
	}

	const userErrors = removingTags ?
		response.data?.tagsRemove?.userErrors :
		response.data?.tagsAdd?.userErrors;

	if (userErrors.length > 0) {
		userErrors.forEach((error: any) => {
			console.error(`Error on field ${error.field}: ${error.message}`);
		});
	}
	return false;
}

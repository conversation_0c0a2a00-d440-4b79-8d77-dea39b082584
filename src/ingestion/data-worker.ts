import { APIGatewayProxyResultV2, SQSEvent } from "aws-lambda";
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { ParquetWriter} from '@dsnp/parquetjs';
import { orderSchema, discountSchema, referralSchema } from './order-schema';
import {getAPIaccessToken, getAuthToken, getShopInfoByShopDomain, SHOPIFY_API_VERSION} from "../utils/shopify-helper";
import { queryDatabase, checkActiveLoyaltyOrGWP } from "../utils/raleon-helper";
import fetch from 'node-fetch';
import { FirehoseClient, PutRecordBatchCommand } from "@aws-sdk/client-firehose";
const crypto = require('crypto');

const fs = require('fs');

const s3Client = new S3Client({});

const WEBAPP_API_URL = process.env.WEBAPP_API_URL || 'https://app.raleon.io/api/v1';
const SHOPIFY_ENDPOINT = `https://[domain]/admin/api/${SHOPIFY_API_VERSION}/graphql.json`;

export class Handler {
	accessToken: any;
	orgId: any;
	async main(event: SQSEvent): Promise<APIGatewayProxyResultV2> {
		const messages = event.Records.map((record: any) => {
			const message = JSON.parse(record.body);
			console.log(`Processing message: ${JSON.stringify(message)}`);
			return {
				...message
			};
		});

		for (let message of messages) {
			if(message.raleonIgnoreData) {
				console.log(`Ignoring message`);
				continue;
			}
			let storeInfo = await getShopInfoByShopDomain(message.shopDomain);
			const hasActiveLoyalty = await checkActiveLoyaltyOrGWP(storeInfo.orgId);
			let isCustomer = true;
			if (!hasActiveLoyalty) {
				isCustomer = false;
			} else {
				isCustomer = await this.isCustomer(storeInfo.orgId, message.customer.id);
			}
			await this.saveOrderData(message, isCustomer, storeInfo);
			console.log(`isCustomer: ${isCustomer}`);
			await this.saveDiscountData(message, storeInfo, storeInfo.accessToken, hasActiveLoyalty);
			if(isCustomer) {
				await this.saveReferralData(message, storeInfo);
			}
		}
		
		return {
			statusCode: 200,
			body: JSON.stringify("Orders processed and saved to Parquet!"),
		};
	}
	async saveReferralData(message: any, storeInfo: any) {
		if(message.raleon && message.raleon.referral) {
			let writer = await ParquetWriter.openFile(referralSchema, `/tmp/referral_${message.id}.parquet`);
			await writer!.appendRow({order_id: message.id, referral: true});
			await writer!.close();
			const params = {
				Bucket: process.env.OUTPUT_BUCKET,
				Key: `shopify_order_referrals/organization=${storeInfo.orgId}/${message.id}.parquet`,
				Body: fs.createReadStream(`/tmp/referral_${message.id}.parquet`),
			};

			const command = new PutObjectCommand(params);
			await s3Client.send(command);
			fs.unlinkSync(`/tmp/referral_${message.id}.parquet`);
			const database = process.env.ATHENA_DB || 'ecommerce';
			if (!process.env.OUTPUT_BUCKET) {
				throw new Error(
					"CURATED_OUTPUT_BUCKET environment variable is not set"
				);
			}
			console.log(`referral data saved`);
		}
	}

	private async saveDiscountData(message: any, storeInfo: any, accessTokenToken: any, hasActiveLoyalty: boolean): Promise<void> {
		let writer = null;
		let disounts: { order_id: any; campaign_id: any; discount_code: any; discount_amount: any; discount_type: any; is_non_raleon_discount: boolean; }[] = [];
		let promotionalCampaignId = 0;
		let price = 0;
		let sessionToken = '';
		message.line_items.forEach((item: any) => {
			item.properties.forEach((property: any) => {
				if (property.name === "_campaignId") {
					promotionalCampaignId = property.value;
				}
				if (property.name === "_price") {
					price = property.value;
				}
			});
			if(promotionalCampaignId !== 0) {	
				disounts.push({order_id: message.id, campaign_id: promotionalCampaignId, discount_code: "RALEON_FREE_GIFT", discount_amount: price, discount_type: "free-gift", is_non_raleon_discount: false});
				promotionalCampaignId = 0;
				price = 0;
			}
		});
		if(message.discount_codes && message.discount_codes.length > 0 && hasActiveLoyalty) {
			sessionToken = await getAuthToken(storeInfo.accessToken);	
		}
		for (let discount of message.discount_codes) {
			const matchingDiscount = disounts.find(d => d.discount_amount === (discount.amount || 0));
			if (matchingDiscount) {
				matchingDiscount.discount_code = discount.code;
				continue;
			}
			
			let campaignId = null;
			try {
				if(hasActiveLoyalty) {
					const response = await this.fetch(
						sessionToken,
						`${WEBAPP_API_URL}/inventory-coupons/verify`,
						'POST',
						JSON.stringify({
							discountCode: discount.code,
						})
					);
					console.log(`discount verify response: ${JSON.stringify(response)}`);
					if (response && response.statusCode === 200) { 
						const responseBody = JSON.parse(response.body);
						campaignId = responseBody.loyaltyCampaignId;
						disounts.push({order_id: message.id, campaign_id: campaignId, discount_code: discount.code, discount_amount: discount.amount || 0, discount_type: discount.type, is_non_raleon_discount: false});
					} else {
						disounts.push({order_id: message.id, campaign_id: 0, discount_code: discount.code, discount_amount: discount.amount || 0, discount_type: discount.type, is_non_raleon_discount: true});
					}
				} else {
					disounts.push({order_id: message.id, campaign_id: 0, discount_code: discount.code, discount_amount: discount.amount || 0, discount_type: discount.type, is_non_raleon_discount: true});
				}
			} catch (e) {
				console.error(e);
				disounts.push({order_id: message.id, campaign_id: 0, discount_code: discount.code, discount_amount: discount.amount || 0, discount_type: discount.type, is_non_raleon_discount: true});
			}
		}
		if(disounts.length > 0) {
			writer = await ParquetWriter.openFile(discountSchema, `/tmp/discount_${message.id}.parquet`);
		}
		for(let discount of disounts) {
			console.log(`saving parquet file`);		
			console.log(`message id: ${message.id}`);
			console.log(`discount: ${JSON.stringify(discount)}`);	
			await writer!.appendRow(discount);
		}
		
		if(disounts.length > 0) {
			console.log(`saving discount data`);		
			console.log(`message id: ${message.id}`);
			console.log(`storeInfo: ${JSON.stringify(storeInfo)}`);
			await writer!.close();
			const params = {
				Bucket: process.env.OUTPUT_BUCKET,
				Key: `shopify_order_discounts/organization=${storeInfo.orgId}/discount_${message.id}.parquet`,
				Body: fs.createReadStream(`/tmp/discount_${message.id}.parquet`),
			};

			const command = new PutObjectCommand(params);
			await s3Client.send(command);
			fs.unlinkSync(`/tmp/discount_${message.id}.parquet`);
			const database = process.env.ATHENA_DB || 'ecommerce';
			if (!process.env.OUTPUT_BUCKET) {
				throw new Error(
					"CURATED_OUTPUT_BUCKET environment variable is not set"
				);
			}
			console.log(`discount data saved`);
		}
	}

	private async saveOrderData(message: any, isCustomer: boolean, storeInfo: any): Promise<void> {
		const emailHash = message.contact_email ? crypto.createHash('sha256').update(message.contact_email).digest('hex') : null;
		let totalLoyaltySpend = 0;
		if(message.raleon && message.raleon.totalLoyaltySpend) {
			totalLoyaltySpend = message.raleon.totalLoyaltySpend;
		}
		const relatedData = await this.getRelatedOrderData(message.id, message.shopDomain);
		const riskRecommendation = relatedData?.risk ? relatedData.risk.recommendation : null;

		const commonData = {
			id: message.id,
			email: emailHash,
			currency: message.currency,
			total_price: message.total_price,
			subtotal_price: message.subtotal_price,
			total_discounts: message.total_discounts,
			total_shipping_price: (message.shipping_lines?.[0] && message.shipping_lines[0].price) || "0.00",
			cancel_reason: message.cancel_reason || "",
			created_at: this.convertToMillis(message.created_at),
			cancelled_at: message.cancelled_at ? this.convertToMillis(message.cancelled_at) : null,
			total_refunded: 0,
			member: isCustomer,
			loyalty_spend: totalLoyaltySpend,
			customer: message.customer.id,
			organization: `${storeInfo.orgId}`,
			order_risk_recommendation: riskRecommendation,
		};
		console.log(`order_risk_recommendation: ${riskRecommendation}`);


		console.log(`orgId: ${storeInfo.orgId}`);

		const records: any[] = message.line_items?.map((item: any) => {
			const relatedLineItem = relatedData?.lineItems.edges.find((edge: any) => edge.node.id.includes(item.id));

			console.log(`relatedLineItem: ${JSON.stringify(relatedLineItem)}`);

			const is_subscription = relatedLineItem?.node?.sellingPlan ? true : false;
			const category = relatedLineItem?.node?.variant?.product?.category ? relatedLineItem.node.variant.product.category.name : null;

			console.log(`category: ${category}`);
			console.log(`is_subscription: ${is_subscription}`);


			const data = {
				...commonData,
				item_id: item.product_id,
				item_sku: item.sku || "",
				item_name: item.name,
				item_price: item.variant ? item.variant.price : item.price ? item.price : "0.00",
				item_quantity: item.quantity,
				variant_id: item.variant_id || null,
				product_category: category,
				is_subscription: is_subscription,
			};

			return {
				Data: JSON.stringify(data),
			}
		});

		const chunkSize = 450;
		const chunkedItems = [];
		for (let i = 0; i < records.length; i += chunkSize) {
			chunkedItems.push(records.slice(i, i + chunkSize));
		}

		for (let chunk of chunkedItems) {
			const firehoseClient = new FirehoseClient();
			const params = {
				DeliveryStreamName: process.env.FIREHOSE_DELIVERY_STREAM!,
				Records: chunk,
			};
			await firehoseClient.send(new PutRecordBatchCommand(params));
		}
	}
	private async isCustomer(orgId: any, customerId: string): Promise<boolean> {
		const queryIdentity = `
			SELECT identityvalue 
			FROM raleonuseridentity 
			WHERE identitytype = $1 AND orgid = $2 AND identityvalue = $3 and firstlogindate IS NOT NULL
		`;
		const valuesIdentity = ['customer_id', orgId, customerId];
		console.log(`query: ${queryIdentity}`);
		console.log(`values: ${valuesIdentity}`);

		const queryLoyalty = `
			SELECT 1
			FROM loyaltyprogram
			WHERE orgid = $1 AND active = TRUE AND activationdate IS NOT NULL
		`;
		const valuesLoyalty = [orgId];

		try {
			const resultIdentity = await queryDatabase(queryIdentity, valuesIdentity);
			const resultLoyalty = await queryDatabase(queryLoyalty, valuesLoyalty);

			// Return true only if the customer exists and is part of an active loyalty program
			return resultIdentity.length > 0 && resultLoyalty.length > 0;
		} catch (error) {
			console.error('Error querying customer data:', error);
			throw error;
		}
	}

	
    private async getRelatedOrderData(orderId: string, shopDomain: string): Promise<any> {
		let shopifyURL = SHOPIFY_ENDPOINT.replace(
            "[domain]",
            shopDomain
        );
        console.log(`shopifyURL 👉`, shopifyURL);
        const query = `
			query {
				order(id: "gid://shopify/Order/${orderId}") {
					id
					risk {
						recommendation
					}
					lineItems(first: 250) {
						edges {
							node {
								id
								sellingPlan {
									name
								}
								variant {
									id
									product {
										category {
											name
										}
									}
								}
							}
						}
					}
				}
			}
        `;

        const apitoken = await getAPIaccessToken(shopDomain);
        const response = await fetch(shopifyURL, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "X-Shopify-Access-Token": apitoken,
            },
            body: JSON.stringify({ query }),
        });

        const queryData: any = await response.json();

		console.log(queryData);
		console.log('line items', JSON.stringify(queryData?.data?.order?.lineItems?.edges));

		return queryData?.data?.order;
    }

	
	private convertToMillis(dateString: string): number {
		return new Date(dateString).getTime();
	}

	private async fetch(
		authToken: string,
		url: string,
		method: string,
		body?: string
	): Promise<any> {
		let response;
		const options: any = {
			method,
			headers: {
				"Content-Type": "application/json",
				Authorization: `Bearer ${authToken}`,
				'ngrok-skip-browser-warning': true,
			},
		};
		if (body) options.body = body;
		try {
			response = await fetch(url, options);
		} catch (e) {
			console.log("error 👉", e);
		}

		return await response!.json();
	}
}

export const handler = new Handler();
export const main = handler.main.bind(handler);

import { APIGatewayProxyResultV2, SQSEvent } from "aws-lambda";
import {addAthenaPartitionsOrg, getQueryResults, runAthenaQueryV2, updateAthenaPartitions, waitForQueryExecution} from "../utils/athena-helpers";
const { ParquetWriter} = require("@dsnp/parquetjs");
import { print } from "graphql";
import gql from "graphql-tag";
import { S3Client, PutObjectCommand, GetObjectCommand } from "@aws-sdk/client-s3";
import {getAPIaccessToken, getAuthToken, getShopInfoByOrgId, SHOPIFY_API_VERSION} from "../utils/shopify-helper";
import { discountSchema, orderSchema, refundSchema } from './order-schema';
import { queryDatabase, saveHistoricalDataComplete, checkActiveLoyaltyOrGWP } from "../utils/raleon-helper";
import SQS = require("aws-sdk/clients/sqs");
import { FirehoseClient, PutRecordBatchCommand, PutRecordBatchCommandInput } from "@aws-sdk/client-firehose";
import { WEBAPP_API_URL } from "../events/webhook-worker";

const crypto = require('crypto');
const SHOPIFY_ADMIN_API_ENDPOINT =
	`https://{{DOMAIN}}/admin/api/${SHOPIFY_API_VERSION}/graphql.json`;
const POLLING_INTERVAL = 5000; // 5 seconds
const fs = require('fs');

const s3Client = new S3Client({});
let sqsClient = new SQS();

export class Handler {
	shopInfo: any;
	shopifyToken: string;
	raleonToken: string;
	existingCustomerIds: Set<string>;
	s3Client = new S3Client({});
	firstRun = false;
	hasActiveLoyalty = false;
	async main(event: SQSEvent): Promise<APIGatewayProxyResultV2> {
		console.log(JSON.stringify(event));
		this.existingCustomerIds = new Set();
	
		const messages = event.Records.map((record: any) => {
			const message = JSON.parse(record.body);
			return {
				orgId: message.orgId,
				queryFilter: message.queryFilter || "",
				date: message.date || "",
				incrementDays: message.incrementDays || 7,
			};
		});

		for (let message of messages) {
			try {
				const incrementDays = message.incrementDays ? parseInt(message.incrementDays, 10) : 7;
				if(message.date === "" && message.queryFilter === "") {
					const currentDate = new Date();
					currentDate.setMonth(currentDate.getMonth() - 12); // Set to 12 months ago
					const date12MonthsAgo = currentDate.toISOString().split('T')[0];
					currentDate.setDate(currentDate.getDate() + incrementDays);
					const dateOneWeekLater = currentDate.toISOString().split('T')[0];
					message.queryFilter = `created_at:>'${date12MonthsAgo}' AND created_at:<'${dateOneWeekLater}'`;
					message.date = date12MonthsAgo;
					this.firstRun = true;
				}
				if (message.date !== "" && message.queryFilter === "") {
					try {
						const dateFilter = new Date(message.date);
						if (isNaN(dateFilter.getTime())) {
							throw new Error("Invalid date");
						}

						let dateOneWeekLater = new Date(dateFilter);
						dateOneWeekLater.setDate(dateFilter.getDate() + incrementDays);
						let dateOneWeekLaterString = dateOneWeekLater.toISOString().split('T')[0];
						
						message.queryFilter = `created_at:>'${message.date}' AND created_at:<'${dateOneWeekLaterString}'`;
					} catch (error) {
						console.error("Error processing date:", error);
						return {
							statusCode: 400,
							body: JSON.stringify("Invalid date format."),
						};
					}
					this.firstRun = true;
				}

				const currentRunTime = new Date().toISOString();
				this.shopInfo = await getShopInfoByOrgId(message.orgId);
				if (!this.shopInfo) {
					continue;
				}
				this.shopifyToken = await getAPIaccessToken(this.shopInfo.shopDomain);
				if (!this.shopifyToken) {
					continue;
				}
				await this.loadCustomerIds(this.shopInfo.orgId);
				this.hasActiveLoyalty = await checkActiveLoyaltyOrGWP(this.shopInfo.orgId);
				let bulkDataUrl = await this.initiateBulkOperation(message);
				if(bulkDataUrl === "No URL found") {
					await this.storeLastRunTime(currentRunTime, message.orgId);
					await this.invokeNewLambda(message);
					return {
						statusCode: 200,
						body: JSON.stringify("No Orders to process."),
					};
				}
				await this.processBulkData(bulkDataUrl);
				await this.storeLastRunTime(currentRunTime, message.orgId);
				await this.invokeNewLambda(message);
			}
			catch (error) {
				console.error("Error processing message:", error);
			}
		}
		return {
			statusCode: 200,
			body: JSON.stringify("Orders processed and saved to Parquet!"),
		};
	}

	private async storeLastRunTime(currentRunTime: any, orgId: string) {
		const bucket = process.env.OUTPUT_BUCKET;
		const key = 'shopify_orders/last_run_time.json';
	
		try {
			const getParams = {
				Bucket: bucket,
				Key: key,
			};
			const data = await this.s3Client.send(new GetObjectCommand(getParams));
			const bodyContents = await data.Body?.transformToString();
	
			const existingData = JSON.parse(bodyContents!);
			existingData[orgId] = currentRunTime;
	
			const updatedData = JSON.stringify(existingData);
			const putParams = {
				Bucket: bucket,
				Key: key,
				Body: updatedData,
			};
			await this.s3Client.send(new PutObjectCommand(putParams));
		} catch (error: any) {
			if (error.name === 'NoSuchKey') {
				const newData = JSON.stringify({ [orgId]: currentRunTime });
				const putParams = {
					Bucket: bucket,
					Key: key,
					Body: newData,
				};
				await this.s3Client.send(new PutObjectCommand(putParams));
			} else {
				console.error(error);
			}
		}
	}

	private async checkBulkOperationStatus(operationId: string): Promise<any> {
		const query = gql`
			query {
				currentBulkOperation {
					id
					status
					url
				}
			}
		`;

		const maxRetries = 2;
		let attempt = 1;

		while (attempt <= maxRetries) {
			try {
				const response = await fetch(
					SHOPIFY_ADMIN_API_ENDPOINT.replace("{{DOMAIN}}", this.shopInfo.shopDomain),
					{
						method: "POST",
						headers: {
							"X-Shopify-Access-Token": this.shopifyToken,
							"Content-Type": "application/json",
						},
						body: JSON.stringify({ query: print(query) }),
					}
				);

				const jsonData = await response.json();

				if (jsonData.errors) {
					console.error(`Attempt ${attempt} - Bulk operation error:`, JSON.stringify(jsonData.errors, null, 2));
					if (attempt === maxRetries) {
						throw new Error(`Bulk operation error: ${JSON.stringify(jsonData.errors)}`);
					}
				} else if (!jsonData.data || !jsonData.data.currentBulkOperation) {
					console.error(`Attempt ${attempt} - No currentBulkOperation found in response:`, JSON.stringify(jsonData, null, 2));
					if (attempt === maxRetries) {
						throw new Error("No bulk operation data found in response");
					}
				} else {
					const currentBulkOperation = jsonData.data.currentBulkOperation;
					if (currentBulkOperation.id !== operationId) {
						console.error(`Attempt ${attempt} - Operation ID mismatch:`, {
							expected: operationId,
							received: currentBulkOperation.id,
							fullResponse: JSON.stringify(jsonData, null, 2)
						});
						if (attempt === maxRetries) {
							throw new Error("Bulk operation ID mismatch");
						}
					} else {
						return currentBulkOperation;  // Success! Return the result
					}
				}

				// If we get here, there was an error but we have retries left
				attempt++;
				if (attempt <= maxRetries) {
					console.log(`Retrying in 5 seconds... (Attempt ${attempt} of ${maxRetries})`);
					await new Promise(resolve => setTimeout(resolve, 5000));  // 5 second delay between retries
				}
			} catch (error) {
				console.error(`Attempt ${attempt} - Network or other error:`, error);
				if (attempt === maxRetries) {
					throw error;
				}
				attempt++;
				if (attempt <= maxRetries) {
					console.log(`Retrying in 5 seconds... (Attempt ${attempt} of ${maxRetries})`);
					await new Promise(resolve => setTimeout(resolve, 5000));
				}
			}
		}

		throw new Error("All retry attempts failed");
	}

	private async initiateBulkOperation(
		message: any
	): Promise<string> {
		const date24HoursAgo = new Date(
			Date.now() - 24 * 60 * 60 * 1000
		).toISOString();
		const filter = message.queryFilter;
		const filterQuery = filter ? `(query: "${filter}")` : "";
		const query = gql`
			mutation {
				bulkOperationRunQuery(
					query: """
					{
						orders${filterQuery} {
							edges {
							node {
								id
								createdAt
								email
								totalPrice
								subtotalPrice
								totalDiscounts
								totalShippingPrice
								cancelReason
								cancelledAt
								totalRefunded
								metafield(namespace: "raleonInfo", key: "loyalty_spend") {
									value
								}
								customer {
									id
								}
								risk {
									recommendation
								}
								discountApplications(first: 10) {
								edges {
									node {
									allocationMethod
									targetSelection
									targetType
									value {
										... on MoneyV2 {
											amount
											currencyCode
										}
										... on PricingPercentageValue {
											percentage
										}
									}
									... on DiscountCodeApplication {
										code
									}
									}
								}
								}
								lineItems(first: 250) {
								edges {
									node {
									id
									name
									quantity
									sku
									customAttributes {
										key
										value
									}
									sellingPlan {
										name
									}
									discountAllocations {
										discountApplication {
										allocationMethod
										targetSelection
										targetType
										value {
											... on MoneyV2 {
											amount
											currencyCode
											}
											... on PricingPercentageValue {
											percentage
											}
										}
										... on DiscountCodeApplication {
											code
										}
										}
									}
									variant {
										id
										price
										product {
										id
										category {
											name
										}
										}
									}
									}
								}
								}
							}
							}
						}
					}
					"""
				) {
					bulkOperation {
						id
						status
						objectCount
						url
					}
					userErrors {
						field
						message
					}
				}
			}
		`;
		console.log(
			SHOPIFY_ADMIN_API_ENDPOINT.replace("{{DOMAIN}}", this.shopInfo.shopDomain)
		);
		const response = await fetch(
			SHOPIFY_ADMIN_API_ENDPOINT.replace("{{DOMAIN}}", this.shopInfo.shopDomain),
			{
				method: "POST",
				headers: {
					"X-Shopify-Access-Token": this.shopifyToken,
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ query: print(query) }),
			}
		);

		const jsonData = await response.json();
		console.log(`jsonData 👉`, JSON.stringify(jsonData));
		if(jsonData.errors && (jsonData.errors === "This store is unavailable" || jsonData.errors === "Unavailable Shop" || jsonData.errors === "Not Found" || jsonData.errors === "[API] Invalid API key or access token (unrecognized login or wrong password)")) {
			console.log("Shop is unavailable for organization: ", message.orgId);
			return "No URL found";
		}
		const bulkOperation = jsonData.data.bulkOperationRunQuery.bulkOperation;

		const operationId = bulkOperation.id;

		// Poll for completion status
		let currentBulkOperation = { status: "Not Finished", url: "" };
		while (currentBulkOperation.status !== "COMPLETED") {
			await new Promise((resolve) =>
				setTimeout(resolve, POLLING_INTERVAL)
			);
			currentBulkOperation = await this.checkBulkOperationStatus(
				operationId
			);
		}
		console.log(
			"currentBulkOperation" + JSON.stringify(currentBulkOperation)
		);
		if (!currentBulkOperation.url || currentBulkOperation.url === "") {
			return "No URL found";
		}
		return currentBulkOperation.url;
	}


	private async processBulkData(bulkDataUrl: string) {
		const response = await fetch(bulkDataUrl, {
			method: "GET",
			headers: {
				"X-Shopify-Access-Token": this.shopifyToken,
			},
		});
		const reader = response.body?.getReader();
		let completed = false;
		let accumulatedText = "";
		let currentOrder: any = null;
		let currentItems: any[] = [];
		let currentDiscounts: any[] = [];
		let toProcess = [];
		let orderIds = [];
	
		while (!completed) {
			const { done, value } = await reader!.read();
			completed = done;
	
			accumulatedText += new TextDecoder("utf-8").decode(value || new Uint8Array());
			let lastIndex = accumulatedText.lastIndexOf("\n");
			const fullLines = accumulatedText.substring(0, lastIndex).split("\n");
			accumulatedText = accumulatedText.substring(lastIndex + 1);
	
			for (const line of fullLines) {
				if (line.trim() === "") {
					continue;
				}
				const item = JSON.parse(line);
				if ("id" in item && "totalPrice" in item) {
					if (currentOrder) {
						toProcess.push({ order: currentOrder, items: currentItems, discounts: currentDiscounts });
						currentOrder = null;
						currentItems = [];
						currentDiscounts = [];
					}
					currentOrder = item;
					const id = item.id.match(/\d+/)?.[0]!;
					orderIds.push(parseInt(id));
				} else if ("name" in item && "__parentId" in item) {
					currentItems.push(item);
				}
				else if ("allocationMethod" in item) {
					currentDiscounts.push(item);
				}
			}
		}
		if (currentOrder) {
			toProcess.push({ order: currentOrder, items: currentItems, discounts: currentDiscounts });
		}
		let refunds = [];
		if(this.firstRun) {
			for (const { order, items, discounts } of toProcess) {
				const id: string = order.id.match(/\d+/)?.[0];
				if (id) {
					await this.processAndSaveOrder(order, items, discounts);
				}
				if(order.totalRefunded > 0) {
					refunds.push({order_id: order.id.match(/\d+/)[0], refund_amount: order.totalRefunded});
				}
			}
		}
		else {
			const existingOrderIds: any[] = await this.getExistingOrders(orderIds, this.shopInfo.orgId);
		
			for (const { order, items, discounts } of toProcess) {
				const id: string = order.id.match(/\d+/)?.[0];
				if (id && !existingOrderIds.includes(id)) {
					await this.processAndSaveOrder(order, items, discounts);
				}
				if(order.totalRefunded > 0) {
					refunds.push({order_id: order.id.match(/\d+/)[0], refund_amount: order.totalRefunded});
				}
			}
		}
		if(refunds.length > 0) {
			await this.saveRefundData(refunds, this.shopInfo);
		}
	}

	private async getExistingOrders(orderIds: number[], orgId: string) {
		let athenaQuery = `
			SELECT id FROM  "${process.env.ATHENA_DB}"."orders"
			WHERE organization = '${orgId}'
			AND id IN (${orderIds.join(",")})
		`;

		const queryExecution = await runAthenaQueryV2(
			athenaQuery,
			process.env.ATHENA_OUTPUT_BUCKET!
		);

		const queryId = queryExecution.QueryExecutionId;
		if (!queryId) {
			throw new Error("Failed to start Athena query");
		}

		const succeeded = await waitForQueryExecution(queryId);
		if (!succeeded) {
			throw new Error("Athena query did not succeed");
		}

		const existingOrderIds: any[] = [];
		const queryResults = await getQueryResults(queryId);
		if (queryResults.ResultSet != null && queryResults.ResultSet.Rows != null) {
			const rows = queryResults.ResultSet.Rows;
			if (rows.length > 1) {
				rows.slice(1).forEach((row) => {
					const data: any = row.Data;
					const existingId = data[0].VarCharValue;
					if (!existingOrderIds.includes(existingId)) {
						existingOrderIds.push(existingId);
					}
				});
			} else {
				console.log("No query results returned");
				return [];
			}
		} else {
			console.log("No query results returned");
		}
		return existingOrderIds;
	}


	private async processAndSaveOrder(order: any, items: any[], discounts: any[] = []) {
		const discount_codes = [];

		if (items.length === 0) {
			return;
		}
		if (order.customer == null) {
			order.customer = { id: "0" };
		}
		let loyaltySpend = '0.00';
		if (order.metafield && order.metafield.value) {
			try {
				const metafieldValue = JSON.parse(order.metafield.value);
				loyaltySpend = metafieldValue.amount;
			} catch (error) {
				console.error("Error parsing metafield value:", error);
			}
		}
		const commonData: { [key: string]: any } = {
			id: order.id.match(/\d+/)[0],
			email: order.email == null ? "" : crypto.createHash('sha256').update(order.email).digest('hex'),
			currency: "USD",
			total_price: order.totalPrice,
			subtotal_price: order.subtotalPrice,
			total_discounts: order.totalDiscounts,
			total_shipping_price: order.totalShippingPrice,
			cancel_reason: order.cancelReason || "",
			created_at: this.convertToMillis(order.createdAt),
			cancelled_at: order.cancelledAt ? this.convertToMillis(order.cancelledAt) : null,
			total_refunded: order.totalRefunded,
			member: this.existingCustomerIds.has(order.customer.id.match(/\d+/)[0]),
			loyalty_spend: loyaltySpend,
			customer: order.customer.id.match(/\d+/)[0],
			organization: `${this.shopInfo.orgId}`,
			order_risk_recommendation: order.risk ? order.risk.recommendation : "",
		};

		for (const discountAllocation of discounts) {
			let discountType = 'fixed_amount'; // default type
			let amount = "0.00";
		
			if (discountAllocation?.targetType === 'SHIPPING_LINE') {
				discountType = 'free_shipping';
			} else if (discountAllocation?.value?.percentage) {
				discountType = 'percentage';
				amount = discountAllocation.value.percentage;
			} else if (discountAllocation?.value?.amount) {
				discountType = 'fixed_amount';
				amount = discountAllocation.value.amount;
			} else if (discountAllocation?.allocationMethod && discountAllocation?.code) {
				// Specific handling for DiscountCodeApplication if above fields don't exist
				discountType = 'fixed_amount';
				amount = discountAllocation.value?.amount || "0.00";
			}
		
			discount_codes.push({
				code: discountAllocation?.code || "",
				amount: amount,
				type: discountType,
			});
		}
		
	
		const records: any[] = items.map((item: any) => {
			const data = {
				...commonData,
				item_id: item.variant?.product?.id ? item.variant.product.id.match(/\d+/)[0] : null,
				item_sku: item.sku || "",
				item_name: item.name,
				item_price: item.variant ? item.variant.price : item.price ? item.price : "0.00",
				item_quantity: item.quantity,
				variant_id: item.variant && item.variant.id ? item.variant.id.match(/\d+/)[0] : null,
				product_category: item.variant?.product?.category?.name || "",
				is_subscription: item.sellingPlan ? true : false,
			};
			item.customAttributes.forEach((property: any) => {
				if (property.key === "_campaignId") {
					discount_codes.push({order_id: commonData.id, campaign_id: property.value, discount_code: "RALEON_FREE_GIFT", discount_amount: data.item_price, discount_type: "free-gift", is_non_raleon_discount: false});
				}
			});
			return {
				Data: JSON.stringify(data),
			};
		});

		this.saveDiscountData(commonData.id, discount_codes, this.shopInfo).catch((error) => {
			console.error("Error saving discount data:", error);
		});
	
		const chunkSize = 450;
		const chunkedItems = [];
		for (let i = 0; i < records.length; i += chunkSize) {
			chunkedItems.push(records.slice(i, i + chunkSize));
		}
	
		for (let chunk of chunkedItems) {
			const firehoseClient = new FirehoseClient();
			const params = {
				DeliveryStreamName: process.env.FIREHOSE_DELIVERY_STREAM!,
				Records: chunk,
			};
			await firehoseClient.send(new PutRecordBatchCommand(params));
		}
	
		return { records: items };
	}

	private async saveRefundData(refunds: Array<any>, storeInfo: any): Promise<void> {
		let writer = null;
		const timestamp = new Date().toISOString().replace(/[-:T]/g, '').slice(0, 15);
		if(refunds.length > 0) {
			writer = await ParquetWriter.openFile(refundSchema, `/tmp/refund_${timestamp}.parquet`);
		}
		for(let refund of refunds) {
			await writer!.appendRow(refund);
		}
		
		if(refunds.length > 0) {
			await writer!.close();
			
			const params = {
				Bucket: process.env.OUTPUT_BUCKET,
				Key: `shopify_order_refunds/organization=${storeInfo.orgId}/refund_${timestamp}.parquet`,
				Body: fs.createReadStream(`/tmp/refund_${timestamp}.parquet`),
			};

			const command = new PutObjectCommand(params);
			await s3Client.send(command);
			fs.unlinkSync(`/tmp/refund_${timestamp}.parquet`);
			const database = process.env.ATHENA_DB || 'ecommerce';
			if (!process.env.OUTPUT_BUCKET) {
				throw new Error(
					"CURATED_OUTPUT_BUCKET environment variable is not set"
				);
			}
		}
	}


	private async saveDiscountData(order_id: string, discount_codes: Array<any>, storeInfo: any): Promise<void> {
		let writer = null;
		let disounts = [];
		for (let discount of discount_codes) {
			if(this.firstRun) {
				//first run dont verify discount codes
				disounts.push({order_id, campaign_id: 0, discount_code: discount.code, discount_amount: discount.amount || 0, discount_type: discount.type, is_non_raleon_discount: true});
				continue;
			}
			let campaignId = null;
			try {
				if(this.hasActiveLoyalty) {
					if(this.raleonToken === undefined) {
						this.raleonToken = await getAuthToken(this.shopInfo.accessToken);
						if (!this.raleonToken) {
							throw new Error("Failed to get Raleon token");
						}
					}
					const response = await this.fetch(
						this.raleonToken,
						`${WEBAPP_API_URL}/inventory-coupons/verify`,
						'POST',
						JSON.stringify({
							discountCode: discount.code,
						})
					);
					if (response && response.statusCode === 200) { 
						const responseBody = JSON.parse(response.body);
						campaignId = responseBody.loyaltyCampaignId;
						disounts.push({order_id, campaign_id: campaignId, discount_code: discount.code, discount_amount: discount.amount || 0, discount_type: discount.type, is_non_raleon_discount: false});
					} else {
						disounts.push({order_id, campaign_id: 0, discount_code: discount.code, discount_amount: discount.amount || 0, discount_type: discount.type, is_non_raleon_discount: true});
					}
				} else {
					disounts.push({order_id, campaign_id: 0, discount_code: discount.code, discount_amount: discount.amount || 0, discount_type: discount.type, is_non_raleon_discount: true});
				}
			} catch (e) {
				console.log(e);
				disounts.push({order_id, campaign_id: 0, discount_code: discount.code, discount_amount: discount.amount || 0, discount_type: discount.type, is_non_raleon_discount: true});
			}
		}
		if(disounts.length > 0) {
			writer = await ParquetWriter.openFile(discountSchema, `/tmp/discount_${order_id}.parquet`);
		}
		for(let discount of disounts) {
			await writer!.appendRow(discount);
		}
		
		if(disounts.length > 0) {
			await writer!.close();
			const params = {
				Bucket: process.env.OUTPUT_BUCKET,
				Key: `shopify_order_discounts/organization=${storeInfo.orgId}/discount_${order_id}.parquet`,
				Body: fs.createReadStream(`/tmp/discount_${order_id}.parquet`),
			};

			const command = new PutObjectCommand(params);
			await s3Client.send(command);
			fs.unlinkSync(`/tmp/discount_${order_id}.parquet`);
			const database = process.env.ATHENA_DB || 'ecommerce';
			if (!process.env.OUTPUT_BUCKET) {
				throw new Error(
					"CURATED_OUTPUT_BUCKET environment variable is not set"
				);
			}
		}
	}


	private async invokeNewLambda(message: any) {
		const incrementDays = message.incrementDays ? parseInt(message.incrementDays, 10) : 7;
		if (isNaN(Date.parse(message.date))) {
			return;
		}
		let sqsClient = new SQS();
		const dateFilter = new Date(message.date);
		let dateOneWeekLater = new Date(dateFilter);
		dateOneWeekLater.setDate(dateFilter.getDate() + incrementDays);
		const currentDate = new Date();
		if (dateOneWeekLater > currentDate) {
			const database = process.env.ATHENA_DB || 'ecommerce';
			await addAthenaPartitionsOrg(
				`${database}.orders`,
				message.orgId,
				'shopify_orders',
				process.env.OUTPUT_BUCKET!,
				process.env.ATHENA_OUTPUT_BUCKET!
			);
			await addAthenaPartitionsOrg(
				`${database}.order_discounts`,
				message.orgId,
				'shopify_order_discounts',
				process.env.OUTPUT_BUCKET!,
				process.env.ATHENA_OUTPUT_BUCKET!
			);
			await addAthenaPartitionsOrg(
				`${database}.order_refunds`,
				message.orgId,
				'shopify_order_refunds',
				process.env.OUTPUT_BUCKET!,
				process.env.ATHENA_OUTPUT_BUCKET!
			);
			//kick off metrics lambda
			await saveHistoricalDataComplete(message.orgId);
			await this.invokeMetrics(message);
			return;
		}
		let dateOneWeekLaterString = dateOneWeekLater.toISOString().split('T')[0];
		let newInstanceData = {
			orgId: message.orgId,
			queryFilter: "",
			date: dateOneWeekLaterString,
			incrementDays: incrementDays,
		};

		await sqsClient.sendMessage({
			QueueUrl: process.env.QUEUE_URL as string,
			MessageBody: JSON.stringify(newInstanceData),
		}).promise();
	}

	private async loadCustomerIds(orgId: any) {
		const loyaltyProgramQuery = `
			SELECT 1 
			FROM loyaltyprogram 
			WHERE orgid = $1 AND active = TRUE AND activationdate IS NOT NULL
		`;
		const loyaltyProgramValues = [orgId];

		try {
			const loyaltyProgramResult = await queryDatabase(loyaltyProgramQuery, loyaltyProgramValues);

			if (loyaltyProgramResult.length > 0 && !this.firstRun) {
				const customerQuery = `
					SELECT identityvalue 
					FROM raleonuseridentity 
					WHERE identitytype = $1 AND orgid = $2 AND firstlogindate IS NOT NULL
				`;
				const customerValues = ['customer_id', orgId];

				const customerResult = await queryDatabase(customerQuery, customerValues);
				customerResult.forEach(row => this.existingCustomerIds.add(row.identityvalue));
			}
		} catch (error) {
			console.error('Error querying data:', error);
			throw error;
		}
	}

	private async invokeMetrics(message: any) {
		const metricsMessage = {
            orgId: message.orgId,
            query: "",
            variables: null,
            segment_query: null,
            segment_variables: null,
            queryOverride: "",
            variableOverride: "",
            catalog: "",
            name: "excluded_customers",
            type: "excluded-customers",
            segment: null,
            dataStructure: "",
            lastRunDate: null,
            activationDate: "",
            priority: 1,
            fieldMappings: ""
        };

        await sqsClient.sendMessage({
            QueueUrl: process.env.METRIC_QUEUE_URL as string,
            MessageBody: JSON.stringify(metricsMessage)
        }).promise();
        
        console.log('Successfully sent message to metrics queue');
        return;
    }

	private convertToMillis(dateString: string): number {
		return new Date(dateString).getTime();
	}
	


	private async fetch(
		authToken: string,
		url: string,
		method: string,
		body?: string
	): Promise<any> {
		let response;
		const options: any = {
			method,
			headers: {
				"Content-Type": "application/json",
				Authorization: `Bearer ${authToken}`,
				'ngrok-skip-browser-warning': true,
			},
		};
		if (body) options.body = body;
		try {
			response = await fetch(url, options);
		} catch (e) {
			console.log("error 👉", e);
		}

		return await response!.json();
	}
}

export const handler = new Handler();
export const main = handler.main.bind(handler);

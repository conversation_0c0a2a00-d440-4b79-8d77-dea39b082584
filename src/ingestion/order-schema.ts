import { ParquetSchema } from '@dsnp/parquetjs';

export const orderSchema = new ParquetSchema({
	id: { type: "INT64" },
	email: { type: "UTF8" },
	currency: { type: "UTF8" },
	total_price: { type: "DOUBLE" },
	subtotal_price: { type: "DOUBLE" },
	total_discounts: { type: "DOUBLE" },
	total_shipping_price: { type: "DOUBLE" },
	item_id: { type: "INT64" },
	item_name: { type: "UTF8" },
	item_price: { type: "DOUBLE" },
	item_quantity: { type: "INT32" },
	item_sku: { type: "UTF8" },
	created_at: { type: "TIMESTAMP_MILLIS" },
	cancel_reason: { type: "UTF8" },
	cancelled_at: { type: "TIMESTAMP_MILLIS", optional: true},
	total_refunded: { type: "DOUBLE" },
	member: {type: "BOOLEAN", optional: false},
	loyalty_spend: {type: "DOUBLE", optional: true},
	customer: {type: "UTF8", optional: false},
});

export const discountSchema = new ParquetSchema({
	order_id: { type: "INT64" },
	campaign_id: { type: "INT64" },
	discount_code: { type: "UTF8", optional: true },
	discount_amount: { type: "DOUBLE" },
	discount_type: { type: "UTF8", optional: true },
	is_non_raleon_discount: { type: "BOOLEAN" },
});

export const referralSchema = new ParquetSchema({
	order_id: { type: "INT64" },
	referral: { type: "BOOLEAN" }
});

export const refundSchema = new ParquetSchema({
	order_id: { type: "INT64" },
	refund_amount: { type: "DOUBLE" }
});

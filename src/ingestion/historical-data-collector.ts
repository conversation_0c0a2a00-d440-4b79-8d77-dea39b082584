import { APIGatewayProxyResultV2 } from "aws-lambda";
import { print } from "graphql";
import gql from "graphql-tag";
import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import { DynamoDBDocumentClient, PutCommand } from "@aws-sdk/lib-dynamodb";
import { getAPIaccessToken, getShopInfoByOrgId, SHOPIFY_API_VERSION } from "../utils/shopify-helper";
import { SFNClient, SendTaskSuccessCommand } from "@aws-sdk/client-sfn";


interface HistoricalDataEvent {
    orgId: string;
    taskToken: string;
}

const SHOPIFY_ADMIN_API_ENDPOINT = `https://{{DOMAIN}}/admin/api/${SHOPIFY_API_VERSION}/graphql.json`;
const ddbClient = new DynamoDBClient({});
const ddbDocClient = DynamoDBDocumentClient.from(ddbClient);
const sfnClient = new SFNClient({});

export class Handler {
    shopInfo: any;
    shopifyToken: string;

    async main(event: HistoricalDataEvent): Promise<APIGatewayProxyResultV2> {
        console.log('Historical data collector event:', JSON.stringify(event));
        
        try {
            // Get organization info
            this.shopInfo = await getShopInfoByOrgId(event.orgId);
            if (!this.shopInfo) {
                await sfnClient.send(new SendTaskSuccessCommand({
                        taskToken: event.taskToken,
                        output: JSON.stringify({
                            errorMessage: 'Shop info not found',
                            taskToken: event.taskToken,
                            status: 'COMPLETED',
                            s3Location: '',
                            bulkOperationId: '',  // Use extracted ID
                            organizationId: String(event.orgId)  // Ensure organizationId is a string
                        })
                    }));
                    console.log('Successfully notified Step Function of completion');
                return {
                    statusCode: 200,
                    body: JSON.stringify({
                        errorMessage: 'Shop info not found',
                        taskToken: event.taskToken,
                        orgId: event.orgId
                    })
                };
            }

            // Get Shopify access token
            this.shopifyToken = await getAPIaccessToken(this.shopInfo.shopDomain);
            if (!this.shopifyToken) {
                throw new Error('Failed to get Shopify token');
            }

            // Calculate date range for last year
            const currentDate = new Date();
            const oneYearAgo = new Date();
            oneYearAgo.setFullYear(currentDate.getFullYear() - 1);

            // Create date filter
            const dateFilter = `created_at:>'${oneYearAgo.toISOString()}' AND created_at:<'${currentDate.toISOString()}'`;
            
            // Initialize bulk operation
            const operationId = await this.initiateBulkOperation(dateFilter, event.taskToken);
            
            if (!operationId) {
                return {
                    statusCode: 200,
                    body: JSON.stringify({ message: "No orders to process" })
                };
            }

            // Return the operation ID and taskToken
            return {
                statusCode: 200,
                body: JSON.stringify({
                    bulkOperationId: operationId,
                    taskToken: event.taskToken,
                    orgId: event.orgId
                })
            };

        } catch (error) {
            console.error('Error in historical data collection:', error);
            throw error;
        }
    }

    private async initiateBulkOperation(filter: string, taskToken: string): Promise<string> {
        const query = gql`
            mutation {
                bulkOperationRunQuery(
                    query: """
                    {
                        orders(query: "${filter}") {
                            edges {
                                node {
                                    id
                                    createdAt
                                    email
                                    totalPrice
                                    subtotalPrice
                                    totalDiscounts
                                    totalShippingPrice
                                    cancelReason
                                    cancelledAt
                                    totalRefunded
                                    metafield(namespace: "raleonInfo", key: "loyalty_spend") {
                                        value
                                    }
                                    customer {
                                        id
                                    }
                                    risk {
                                        recommendation
                                    }
                                    discountApplications(first: 10) {
                                        edges {
                                            node {
                                                allocationMethod
                                                targetSelection
                                                targetType
                                                value {
                                                    ... on MoneyV2 {
                                                        amount
                                                        currencyCode
                                                    }
                                                    ... on PricingPercentageValue {
                                                        percentage
                                                    }
                                                }
                                                ... on DiscountCodeApplication {
                                                    code
                                                }
                                            }
                                        }
                                    }
                                    lineItems(first: 250) {
                                        edges {
                                            node {
                                                id
                                                name
                                                quantity
                                                sku
                                                customAttributes {
                                                    key
                                                    value
                                                }
                                                sellingPlan {
                                                    name
                                                }
                                                discountAllocations {
                                                    discountApplication {
                                                        allocationMethod
                                                        targetSelection
                                                        targetType
                                                        value {
                                                            ... on MoneyV2 {
                                                                amount
                                                                currencyCode
                                                            }
                                                            ... on PricingPercentageValue {
                                                                percentage
                                                            }
                                                        }
                                                        ... on DiscountCodeApplication {
                                                            code
                                                        }
                                                    }
                                                }
                                                variant {
                                                    id
                                                    price
                                                    product {
                                                        id
                                                        category {
                                                            name
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    """
                ) {
                    bulkOperation {
                        id
                        status
                        objectCount
                        url
                    }
                    userErrors {
                        field
                        message
                    }
                }
            }
        `;

        console.log('Initiating bulk operation with Shopify API:', 
            SHOPIFY_ADMIN_API_ENDPOINT.replace("{{DOMAIN}}", this.shopInfo.shopDomain));

        const response = await fetch(
            SHOPIFY_ADMIN_API_ENDPOINT.replace("{{DOMAIN}}", this.shopInfo.shopDomain),
            {
                method: "POST",
                headers: {
                    "X-Shopify-Access-Token": this.shopifyToken,
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({ query: print(query) }),
            }
        );

        const jsonData = await response.json();
        console.log('Bulk operation response:', JSON.stringify(jsonData));

        if (jsonData.errors && (jsonData.errors === "Unavailable Shop" || 
            jsonData.errors === "Not Found" || 
            jsonData.errors === "[API] Invalid API key or access token (unrecognized login or wrong password)")) {
            console.log("Shop is unavailable for organization:", this.shopInfo.orgId);
            return "";
        }

        const bulkOperation = jsonData.data.bulkOperationRunQuery.bulkOperation;
        const operationId = bulkOperation.id;

        // Save to DynamoDB with taskToken
        await ddbDocClient.send(new PutCommand({
            TableName: process.env.BULK_OPERATIONS_TABLE!,
            Item: {
                bulkOperationId: operationId,
                organizationId: String(this.shopInfo.orgId),
                taskToken: taskToken,
                status: 'PENDING',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            }
        }));

        return operationId;
    }
}

export const handler = new Handler();
export const main = handler.main.bind(handler);
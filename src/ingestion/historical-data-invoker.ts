import { GetObjectCommand, PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { APIGatewayProxyResultV2 } from 'aws-lambda';
import { getOrgsWithActivePlan } from '../utils/raleon-helper';
import { SQS } from 'aws-sdk';

export class Handler {
	s3Client = new S3Client({ region: 'us-east-1' });
	sqsClient = new SQS({ region: 'us-east-1' });
	async main(event: any): Promise<APIGatewayProxyResultV2> {
		const orgsLastRunTimes: any = await this.getOrgsLastRunTimes();
		if (orgsLastRunTimes.statusCode) {
			return orgsLastRunTimes;
		}
	
		const activeOrgs = await getOrgsWithActivePlan();
		const activeOrgIds = new Set(activeOrgs.map((org: any) => org.orgid));
		console.log(`activeOrgIds: ${activeOrgIds}`);
		const now = new Date();
		const minutesAgo = new Date(now.getTime() - 15 * 60 * 1000);
		const cutoffTime = minutesAgo.toISOString();
	
		const orgs = Object.keys(orgsLastRunTimes).filter(org => activeOrgIds.has(Number(org)));
		console.log(`orgs: ${orgs}`);
		for (const org of orgs) {
			const lastRunTime = orgsLastRunTimes[org];
			const params = {
				MessageBody: JSON.stringify({
					orgId: org,
					queryFilter: `created_at:>'${lastRunTime}' AND created_at:<'${cutoffTime}'`
				}),
				QueueUrl: process.env.QUEUE_URL!,
			};
			await this.sqsClient.sendMessage(params).promise();
			console.log(`message sent for ${org}`);
		}
	
		return {
			statusCode: 200,
			body: JSON.stringify('Finished sending messages to process historical data.'),
		};
	}
	

	private async getOrgsLastRunTimes(): Promise<any> {
		try {
			const data = await this.s3Client.send(new GetObjectCommand({
				Bucket: process.env.OUTPUT_BUCKET,
				Key: 'shopify_orders/last_run_time.json',
			}));
			const bodyContents = await data.Body?.transformToString();
			if (!bodyContents) {
				return {
					statusCode: 404,
					body: JSON.stringify("No orgs configured."),
				};
			}
			return JSON.parse(bodyContents);
		} catch (error: any) {
			console.error(error);
			return {
				statusCode: 500,
				body: JSON.stringify(error),
			}
		}
	}
}


export const handler = new Handler();
export const main = handler.main.bind(handler);
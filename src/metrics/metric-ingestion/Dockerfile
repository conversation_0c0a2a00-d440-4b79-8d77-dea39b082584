FROM python:3.12-slim

# Install system-level dependencies and certificates
RUN apt-get update && \
    apt-get install -y gcc g++ make libffi-dev libssl-dev libpq-dev ca-certificates && \
    update-ca-certificates && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Set work directory
WORKDIR /app

COPY requirements.txt .
COPY . .

RUN pip install --upgrade pip setuptools wheel && \
    pip install -r requirements.txt

ENV NUMBA_CACHE_DIR="/tmp/numba_cache"
ENV REQUESTS_CA_BUNDLE=/etc/ssl/certs/ca-certificates.crt
ENV SSL_CERT_FILE=/etc/ssl/certs/ca-certificates.crt

ENTRYPOINT ["python", "-c", "import handler; handler.main()"]

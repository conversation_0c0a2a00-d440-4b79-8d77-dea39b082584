import aiohttp
import asyncio
import boto3
import json
from typing import List, Dict, Any
import os
from datetime import datetime
import pandas as pd
import athena_helper
from shopify_helper import bulk_update_metafields, get_shop_info_by_org_id, get_api_access_token
from raleon_helper import query_database

class AthenaWorkGroup:
    METRIC = 'metric'
    ADHOC = 'adhoc'

class ExcludedOrders:
    def __init__(self):
        self.s3_client = boto3.client('s3')

    def getData(self, message):
        self.message = message
        shop_info = get_shop_info_by_org_id(self.message['orgId'])
        if not shop_info:
            return
        sessionToken = get_api_access_token(shop_info['shopDomain'])
        if not sessionToken:
            return
        return asyncio.run(self.load_excluded_orders(self.message['orgId'], shop_info['shopDomain'], sessionToken))
        
    def processData(self, data):
        return data
    
    def writeData(self, data):
        if not data:
            print("No data to write.")
            return
        self.write_metrics_data_to_s3(data, self.message)
    
    def write_metrics_data_to_s3(self, metrics_data, message):
        rundate = datetime.now()
        metrics_data = [{'orderId': int(oid), 'runDate': rundate} for oid in metrics_data]
        df = pd.DataFrame(metrics_data)
        df['runDate'] = (df['runDate'].astype(int) / 1e6).astype('int64')
        df['orderId'] = df['orderId'].astype('int64')
        
        # Path where the files will be stored in S3
        s3_path = f"{message['name']}/organization={message['orgId']}/"
        bucket_name = os.environ["CURATED_BUCKET"]

        # Delete existing files in the specified partition
        self.delete_existing_files(bucket_name, s3_path)

        # Save new file and upload
        file_path = f"/tmp/{message['orgId']}_{message['name']}.parquet"
        df.to_parquet(file_path)
        timestamp = self.generate_timestamp()
        s3_key = f"{s3_path}{message['name']}_{timestamp}.parquet"
        self.s3_client.upload_file(file_path, bucket_name, s3_key)
        print(f"Uploaded new file to {bucket_name}/{s3_key}")

    def delete_existing_files(self, bucket, path):
        # List all objects in the specified S3 path
        response = self.s3_client.list_objects_v2(Bucket=bucket, Prefix=path)
        if 'Contents' in response:
            for obj in response['Contents']:
                print(f"Deleting {obj['Key']} from {bucket}")
                self.s3_client.delete_object(Bucket=bucket, Key=obj['Key'])

    async def load_excluded_orders(self, org_id, shop_domain, access_token):
        excluded_tags = set()

        try:
            order_query = "SELECT orderexclusiontags FROM loyaltyprogram WHERE orgid = %s"
            rows = query_database(order_query, (org_id,))
            for row in rows:
                # Assuming exclusiontags is the first column in the result
                exclusion_tags_string = row[0]
                if exclusion_tags_string:  # Ensure there's something to split
                    excluded_tags.update(exclusion_tags_string.split(','))
        except Exception as error:
            print('Error querying exclusion tags:', error)
            raise

        if not excluded_tags:
            return None
        tag_query = ""

        for tag in excluded_tags:
            print(f'Excluded tag: {tag}')
            escaped_tag = tag.replace('"', '\\"')
            tag_query += f'tag:"{escaped_tag}" OR '

        if tag_query.endswith(" OR "):
            tag_query = tag_query[:-3]
        order_ids = set()
        has_next_page = True
        cursor = None

        async with aiohttp.ClientSession() as session:
            while has_next_page:
                query = f"""
                query getOrders($tagQuery: String!, $cursor: String) {{
                    orders(first: 250, query: $tagQuery, after: $cursor) {{
                        edges {{
                            node {{
                                id
                            }}
                            cursor
                        }}
                        pageInfo {{
                            hasNextPage
                        }}
                    }}
                }}
                """
                variables = {'tagQuery': tag_query, 'cursor': cursor}
                headers = {
                    "X-Shopify-Access-Token": access_token,
                    "Content-Type": "application/json",
                }
                json_data = {'query': query, 'variables': variables}
                print(f'json_data 👉 {json.dumps(json_data)}')
                try:
                    response = await session.post(f"https://{shop_domain}/admin/api/2025-01/graphql.json", headers=headers, json=json_data)
                    json_response = await response.json()
                    print(f'jsonData 👉 {json.dumps(json_response)}')
                    if 'errors' in json_response:
                        print(f"Error fetching customers from Shopify: {json_response['errors']}")
                        return set()
                    edges = json_response['data']['orders']['edges']
                    for edge in edges:
                        order_id = edge['node']['id']
                        print(f'edge.node.id 👉 {order_id}')
                        order_ids.add(order_id.split('/')[-1])
                    last_edge = edges[-1] if edges else None
                    cursor = last_edge['cursor'] if last_edge else None
                    has_next_page = json_response['data']['orders']['pageInfo']['hasNextPage']
                except Exception as error:
                    print('Error fetching orders from Shopify:', error)
                    raise

        return order_ids
    
    def generate_timestamp(self):
        date = datetime.now()
        return date.strftime("%Y%m%d_%H%M%S")
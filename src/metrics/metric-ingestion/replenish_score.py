from io import BytesIO
import os
import pandas as pd
import numpy as np
import boto3
import athena_helper
import raleon_helper
import psycopg2
import psycopg2.extras
from datetime import datetime
import time
from functools import wraps

def timer(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start = time.time()
        result = func(*args, **kwargs)
        duration = time.time() - start
        print(f"{func.__name__} took {duration:.2f} seconds")
        return result
    return wrapper

class ReplenishmentSeeker:
    @timer
    def getData(self, message):
        self.message = message
        database = os.environ["ATHENA_DB"]
        
        # Get excluded product IDs
        excluded_products = raleon_helper.get_excluded_product_ids(message['orgId'])
        exclude_clause = ""
        if excluded_products:
            # Convert list to comma-separated string if it's not already
            if isinstance(excluded_products, list):
                excluded_products = ','.join(map(str, excluded_products))
            exclude_clause = f"AND o.item_id NOT IN ({excluded_products})"
            
        query_string = f"""
        WITH aggregated_refunds AS (
            SELECT 
                order_id, 
                COALESCE(SUM(refund_amount), 0) AS total_refund_amount
            FROM 
                "{database}"."order_refunds"
            WHERE 
                organization = '{message['orgId']}'
            GROUP BY 
                order_id  -- Pre-aggregate refund amounts for each `order_id`
        ),
        order_totals AS (
            SELECT 
                o.id,
                o.subtotal_price - COALESCE(r.total_refund_amount, 0) AS price  -- Subtract pre-aggregated refund amount
            FROM 
                "{database}"."filtered_orders" o
            LEFT JOIN 
                aggregated_refunds r ON o.id = r.order_id  -- Join the pre-aggregated refund amounts
            WHERE 
                o.organization = '{message['orgId']}'
                AND o.customer != '0'
                {exclude_clause}
        )
        SELECT 
            o.customer AS user,
            o.item_id AS product,
            o.created_at AS date,
            ot.price,
            o.id
        FROM 
            "{database}"."filtered_orders" o
        JOIN 
            order_totals ot ON ot.id = o.id
        WHERE 
            o.organization = '{message['orgId']}' 
            AND o.customer != '0'
            AND o.customer NOT IN (
                SELECT customerId 
                FROM "{database}".excluded_customers 
                WHERE organization = '{message['orgId']}'
            )
            {exclude_clause}
        GROUP BY 
            o.customer, o.item_id, o.created_at, ot.price, o.id;
        """
        
        if 'queryOverride' in message and message['queryOverride']:
            query_string = message['queryOverride']
            query_string = query_string.replace("{orgId}", str(message['orgId']))
            query_string = query_string.replace("{database}", database)
            
        columns = ['user', 'product', 'date', 'price', 'id']
        return self.query_athena_and_get_df(query_string, columns)
    @timer
    def processData(self, df):
        """Process the data with proper handling for first-time runs"""
        # Get previous metrics (will be empty DataFrame on first run)
        df_prev_metric_values = self.get_previous_metric_values()
        
        # Calculate replenishment metrics
        replenishment_candidates = self.identify_replenishment_candidates(df)
        customer_timing = self.analyze_customer_timing(df, replenishment_candidates)
        
        # If we have no candidates, return empty DataFrame
        if customer_timing.empty:
            return pd.DataFrame()
            
        # Prepare the current data
        customer_timing = customer_timing.rename(columns={'user': 'customer'})
        customer_timing['rundate'] = pd.Timestamp.now()
        
        # If we have no previous data, return all current records
        if df_prev_metric_values.empty:
            customer_timing['previousreplenishmentscore'] = pd.NA
            return customer_timing
        
        # Merge with previous metric values
        customer_timing = customer_timing.merge(
            df_prev_metric_values, 
            on='customer', 
            how='left'
        )
        
        # Filter rows where previous score is different or was not present
        customer_timing = customer_timing[
            (customer_timing['previousreplenishmentscore'].isna()) |
            (customer_timing['previousreplenishmentscore'] != customer_timing['replenishment_score'])
        ]
        
        return customer_timing
    @timer
    def update_postgres(self, final_df):
        # print("Updating Postgres")
        database_info = raleon_helper.get_database_info()
        conn = psycopg2.connect(
            user=database_info['username'],
            password=database_info['password'],
            host=database_info['host'],
            dbname=database_info['dbname'],
            port=database_info['port']
        )
        
        try:
            cur = conn.cursor()
            print("Connected to database")
            
            update_sql = """
                UPDATE raleonuseridentity 
                SET replenishmentscore = data.score,
                replenishmentproduct = data.product,
                    metricsupdated = NOW()
                FROM (VALUES %s) AS data (identityvalue, score, product)
                WHERE raleonuseridentity.identityvalue = data.identityvalue 
                AND raleonuseridentity.orgid = {0};
            """.format(self.message['orgId'])
            
            update_data = [(str(row['customer']), float(row['replenishment_score']), str(row['product'])) 
                        for _, row in final_df.iterrows()]
            
            if update_data:
                psycopg2.extras.execute_values(
                    cur,
                    update_sql,
                    update_data,
                    template=None,
                    page_size=1000
                )
                
                cur.execute("SELECT 1", (self.message['orgId'],))
                conn.commit()
                print(f"Updated {len(update_data)} records")
            else:
                print("No records to update")
                
        except Exception as e:
            print(f"An error occurred: {e}")
            conn.rollback()
        finally:
            cur.close()
            conn.close()
    @timer
    def writeData(self, final_df):
        if final_df.empty:
            print("No changes in metrics detected. No data written.")
            return
            
        s3 = boto3.client('s3')
        # print("Writing data to S3 and updating Postgres")
        self.update_postgres(final_df)

        # Handle NA values and ensure numeric types before writing
        if 'previousreplenishmentscore' in final_df.columns:
            final_df['previousreplenishmentscore'] = final_df['previousreplenishmentscore'].fillna(0.0)
            final_df['previousreplenishmentscore'] = pd.to_numeric(final_df['previousreplenishmentscore'], errors='coerce')
        
        final_df['replenishment_score'] = final_df['replenishment_score'].fillna(0.0)
        final_df['replenishment_score'] = pd.to_numeric(final_df['replenishment_score'], errors='coerce')
        
        # Convert to float64 dtype
        for col in ['previousreplenishmentscore', 'replenishment_score']:
            if col in final_df.columns:
                final_df[col] = final_df[col].astype('float64')
        
        parquet_buffer = BytesIO()
        final_df.to_parquet(
            parquet_buffer,
            index=False,
            engine='pyarrow'
        )
        
        if 'CURATED_BUCKET' not in os.environ:
            raise ValueError("CURATED_BUCKET environment variable is not set")
            
        formatted_time = pd.Timestamp.now().strftime('%Y-%m-%d_%H-%M')
        s3.put_object(
            Bucket=os.environ["CURATED_BUCKET"], 
            Key=f"replenish_score/organization={self.message['orgId']}/{formatted_time}.parquet", 
            Body=parquet_buffer.getvalue()
        )

    @timer
    def get_previous_metric_values(self):
        database = os.environ["ATHENA_DB"]
        query_string = f"""
        SELECT 
            customer, 
            max_by(replenishment_score, rundate) as previousreplenishmentscore 
        FROM "{database}"."replenish_score" 
        WHERE organization='{str(self.message['orgId'])}' 
        GROUP BY customer;
        """
        columns = ['customer', 'previousreplenishmentscore']
        return self.query_athena_and_get_df(query_string, columns)
    @timer
    def query_athena_and_get_df(self, query_string, columns):
        if 'ATHENA_OUTPUT_BUCKET' not in os.environ:
            raise ValueError("ATHENA_OUTPUT_BUCKET environment variable is not set")
            
        response = athena_helper.run_athena_query_v2(query_string, os.environ["ATHENA_OUTPUT_BUCKET"])
        query_id = response['QueryExecutionId']

        if athena_helper.wait_for_query_execution(query_id):
            results = athena_helper.get_query_results(query_id)
            rows = [row['Data'] for row in results['ResultSet']['Rows'][1:]]
            df = pd.DataFrame(rows, columns=columns)
            
            for col in df.columns:
                if df[col].apply(isinstance, args=(dict,)).any():
                    df[col] = df[col].apply(lambda x: self.extract_value_from_dict(x, col))
            return df
        else:
            raise Exception("Query failed!")

    def extract_value_from_dict(self, row, column_name):
        # If row is already a string/number, return it directly
        if not isinstance(row, dict):
            return row
            
        # Extract from VarCharValue format
        if 'VarCharValue' in row:
            value = row['VarCharValue']
            if column_name == 'date':
                return pd.to_datetime(value)
            elif column_name in ['price']:
                return float(value)
            return value
        
        return None

    def calculate_customer_metrics(self, purchase_dates):
        if len(purchase_dates) < 2:
            return {
                'avg_interval': None,
                'std_interval': None,
                'purchase_count': 1,
                'first_purchase': purchase_dates.min(),
                'last_purchase': purchase_dates.max(),
                'regularity_score': 0
            }
        
        intervals = purchase_dates.sort_values().diff().dropna().dt.days
        return {
            'avg_interval': intervals.mean(),
            'std_interval': intervals.std(),
            'purchase_count': len(purchase_dates),
            'first_purchase': purchase_dates.min(),
            'last_purchase': purchase_dates.max(),
            'regularity_score': 1 - (intervals.std() / intervals.mean() if intervals.mean() > 0 else 0)
        }

    def calculate_confidence_score(self, metrics, purchase_dates):
        if len(purchase_dates) < 2:
            return 0.0
            
        # Convert to days and handle TimeDelta
        dates_sorted = sorted(purchase_dates)
        intervals = np.array([(dates_sorted[i] - dates_sorted[i-1]).days 
                            for i in range(1, len(dates_sorted))])
        
        # More dramatic weighting curve (exponential)
        weights = np.exp(np.linspace(0, 1, len(intervals))) / np.exp(1)
        
        # Calculate weighted mean and variance
        weighted_mean = np.average(intervals, weights=weights)
        weighted_variance = np.average((intervals - weighted_mean)**2, weights=weights)
        weighted_std = np.sqrt(weighted_variance)
        
        # Calculate consistency score
        if weighted_mean == 0:
            consistency_score = 0
        else:
            cv = weighted_std / weighted_mean
            consistency_score = 1 / (1 + cv)  # Transform to 0-1 scale
        
        # Add purchase count factor
        purchase_count_factor = 1 - (1 / len(purchase_dates))
        
        # Combine factors
        final_score = consistency_score * 0.7 + purchase_count_factor * 0.3
        
        return max(0, min(1, final_score))
    @timer
    def identify_replenishment_candidates(self, df, min_repeat_purchases=2, min_repurchase_rate=0.03, max_inactive_days=365):
        if df is None or df.empty:
            return {}

        try:
            current_date = datetime.now()

            # Convert dates to datetime
            df['date'] = pd.to_datetime(df['date'])
            
            # Group and transform data
            grouped = df.groupby(['product', 'user'])
            purchase_dates = grouped['date'].agg(list)
            purchase_counts = grouped.size()

            # Vectorized metrics calculation
            metrics_df = pd.DataFrame({
                'dates': purchase_dates,
                'count': purchase_counts
            }).reset_index()
            
            # Calculate last purchase dates for all users at once
            metrics_df['last_purchase'] = metrics_df['dates'].apply(max)
            metrics_df['days_since_purchase'] = (current_date - metrics_df['last_purchase']).dt.days
            metrics_df['active'] = metrics_df['days_since_purchase'] <= max_inactive_days
            metrics_df['repeat'] = metrics_df['count'] > 1

            # Calculate intervals
            metrics_df['intervals'] = metrics_df['dates'].apply(
                lambda dates: np.diff([d.timestamp() for d in sorted(dates)]) / (24*3600) if len(dates) > 1 else []
            )
            metrics_df['avg_interval'] = metrics_df['intervals'].apply(
                lambda x: np.mean(x) if len(x) > 0 else None
            )

            # Aggregate by product
            product_stats = metrics_df[metrics_df['active']].groupby('product').agg({
                'user': 'count',  # active customers
                'repeat': 'sum',  # repeat customers
                'avg_interval': lambda x: np.mean([i for i in x if i is not None])
            })

            # Calculate repurchase rates
            product_stats['repurchase_rate'] = product_stats['repeat'] / product_stats['user']
            
            # Filter and create final metrics
            qualified = product_stats[
                (product_stats['repurchase_rate'] >= min_repurchase_rate) & 
                (product_stats['repeat'] >= min_repeat_purchases)
            ]
            
            return {
                product: {'avg_days_between_purchases': row['avg_interval']}
                for product, row in qualified.iterrows()
            }
        except Exception as e:
            print(f"Error in replenishment candidate identification: {str(e)}")
            return {}
    
    def calculate_replenishment_score(self, row, max_overdue_factor=2):
        days_until_due = float(row['avg_replenishment_interval'] - row['days_since_purchase'])
        max_overdue_days = float(row['avg_replenishment_interval'] * max_overdue_factor)
        
        if row['days_overdue'] > max_overdue_days:
            return 0
        
        if row['days_since_purchase'] < (0.70 * row['avg_replenishment_interval']):
            return 0
            
        base_score = float(row['confidence_score'] * 800)
        
        if 0 <= row['days_overdue'] <= (0.1 * row['avg_replenishment_interval']):
            timing_score = 200.0
        else:
            overdue_penalty = min(1.0, float(row['days_overdue']) / max_overdue_days)
            timing_score = 200.0 * (1 - overdue_penalty)
            
            if days_until_due > 0:
                early_penalty = days_until_due / 30
                timing_score *= (1 - early_penalty)
        
        final_score = base_score + timing_score
        return 0 if pd.isna(final_score) else round(max(0, min(1000, final_score)))
    @timer
    def analyze_customer_timing(self, df, replenishment_items, max_inactive_days=365, min_purchases=2):
        # print(f"Starting analyze_customer_timing with {len(df)} rows")
        current_date = datetime.now()
        customer_timing = []
        
        for product in replenishment_items.keys():
            product_avg_interval = replenishment_items[product]['avg_days_between_purchases']
            if not product_avg_interval:
                continue
                
            item_purchases = df[df['product'] == product]
            
            for user in item_purchases['user'].unique():
                customer_history = item_purchases[item_purchases['user'] == user]
                purchase_dates = pd.Series(customer_history['date'])
                metrics = self.calculate_customer_metrics(purchase_dates)
                
                if metrics['purchase_count'] < min_purchases:
                    continue
                    
                last_purchase = metrics['last_purchase']
                days_since_purchase = (current_date - last_purchase).days
                
                if days_since_purchase > max_inactive_days:
                    continue
                    
                expected_interval = metrics['avg_interval'] or product_avg_interval
                confidence_score = self.calculate_confidence_score(metrics, purchase_dates)
                # print(f"Calculated confidence score: {confidence_score}")
                
                days_overdue = max(0, days_since_purchase - expected_interval)
                
                customer_timing.append({
                    'user': user,
                    'product': product,
                    'last_purchase_date': last_purchase,
                    'days_since_purchase': days_since_purchase,
                    'avg_replenishment_interval': expected_interval,
                    'purchase_count': metrics['purchase_count'],
                    'confidence_score': float(confidence_score),
                    'due_for_replenishment': days_since_purchase >= expected_interval * 0.9,
                    'days_overdue': float(days_overdue)
                })
        
        if not customer_timing:
            return pd.DataFrame()
            
        customer_timing_df = pd.DataFrame(customer_timing)
        customer_timing_df['replenishment_score'] = customer_timing_df.apply(
            self.calculate_replenishment_score, axis=1
        )
        
        return customer_timing_df.sort_values('replenishment_score', ascending=False)
import base64
import boto3
import time
import json
from typing import List, Dict, Any
import os
from datetime import datetime, timedelta
import pandas as pd
import requests
import athena_helper
from raleon_helper import query_database

class AthenaWorkGroup:
    METRIC = 'metric'
    ADHOC = 'adhoc'

class Hubspot:
    def __init__(self):
        self.s3_client = boto3.client('s3')
        self.session = boto3.session.Session()
        self.client = self.session.client(
            service_name='secretsmanager',
            region_name='us-east-1',
            endpoint_url='https://vpce-060670840daa313c8-mcqyvy4b.secretsmanager.us-east-1.vpce.amazonaws.com'
        )
        response = self.client.get_secret_value(SecretId=os.environ['HUBSPOT_SECRET_ARN'])
        self.secret = json.loads(response['SecretString'])
        self.api_key = self.secret['apikey']

    def getData(self, message):
        self.message = message
        if message["query"]:
            return self.get_metric_data(self.message)
        else:
            raise ValueError("Query is not set")
        
    def processData(self, data):
        return data
    
    def writeData(self, data):
        if not data:
            print("No data to write")
            return
        if self.message["dataStructure"]:
            metrics_data_url = self.write_metrics_to_hubspot(data, self.message)
            print(f"metricsDataUrl 👉 {metrics_data_url}")

    def get_contact_by_email(self, email):
        url = f"https://api.hubspot.com/contacts/v1/contact/email/{email}/profile"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            print("Successfully retrieved contact details.")
            return response.json()
        else:
            print(f"Failed to retrieve contact details: {response.text}")
            return None

    def update_associated_company(self, company_id, updates):
        url = f"https://api.hubspot.com/crm/v3/objects/company/{company_id}"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        body = {
            "properties": updates
        }
        response = requests.patch(url, headers=headers, json=body)
        if response.status_code in [200, 201]:
            print(f"Successfully updated company {company_id}.")
        else:
            print(f"Failed to update company {company_id}: {response.text}")

    def write_metrics_to_hubspot(self, metrics_data, message):
        email = self.get_email_from_org(message['orgId'])
        if not email:
            return
        currency_conversion = self.get_currency_conversion(message['orgId'])
        if not currency_conversion:
            currency_conversion = 1
        try:
            currency_conversion = float(currency_conversion)
        except ValueError:
            print(f"Invalid currency conversion factor: {currency_conversion}")
            return
        df = pd.DataFrame(metrics_data)
        for column, details in message['dataStructure'].items():
            print(f"column: {column}, details: {details}")
            prefix = details.get('prefix', '')
            
            if details['type'] == 'float64':
                df[column] = df[column].astype('float64')
            elif details['type'] == 'DOUBLE':
                df[column] = df[column].astype('double')
            elif details['type'] == 'INT64':
                df[column] = df[column].astype('int64')
            elif details['type'] == 'datetime':
                df[column] = pd.to_datetime(df[column]).astype(int) // 10**6  # Convert to Unix timestamp in milliseconds

            
            if prefix == '$':
                df[column] = (df[column].astype('float64') * currency_conversion).round().astype('int64')
            else:
                df[column] = df[column].astype(str)

        metrics_dict = df.to_dict(orient='records')[0]
        self.send_to_hubspot(email, metrics_dict)


    def send_to_hubspot(self, email, attributes):
        print(f"Sending to HubSpot: {attributes}")
        print(f"Email: {email}")
        try:
            contact_info = self.get_contact_by_email(email)
            if contact_info:
                company_id = contact_info.get('properties', {}).get('associatedcompanyid', {}).get('value')
                if company_id:
                    self.update_associated_company(company_id, attributes)
                else:
                    print("No associated company ID found.")
            else:
                print(f"Failed to update HubSpot contact for {email}: {contact_info.text}")
        except Exception as e:
            print(f"Exception occurred while updating contact in HubSpot: {str(e)}")

    def get_email_from_org(self, orgId):
        rows = query_database("SELECT email FROM public.user WHERE organizationid = %s and email not like '%%raleon%%' ORDER BY ID ASC", (orgId,))
        if rows:
            return rows[0][0]
        else:
            print(f'No information found for org: {orgId}')
            return None
        
    def get_currency_conversion(self, orgId):
        rows = query_database("select conversiontousd from supportedcurrencies s join currency c on s.id = c.supportedcurrenciesid where c.organizationid = %s", (orgId,))
        if rows:
            return rows[0][0]
        else:
            print(f'No currency information found for org: {orgId}')
            return None
        
    def get_metric_data(self, message: Dict[str, Any]) -> List[Dict[str, Any]]:
        query = message['query']
        if '{orgId}' not in query:
            raise ValueError("{orgId} placeholder is not in the query. Metric data must be filtered by organization.")
        else:
            query = query.replace("{orgId}", str(message['orgId']))
        if(message['lastRunDate'] != "" and message['lastRunDate'] != None):
            utc_time = datetime.fromisoformat(message['lastRunDate'].replace('Z', '+00:00'))
        else:
            utc_time = datetime.utcnow() - timedelta(days=1)
        formatted_date = utc_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        query = query.replace("{lastRunDate}", formatted_date)
        query = query.replace("{database}", os.environ["ATHENA_DB"])
        for key, value in message['variables'].items():
            placeholder = f"{{{key}}}"
            query = query.replace(placeholder, str(value))
        
        print(f"athenaQuery 👉 {query}")
        column_names = list(message['dataStructure'].keys())
        
        if 'ATHENA_OUTPUT_BUCKET' not in os.environ:
            raise ValueError("ATHENA_OUTPUT_BUCKET environment variable is not set")
        
        athena_result = athena_helper.run_athena_query_v2(
            query,
            os.environ['ATHENA_OUTPUT_BUCKET'],
            0,
            AthenaWorkGroup.METRIC,
            message['catalog']
        )
        print(f"athenaResult 👉 {athena_result}")
        
        check_table_id = athena_result['QueryExecutionId']
        check_table_query_result = athena_helper.wait_for_query_execution(check_table_id)
        
        if not check_table_query_result:
            raise ValueError("Athena query failed to execute")
        else:
            query_execution_id = check_table_id
            query_result_data = athena_helper.get_query_results(query_execution_id)
            query_data = []
            batch_timestamp = self.generate_timestamp()
            
            if query_result_data['ResultSet'] and query_result_data['ResultSet']['Rows']:
                print(f"Query Results: {json.dumps(query_result_data['ResultSet']['Rows'])}")
                data = query_result_data['ResultSet']['Rows']
                
                for i in range(1, len(data)):
                    row_data = data[i]['Data']
                    if not row_data:
                        continue
                    
                    row = {}
                    for index, column in enumerate(row_data):
                        column_name = column_names[index]
                        row[column_name] = 0

                        if 'VarCharValue' not in column:
                            continue

                        value = column['VarCharValue']

                        try:
                            date_value = int(time.mktime(time.strptime(value, "%Y-%m-%d %H:%M:%S")))
                            row[column_name] = date_value
                            continue
                        except ValueError:
                            pass 
                        
                        try:
                            float_value = float(value)
                            row[column_name] = float_value
                        except ValueError:
                            row[column_name] = value

                    query_data.append(row)

            return query_data

    def generate_timestamp(self):
        date = datetime.now()
        return date.strftime("%Y%m%d_%H%M%S")

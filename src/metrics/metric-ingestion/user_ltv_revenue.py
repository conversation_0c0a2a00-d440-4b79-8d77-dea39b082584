from io import BytesIO
import os
import pandas as pd
import numpy as np
import boto3
import athena_helper
import raleon_helper
import psycopg2
import psycopg2.extras

class UserLTVRevenue:
	def getData(self, message):
		self.message = message
		database = os.environ["ATHENA_DB"]
		query_string = f"""
			WITH distinct_orders AS (
				SELECT DISTINCT
					customer,
					id AS order_id,
					total_price,
					created_at
				FROM 
					"{database}".filtered_orders
				WHERE 
					organization = '{message['orgId']}'
			),
			excluded_customers AS (
				SELECT 
					customerid
				FROM 
					"{database}".excluded_customers
				WHERE 
					organization = '{message['orgId']}'
			),
			valid_customers AS (
				SELECT 
					customer
				FROM 
					distinct_orders
				WHERE 
					customer NOT IN (SELECT customerid FROM excluded_customers)
			),
			ltv_data AS (
				SELECT 
					d.customer,
					SUM(d.total_price) AS ltv,
					COUNT(d.order_id) AS num_orders,
					PERCENT_RANK() OVER (ORDER BY SUM(d.total_price) ASC) * 1000 AS ltvdistribution,
					SUM(d.total_price) / COUNT(d.order_id) AS aov
				FROM 
					distinct_orders d
               	WHERE 
                    EXISTS (
                        SELECT 1 
                        FROM valid_customers vc 
                        WHERE d.customer = vc.customer
                    )
				GROUP BY 
					d.customer
			),
			last_90_days_data AS (
				SELECT 
					d.customer,
					SUM(d.total_price) AS revenue
				FROM 
					distinct_orders d
				WHERE d.customer IN (SELECT customer FROM valid_customers)
					AND d.created_at >= date_add('day', -90, current_date)
				GROUP BY 
					d.customer
			)
			SELECT 
				l.customer,
				l.ltv,
				l.ltvdistribution,
				l.aov,
				COALESCE(r.revenue, 0) AS revenue
			FROM 
				ltv_data l
			LEFT JOIN 
				last_90_days_data r
			ON 
				l.customer = r.customer
			ORDER BY 
				l.ltv DESC;
		"""
		if 'queryOverride' in message and message['queryOverride']:
			query_string = message['queryOverride']
			if query_string:
				query_string = query_string.replace("{orgId}", str(message['orgId']))
				query_string = query_string.replace("{database}", os.environ["ATHENA_DB"])
			else:
				print("query_string is empty or None")
		columns = ['customer', 'ltv', 'ltvdistribution', 'aov', 'revenue']
		return self.query_athena_and_get_df(query_string, columns)

	def processData(self, df):
		df_prev_metric_values = self.get_previous_metric_values()
		
		# Ensure numeric columns are properly typed
		numeric_columns = ['revenue', 'ltv', 'ltvdistribution', 'aov']
		for col in numeric_columns:
			df[col] = pd.to_numeric(df[col], errors='coerce')
		
		# Merge with previous metric values
		df = df.merge(df_prev_metric_values, on='customer', how='left')

		if 'previousltv' in df.columns:
			df['previousltv'] = pd.to_numeric(df['previousltv'], errors='coerce')
		if 'previousrevenue' in df.columns:
			df['previousrevenue'] = pd.to_numeric(df['previousrevenue'], errors='coerce')
		if 'previousaov' in df.columns:
			df['previousaov'] = pd.to_numeric(df['previousaov'], errors='coerce')
		
		df = df[
			(df['previousltv'].isna() | (df['previousltv'] != df['ltv'])) |
			(df['previousrevenue'].isna() | (df['previousrevenue'] != df['revenue'])) |
			(df['previousltvdistribution'].isna() | (df['previousltvdistribution'] != df['ltvdistribution'])) |
			(df['previousaov'].isna() | (df['previousaov'] != df['aov']))
		]
		
		df['rundate'] = pd.Timestamp.now()
		
		return df

	def writeData(self, final_rfm):
		if final_rfm.empty:
			print("No changes in metrics detected. No data written.")
			return
		s3 = boto3.client('s3')
		print("Writing data to S3 and updating Postgres")
		self.update_postgres(final_rfm)
		
		parquet_buffer = BytesIO()
		final_rfm[['customer', 'ltv', 'ltvdistribution', 'aov', 'revenue', 'previousltv', 'previousltvdistribution', 'previousrevenue', 'previousaov', 'rundate']].to_parquet(parquet_buffer, index=False)
		if 'CURATED_BUCKET' not in os.environ:
			raise ValueError("CURATED_BUCKET environment variable is not set")
		formatted_time = pd.Timestamp.now().strftime('%Y-%m-%d_%H-%M')
		s3.put_object(Bucket=os.environ["CURATED_BUCKET"], Key=f"user_ltv_revenue/organization={self.message['orgId']}/{formatted_time}.parquet", Body=parquet_buffer.getvalue())

	def get_previous_metric_values(self):
		database = os.environ["ATHENA_DB"]
		query_string = f"""
		SELECT 
			customer, 
			max_by(ltv, rundate) as previousltv,
			max_by(ltvdistribution, rundate) as previousltvdistribution,
			max_by(revenue, rundate) as previousrevenue,
			max_by(aov, rundate) as previousaov
		FROM 
			"{database}"."user_ltv_revenue"
		WHERE 
			organization = '{str(self.message['orgId'])}'
		GROUP BY 
			customer;
		"""
		columns = ['customer', 'previousltv', 'previousltvdistribution', 'previousrevenue', 'previousaov']
		return self.query_athena_and_get_df(query_string, columns)
	
	def query_athena_and_get_df(self, query_string, columns):
		if 'ATHENA_OUTPUT_BUCKET' not in os.environ:
			raise ValueError("ATHENA_OUTPUT_BUCKET environment variable is not set")
		response = athena_helper.run_athena_query_v2(query_string, os.environ["ATHENA_OUTPUT_BUCKET"])
		query_id = response['QueryExecutionId']

		if athena_helper.wait_for_query_execution(query_id):
			results = athena_helper.get_query_results(query_id)
			rows = [row['Data'] for row in results['ResultSet']['Rows'][1:]]  # [1:] to skip header
			df = pd.DataFrame(rows, columns=columns)
			for col in df.columns:
				if df[col].apply(isinstance, args=(dict,)).any():
					print(f"Column {col} has dictionary values.")
				df[col] = df[col].apply(lambda x: self.extract_value_from_dict(x, col))
			return df
		else:
			raise Exception("Query failed!")

	def extract_value_from_dict(self, row, column_name):
		if isinstance(row, dict) and 'VarCharValue' in row:
			value = row['VarCharValue']
			if column_name == 'created_at':
				return pd.to_datetime(value)
			elif column_name in ['ltv', 'revenue', 'ltvdistribution']:
				return float(value)
			else:
				return value
		return row

	def execute_chunked_query(self, cursor, query, data, orgid, chunk_size=1000):
		print(f"Received data: {data} (type={type(data)})")
		print(f"Received orgid: {orgid} (type={type(orgid)})")
		for i in range(0, len(data), chunk_size):
			chunk = data[i:i + chunk_size]
			print(f"Processing chunk: {chunk} (type={type(chunk)})")
			print(f"orgid: {orgid} (type={type(orgid)})")
			
			try:
				cursor.execute(query, (chunk, orgid))  # Pass parameters as a tuple
			except Exception as e:
				print(f"Query execution failed with error: {e}")
				print(f"Parameters passed: {chunk}, {orgid}")
				raise e

			rows = cursor.fetchall()
			print(f"Rows fetched: {rows}")
			for row in rows:
				yield row

	def update_postgres(self, final_rfm):
		print("Updating Postgres")
		database_info = raleon_helper.get_database_info()
		conn = psycopg2.connect(
			user=database_info['username'],
			password=database_info['password'],
			host=database_info['host'],
			dbname=database_info['dbname'],
			port=database_info['port']
		)

		try:
			cur = conn.cursor()
			print("Connected to database")

			# Get existing customer mappings
			query = """
			SELECT identityvalue, raleonuserid 
			FROM raleonuseridentity
			WHERE identitytype = 'customer_id' 
			AND identityvalue = ANY(%s) 
			AND orgid = %s;
			"""
			unique_identityvalues = final_rfm['customer'].tolist()
			print(f"Unique identity values: {unique_identityvalues}")

			existing_mapping = {}
			for row in self.execute_chunked_query(cur, query, unique_identityvalues, self.message['orgId']):
				identity_value, raleon_user_id = row
				existing_mapping[identity_value] = raleon_user_id

			if not existing_mapping:
				print("No existing mappings found in the database. Check query logic or input data.")
			else:
				print(f"Found {len(existing_mapping)} existing records")

			# Separate new and existing customers
			new_customers = final_rfm[~final_rfm['customer'].isin(existing_mapping.keys())]
			existing_customers = final_rfm[final_rfm['customer'].isin(existing_mapping.keys())]
			print(f"New customers: {new_customers}")
			print(f"Existing customers: {existing_customers}")

			# Update existing customers
			if not existing_customers.empty:
				try:
					update_data = [
						(
							row.customer,
							row.ltv,
							row.ltvdistribution,
							row.revenue,
							row.aov
						)
						for row in existing_customers.itertuples(index=False)
					]
					print(f"Update data: {update_data}")

					update_sql = f"""
					UPDATE raleonuseridentity AS r
					SET 
						ltv = data.ltv,
						ltvdistribution = data.ltvdistribution,
						revenue = data.revenue,
						aov = data.aov,
						metricsupdated = NOW()
					FROM (VALUES %s) AS data (identityvalue, ltv, ltvdistribution, revenue, aov)
					WHERE r.identityvalue = data.identityvalue 
					AND r.identitytype = 'customer_id'
					AND r.orgid = {self.message['orgId']};
					"""

					psycopg2.extras.execute_values(
						cur,
						update_sql,
						update_data,
						template="(%s, %s, %s, %s, %s)",
						page_size=1000
					)
					print("Updated existing customers in raleonuseridentity.")
				except Exception as e:
					print(f"Error updating existing customers: {e}")

			conn.commit()
		except Exception as e:
			print(f"An error occurred: {e}")
			conn.rollback()
		finally:
			print("Closing connection")
			cur.close()
			conn.close()




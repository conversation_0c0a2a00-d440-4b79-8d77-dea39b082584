from io import BytesIO
import json
import os
import requests
from shopify_helper import bulk_update_metafields,get_shop_info_by_org_id, get_api_access_token
import pandas as pd
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import train_test_split
import numpy as np
import boto3
import athena_helper
import raleon_helper
import psycopg2
import asyncio

class LoyaltyPoints:

	def getData(self, message):
		self.message = message
		database = os.environ["ATHENA_DB"]
		query_string = f"SELECT customer,item_name,created_at,item_price,item_id,total_refunded,item_quantity FROM {database}.filtered_orders WHERE organization='{str(message['orgId'])}' AND customer!='0'"
		columns = ['customer', 'item_name', 'created_at', 'item_price', 'item_id', 'total_refunded', 'item_quantity']
		return self.query_athena_and_get_df(query_string, columns)
	
	def processData(self, df):
		if df.empty:
			print("No data to process.")
			return df
		# Convert customerto int type5
		df['customer'] = df['customer'].astype(int)

		# Convert created_at to datetime type
		df['created_at'] = pd.to_datetime(df['created_at'])
		print("\nInitial DataFrame:")
		print(df.head())
		##FEATURE ENGINEERING

		# Count of purchases per customer(already done)
		purchase_count = df.groupby('customer').size()

		# Recency of the last purchase (already done)
		latest_purchase = df.groupby('customer')['created_at'].max()
		recency = (df['created_at'].max() - latest_purchase).dt.days

		# Monetary Value
		total_spent = df.groupby('customer')['item_price'].sum()
		average_spent = df.groupby('customer')['item_price'].mean()

		# Product Diversity
		unique_products = df.groupby('customer')['item_id'].nunique()

		# Purchase Frequency (average time between purchases)
		data_sorted = df.sort_values('created_at')
		time_between_purchases = data_sorted.groupby('customer')['created_at'].diff().dt.days
		average_frequency = time_between_purchases.groupby(data_sorted['customer']).mean()

		# Returns/Refunds
		returns_count = df[df['total_refunded'] > 0].groupby('customer').size()

		# Bulk Purchase Behavior
		average_quantity = df.groupby('customer')['item_quantity'].mean()

		# Engagement Duration
		first_purchase = df.groupby('customer')['created_at'].min()
		engagement_duration = (df['created_at'].max() - first_purchase).dt.days

		# Combine features into a single DataFrame
		features = pd.concat([purchase_count, recency, total_spent, average_spent, unique_products, average_frequency, 
							returns_count, average_quantity, engagement_duration], axis=1)

		# List of base feature names
		base_features = ['PurchaseCount', 'Recency', 'TotalSpent', 'AverageSpent', 'UniqueProducts', 'AvgFrequency',
						'ReturnsCount', 'AvgQuantity', 'EngagementDuration']

		# Extend the base feature names with country dummy variable names
		all_features = base_features

		# Set the column names for the features DataFrame
		features.columns = all_features

		# Fill NaN values with 0
		features.replace([np.inf, -np.inf], np.nan, inplace=True)
		features.fillna(0, inplace=True)
		print("\nFeatures DataFrame after filling NaN values:")
		print(features.head())  
		##TRAIN TEST SPLIT
		scaler = StandardScaler()
		features = pd.DataFrame(scaler.fit_transform(features), columns=features.columns, index=features.index)

		# Target variable
		y = (recency <= 30).astype(int)

		# Train/Test Split
		X_train, X_test, y_train, y_test = train_test_split(features, y, test_size=0.2, random_state=42)

		##MODEL TRAINING
		model = LogisticRegression(max_iter=1000)
		model.fit(X_train, y_train)

		##MODEL EVALUATION
		y_prob = model.predict_proba(X_test)[:, 1]

		##PROBABILITIES AND LOG-ODDS W NORMALIZATION
		# Convert to log-odds
		log_odds = np.log(y_prob / (1 - y_prob))

		# Normalize to 0-1000 scale
		normalized_scores = 1000 * (log_odds - log_odds.min()) / (log_odds.max() - log_odds.min())

		# CSV with customers and scores
		output = pd.DataFrame({
			'customer': X_test.index,
			'probability': y_prob,
			'logodds': log_odds,
			'score': normalized_scores
		})
		output['rundate'] = pd.Timestamp.now()
		print("\nFinal output DataFrame:")
		print(output.head())
		return output

	def writeData(self, final_df):
		if final_df.empty:
			print("No scores detected. No data written.")
			return
		s3 = boto3.client('s3')
		self.update_shopify(final_df)	
		
		parquet_buffer = BytesIO()
		final_df[['customer', 'probability', 'logodds', 'score', 'rundate']].to_parquet(parquet_buffer, index=False)
		if 'CURATED_BUCKET' not in os.environ:
			raise ValueError("CURATED_BUCKET environment variable is not set")
		formatted_time = pd.Timestamp.now().strftime('%Y-%m-%d_%H-%M')
		s3.put_object(Bucket=os.environ["CURATED_BUCKET"], Key=f"loyalty_points/organization={self.message['orgId']}/{formatted_time}.parquet", Body=parquet_buffer.getvalue())

	def query_athena_and_get_df(self, query_string, columns):
		if 'ATHENA_OUTPUT_BUCKET' not in os.environ:
			raise ValueError("ATHENA_OUTPUT_BUCKET environment variable is not set")
		response = athena_helper.run_athena_query_v2(query_string, os.environ["ATHENA_OUTPUT_BUCKET"])
		query_id = response['QueryExecutionId']

		if athena_helper.wait_for_query_execution(query_id):
			results = athena_helper.get_query_results(query_id)
			rows = [row['Data'] for row in results['ResultSet']['Rows'][1:]]  # [1:] to skip header
			df = pd.DataFrame(rows, columns=columns)
			for col in df.columns:
				if df[col].apply(isinstance, args=(dict,)).any():
					print(f"Column {col} has dictionary values.")
				df[col] = df[col].apply(lambda x: self.extract_value_from_dict(x, col))
			return df
		else:
			raise Exception("Query failed!")

	def extract_value_from_dict(self, row, column_name):
		if isinstance(row, dict) and 'VarCharValue' in row:
			value = row['VarCharValue']
			if column_name == 'created_at':
				return pd.to_datetime(value)
			elif column_name == 'item_price':
				return float(value)
			elif column_name == 'total_refunded':
				return float(value)
			else:
				return value
		return row

	def update_shopify(self, final_rfm):
		print("Updating Shopify meta fields...")
		shop_info = get_shop_info_by_org_id(self.message['orgId'])
		sessionToken = get_api_access_token(shop_info['shopDomain'])
		jsonl_content = self.generate_jsonl_content(final_rfm[['customer', 'score']].values.tolist())
		bulk_id = asyncio.run(bulk_update_metafields(jsonl_content, shop_info['shopDomain'], sessionToken))		
		print(f"Bulk ID: {bulk_id}")
	
	def generate_jsonl_content(self, customers_scores):
		jsonl_lines = []
		for customer_value, segment_value in customers_scores:
			metafield_data = { "input" : {
				"key": "loyalty_points",
				"namespace": "raleonInfo",
				"ownerId": f"gid://shopify/Customer/{customer_value}",
				"type": "number_decimal",
				"value": segment_value
			}
			}
			jsonl_lines.append(json.dumps(metafield_data))

		return '\n'.join(jsonl_lines)
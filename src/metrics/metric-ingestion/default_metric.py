import boto3
import time
import json
from typing import List, Dict, Any
import os
from datetime import datetime, timedelta
import pandas as pd
import athena_helper
import raleon_helper

class AthenaWorkGroup:
    METRIC = 'metric'
    ADHOC = 'adhoc'

class DefaultMetric:
    def __init__(self):
        self.s3_client = boto3.client('s3')

    def getData(self, message):
        print("Getting data...")
        self.message = message
        if message.get("queryOverride"):
            return self.get_metric_data(self.message, message["queryOverride"])
        elif message.get("query"):
            return self.get_metric_data(self.message, message["query"])
        else:
            raise ValueError("Query is not set")
        
    def processData(self, data):
        print("Processing data...")
        return data
    
    def writeData(self, data):
        print("Writing data...")
        if not data:
            print("No data to write")
            return
        if self.message["dataStructure"]:
            metrics_data_url = self.write_metrics_data_to_s3(data, self.message)
            print(f"metricsDataUrl 👉 {metrics_data_url}")

    def write_metrics_data_to_s3(self, metrics_data, message):
        df = pd.DataFrame(metrics_data)
        # Dynamically convert columns based on dataStructure
        for column, dtype in message['dataStructure'].items():
            print(f"column: {column}, dtype: {dtype}")
            if dtype['type'] == 'float64':
                df[column] = df[column].astype('float64')
            elif dtype['type'] == 'DOUBLE':
                df[column] = df[column].astype('double')
            elif dtype['type'] == 'INT64':
                df[column] = df[column].astype('int64')
            elif dtype['type'] == 'string':
                df[column] = df[column].astype('string')
            elif dtype['type'] == 'datetime':
                df[column] = pd.to_datetime(df[column])
            elif dtype['type'] == 'UTF8' or dtype['type'] == 'STRING':
                df[column] = df[column].astype('string')

        # Convert runDate to milliseconds
        df['runDate'] = (df['runDate'].astype(int) / 1e6).astype('int64')
        file_path = f"/tmp/{message['orgId']}_{message['name']}.parquet"
        df.to_parquet(file_path)

        timestamp = self.generate_timestamp()
        s3_key = f"{message['name']}/organization={message['orgId']}/{message['name']}_{timestamp}.parquet"
        self.s3_client.upload_file(file_path, os.environ["CURATED_BUCKET"], s3_key)

    def get_metric_data(self, message: Dict[str, Any], query: str) -> List[Dict[str, Any]]:
        if '{orgId}' not in query:
            raise ValueError("{orgId} placeholder is not in the query. Metric data must be filtered by organization.")
        else:
            query = query.replace("{orgId}", str(message['orgId']))
        if(message['lastRunDate'] != "" and message['lastRunDate'] != None):
            utc_time = datetime.fromisoformat(message['lastRunDate'].replace('Z', '+00:00'))
        else:
            utc_time = datetime.utcnow() - timedelta(days=1)
        if(message['activationDate'] != "" and message['activationDate'] != None):
            activationDate = datetime.fromisoformat(message['activationDate'].replace('Z', '+00:00'))
            query = query.replace("{activationDate}", activationDate.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3])
        else:
            activationDate = datetime(2023, 10, 1, 0, 0, 0, 0) #no activation date use beginning of time
            query = query.replace("{activationDate}", activationDate.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3])
        formatted_date = utc_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        query = query.replace("{lastRunDate}", formatted_date)
        query = query.replace("{database}", os.environ["ATHENA_DB"])
        for key, value in message['variables'].items():
            placeholder = f"{{{key}}}"
            query = query.replace(placeholder, str(value))
        
        print(f"Query 👉 {query}")
        column_names = list(message['dataStructure'].keys())
        
        if 'ATHENA_OUTPUT_BUCKET' not in os.environ:
            raise ValueError("ATHENA_OUTPUT_BUCKET environment variable is not set")
        if(message['catalog'] == "posgresreadreplicav3"):
            print("Using Raleon DB")
            query_result = raleon_helper.query_database(query, [])
            query_data = []
            batch_timestamp = self.generate_timestamp()
            print(f"queryResult 👉 {query_result}")
            for row in query_result:
                data_row = {}
                for index, column in enumerate(column_names):
                    value = row[index]
                    if isinstance(value, datetime):
                        data_row[column] = int(value.timestamp())
                    else:
                        data_row[column] = value
                data_row['runDate'] = datetime.now()
                query_data.append(data_row)
            
            return query_data
        else:
            athena_result = athena_helper.run_athena_query_v2(
                query,
                os.environ['ATHENA_OUTPUT_BUCKET'],
                0,
                AthenaWorkGroup.METRIC,
                message['catalog']
            )
            print(f"athenaResult 👉 {athena_result}")
            
            check_table_id = athena_result['QueryExecutionId']
            check_table_query_result = athena_helper.wait_for_query_execution(check_table_id)
            
            if not check_table_query_result:
                raise ValueError("Athena query failed to execute")
            else:
                query_execution_id = check_table_id
                query_result_data = athena_helper.get_query_results(query_execution_id)
                query_data = []
                
                if query_result_data['ResultSet'] and query_result_data['ResultSet']['Rows']:
                    print(f"Query Results: {json.dumps(query_result_data['ResultSet']['Rows'])}")
                    data = query_result_data['ResultSet']['Rows']
                    
                    for i in range(1, len(data)):
                        row_data = data[i]['Data']
                        if not row_data:
                            continue
                        
                        row = {}
                        for index, column in enumerate(row_data):
                            column_name = column_names[index]
                            row[column_name] = 0

                            if 'VarCharValue' not in column:
                                continue

                            value = column['VarCharValue']

                            try:
                                date_value = int(time.mktime(time.strptime(value, "%Y-%m-%d %H:%M:%S")))
                                row[column_name] = date_value
                                continue
                            except ValueError:
                                pass 
                            
                            try:
                                float_value = float(value)
                                row[column_name] = float_value
                            except ValueError:
                                row[column_name] = value
                        
                        row['runDate'] = datetime.now()
                        query_data.append(row)

                return query_data

    def generate_timestamp(self):
        date = datetime.now()
        return date.strftime("%Y%m%d_%H%M%S")
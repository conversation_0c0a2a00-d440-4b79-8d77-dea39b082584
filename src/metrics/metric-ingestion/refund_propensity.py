from io import BytesIO
import os
import pandas as pd
import numpy as np
import boto3
import athena_helper
import raleon_helper
import psycopg2
import psycopg2.extras
import time
from functools import wraps

def timer(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start = time.time()
        result = func(*args, **kwargs)
        duration = time.time() - start
        print(f"{func.__name__} took {duration:.2f} seconds")
        return result
    return wrapper

class RefundPropensityScorer:
    @timer
    def getData(self, message):
        self.message = message
        database = os.environ["ATHENA_DB"]
        pgdatabase = os.environ["POSGRES_DB"]
        query_string = f"""
        WITH OrderDiffs AS (
            SELECT
                o.customer AS CustomerID,
                o.created_at,
                LAG(o.created_at) OVER (PARTITION BY o.customer ORDER BY o.created_at) AS PreviousOrderDate
            FROM
                (
                    SELECT DISTINCT
                        customer,
                        created_at,
                        id
                    FROM
                        "{database}"."filtered_orders"
                    WHERE
                        organization = '{message['orgId']}'
                ) o
        ),
        AvgTimeBetweenPurchases AS (
            SELECT
                CustomerID,
                AVG(EXTRACT(DAY FROM (created_at - PreviousOrderDate))) AS AvgTimeBetweenPurchases
            FROM
                OrderDiffs
            WHERE
                PreviousOrderDate IS NOT NULL
            GROUP BY
                CustomerID
        ),
        LoyaltyTransactions AS (
            SELECT
                ltx.loyaltycurrencybalanceid,
                SUM(CASE WHEN ltx.amount < 0 THEN 1 ELSE 0 END) AS used,
                SUM(CASE WHEN ltx.amount > 0 THEN 1 ELSE 0 END) AS earned
            FROM
                "{pgdatabase}"."public"."loyaltycurrencytxlog" ltx
            GROUP BY
                ltx.loyaltycurrencybalanceid
        ),
        aggregated_refunds AS (
            SELECT 
                order_id, 
                COALESCE(SUM(refund_amount), 0) AS total_refund_amount
            FROM 
                "{database}"."order_refunds"
            WHERE 
                organization = '{message['orgId']}'
            GROUP BY 
                order_id
        ),
        aggregated_orders AS (
            SELECT
                id,
                customer,
                MAX(total_discounts) AS total_discounts,
                MAX(total_shipping_price) AS total_shipping_price,
                MAX(total_price) AS total_price,
                MAX(subtotal_price) AS subtotal_price,
                MAX(created_at) AS created_at
            FROM
                "{database}"."filtered_orders"
            WHERE
                organization = '{message['orgId']}'
                AND customer NOT IN (
                    SELECT customerId 
                    FROM "{database}".excluded_customers 
                    WHERE organization = '{message['orgId']}'
                )
            GROUP BY
                id, customer
        )
        SELECT
            ri.id,
            ri.orgid,
            ri.raleonuserid,
            ri.identityvalue,
            ri.loyaltysegment,
            lb.loyaltycurrencyid,
            lb.balance,
            lb.trailingtwelvemonthgranttotal,
            COALESCE(tx.earned, 0) AS earned,
            COALESCE(tx.used, 0) AS used,
            COALESCE(ord.TotalOrders, 0) AS TotalOrders,
            COALESCE(ord.OrdersWithDiscounts, 0) AS OrdersWithDiscounts,
            COALESCE(ord.TotalSpent, 0) AS TotalSpent,
            COALESCE(ord.SubtotalSpent, 0) AS SubtotalSpent,
            COALESCE(ord.OrdersWithFreeShipping, 0) AS OrdersWithFreeShipping,
            COALESCE(ord.OrdersWithRefunds, 0) AS OrdersWithRefunds,
            COALESCE(ord.TotalAmountRefunded, 0) AS TotalAmountRefunded,
            COALESCE(ord.DaysSinceLastPurchase, NULL) AS DaysSinceLastPurchase,
            COALESCE(atbp.AvgTimeBetweenPurchases, NULL) AS AvgTimeBetweenPurchases
        FROM
            "{pgdatabase}"."public"."raleonuseridentity" ri
        LEFT JOIN
            "{pgdatabase}"."public"."loyaltycurrencybalance" lb ON ri.raleonuserid = lb.raleonuserid
        LEFT JOIN
            LoyaltyTransactions tx ON lb.id = tx.loyaltycurrencybalanceid
        LEFT JOIN
            (
                SELECT
                    o.customer AS CustomerID,
                    COUNT(DISTINCT o.id) AS TotalOrders,  -- Count of distinct orders
                    COUNT(DISTINCT CASE WHEN o.total_discounts > 0 THEN o.id END) AS OrdersWithDiscounts,
                    SUM(o.total_price) AS TotalSpent,
                    SUM(o.subtotal_price) AS SubtotalSpent,
                    COUNT(DISTINCT CASE WHEN o.total_shipping_price = 0 THEN o.id END) AS OrdersWithFreeShipping,
                    COUNT(DISTINCT CASE WHEN r.total_refund_amount > 0 THEN o.id END) AS OrdersWithRefunds,
                    COALESCE(SUM(r.total_refund_amount), 0) AS TotalAmountRefunded,  -- Sum pre-aggregated refund amounts
                    MAX(o.created_at) AS LastOrderDate,
                    EXTRACT(DAY FROM CURRENT_DATE - MAX(o.created_at)) AS DaysSinceLastPurchase
                FROM
                    aggregated_orders o
                LEFT JOIN
                    aggregated_refunds r ON o.id = r.order_id 
                GROUP BY
                    o.customer
            ) ord ON ri.identityvalue = ord.CustomerID
        LEFT JOIN
            AvgTimeBetweenPurchases atbp ON ri.identityvalue = atbp.CustomerID
        WHERE
            ri.orgid = {message['orgId']};
        """

        if 'queryOverride' in message and message['queryOverride']:
            query_string = message['queryOverride']
            if query_string:
                query_string = query_string.replace("{orgId}", str(message['orgId']))
                query_string = query_string.replace("{database}", os.environ["ATHENA_DB"])
            else:
                print("query_string is empty or None")

        columns = ['id', 'orgid', 'raleonuserid', 'identityvalue', 'loyaltysegment', 'loyaltycurrencyid',
                  'balance', 'trailingtwelvemonthgranttotal', 'earned', 'used', 'TotalOrders',
                  'OrdersWithDiscounts', 'TotalSpent', 'SubtotalSpent', 'OrdersWithFreeShipping',
                  'OrdersWithRefunds', 'TotalAmountRefunded', 'DaysSinceLastPurchase', 'AvgTimeBetweenPurchases']
                  
        return self.query_athena_and_get_df(query_string, columns)

    @timer
    def processData(self, df):
        df_prev_metric_values = self.get_previous_metric_values()
        
        # Ensure numeric columns are properly typed
        numeric_columns = ['TotalOrders', 'OrdersWithRefunds', 'TotalAmountRefunded', 'TotalSpent']
        for col in numeric_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Calculate refund rate with safety checks
        df['refund_rate'] = np.where(df['TotalOrders'] > 0, 
                                    df['OrdersWithRefunds'] / df['TotalOrders'], 0)
        
        # Calculate average refund amount per order with safety checks
        df['avg_refund_amount'] = np.where(df['OrdersWithRefunds'] > 0,
                                         df['TotalAmountRefunded'] / df['OrdersWithRefunds'], 0)
        
        # Calculate score with error handling
        def calculate_score(row):
            try:
                base_score = 500  # Start with a middle score
                
                if pd.isna(row['TotalOrders']) or row['TotalOrders'] == 0:
                    return 0  # No orders, no risk
                
                # Calculate components with safety checks
                refund_rate = float(row['refund_rate']) if pd.notnull(row['refund_rate']) else 0
                refund_history = 300 * refund_rate
                
                # Ensure all values are float
                total_spent = float(row['TotalSpent']) if pd.notnull(row['TotalSpent']) else 0
                total_orders = float(row['TotalOrders']) if pd.notnull(row['TotalOrders']) else 1
                avg_refund = float(row['avg_refund_amount']) if pd.notnull(row['avg_refund_amount']) else 0
                
                avg_order_value = total_spent / total_orders if total_orders > 0 else 0
                refund_value = 200 * (avg_refund / avg_order_value) if avg_order_value > 0 else 0
                
                days_since = float(row['DaysSinceLastPurchase']) if pd.notnull(row['DaysSinceLastPurchase']) else 0
                order_frequency = 100 * np.exp(-0.1 * days_since)
                
                loyalty_factor = -100 if str(row['loyaltysegment']).strip() == 'Very Loyal' else 0
                
                # Combine components
                score = base_score + refund_history + refund_value + order_frequency + loyalty_factor
                
                # Ensure score is between 0 and 1000
                return float(np.clip(score, 0, 1000))
            except Exception as e:
                print(f"Error calculating score: {e}")
                print(f"Row data: {row}")
                return 500.0  # Return base score on error

        # Calculate the refund propensity score
        df['refundPropensity'] = df.apply(calculate_score, axis=1)
        
        # Merge with previous metric values
        df = df.merge(df_prev_metric_values, on='identityvalue', how='left')

        if 'previousrefundPropensity' in df.columns:
            df['previousrefundPropensity'] = pd.to_numeric(df['previousrefundPropensity'], errors='coerce')
        
        # Filter rows where previous score is different from current score
        df = df[
            (df['previousrefundPropensity'].isna()) |
            (df['previousrefundPropensity'] != df['refundPropensity'])
        ]
        
        df['rundate'] = pd.Timestamp.now()
        
        return df

    @timer
    def get_previous_metric_values(self):
        pgdatabase = os.environ["POSGRES_DB"]
        query_string = f"""
        SELECT 
            identityvalue,
            refundPropensity as previousrefundPropensity 
        FROM 
            "{pgdatabase}"."public"."raleonuseridentity" 
        WHERE 
            orgid = {str(self.message['orgId'])};
        """
        columns = ['identityvalue', 'previousrefundPropensity']
        return self.query_athena_and_get_df(query_string, columns)

    @timer
    def update_postgres(self, final_df):
        print("Updating Postgres")
        database_info = raleon_helper.get_database_info()
        conn = psycopg2.connect(
            user=database_info['username'],
            password=database_info['password'],
            host=database_info['host'],
            dbname=database_info['dbname'],
            port=database_info['port']
        )
        
        try:
            cur = conn.cursor()
            print("Connected to database")
            
            # Update existing records
            update_sql = """
                UPDATE raleonuseridentity 
                SET refundpropensity = data.score,
                    metricsupdated = NOW()
                FROM (VALUES %s) AS data (identityvalue, score)
                WHERE raleonuseridentity.identityvalue = data.identityvalue 
                AND raleonuseridentity.orgid = {0};
            """.format(self.message['orgId'])  # Include orgId directly in SQL
            
            # Prepare data for update
            update_data = [(str(row['identityvalue']), float(row['refundPropensity'])) 
                        for _, row in final_df.iterrows()]
            
            
            if update_data:  # Only proceed if there's data to update
                # Execute update with proper parameter handling
                psycopg2.extras.execute_values(
                    cur,
                    update_sql,
                    update_data,
                    template=None,
                    page_size=1000,
                    fetch=False
                )
                
                # Add the orgid parameter separately
                cur.execute("SELECT 1", (self.message['orgId'],))
                
                conn.commit()
                print(f"Updated {len(update_data)} records")
            else:
                print("No records to update")
            
        except Exception as e:
            print(f"An error occurred: {e}")
            print(f"Update data sample: {update_data[:5] if update_data else 'No data'}")
            conn.rollback()
        finally:
            print("Closing connection")
            cur.close()
            conn.close()

    @timer
    def writeData(self, final_df):
        if final_df.empty:
            print("No changes in metrics detected. No data written.")
            return
        
        print("Updating Postgres with new scores")
        self.update_postgres(final_df)

        # Save to S3
        try:
            s3 = boto3.client('s3')
            parquet_buffer = BytesIO()
            
            # Select and rename columns for parquet file
            output_df = final_df[[
                'identityvalue',
                'TotalOrders',
                'OrdersWithRefunds',
                'TotalAmountRefunded',
                'TotalSpent',
                'refundPropensity',
                'previousrefundPropensity'
            ]].astype({
                'identityvalue': 'string',
                'TotalOrders': 'int',
                'OrdersWithRefunds': 'int',
                'TotalAmountRefunded': 'double',
                'TotalSpent': 'double',
                'refundPropensity': 'double',
                'previousrefundPropensity': 'double'
            })

            output_df['rundate'] = pd.Timestamp.now()
            output_df = output_df.rename(columns={'identityvalue': 'customer'})
            output_df.to_parquet(parquet_buffer, index=False)
            
            if 'CURATED_BUCKET' not in os.environ:
                raise ValueError("CURATED_BUCKET environment variable is not set")
                
            formatted_time = pd.Timestamp.now().strftime('%Y-%m-%d_%H-%M')
            s3_key = f"refund_propensity/organization={self.message['orgId']}/{formatted_time}.parquet"

            print(f"Saving parquet file to S3:", s3_key)
            
            s3.put_object(
                Bucket=os.environ["CURATED_BUCKET"],
                Key=s3_key,
                Body=parquet_buffer.getvalue()
            )
            
            print(f"Saved parquet file to S3: {s3_key}")
            
        except Exception as e:
            print(f"Error saving to S3: {e}")
            raise

    @timer
    def query_athena_and_get_df(self, query_string, columns):
        if 'ATHENA_OUTPUT_BUCKET' not in os.environ:
            raise ValueError("ATHENA_OUTPUT_BUCKET environment variable is not set")
        response = athena_helper.run_athena_query_v2(query_string, os.environ["ATHENA_OUTPUT_BUCKET"])
        query_id = response['QueryExecutionId']

        if athena_helper.wait_for_query_execution(query_id):
            results = athena_helper.get_query_results(query_id)
            rows = [row['Data'] for row in results['ResultSet']['Rows'][1:]]
            df = pd.DataFrame(rows, columns=columns)
            for col in df.columns:
                if df[col].apply(isinstance, args=(dict,)).any():
                    df[col] = df[col].apply(lambda x: self.extract_value_from_dict(x, col))
            return df
        else:
            raise Exception("Query failed!")

    def extract_value_from_dict(self, row, column_name):
        if isinstance(row, dict) and 'VarCharValue' in row:
            value = row['VarCharValue']
            if column_name in ['created_at', 'rundate']:
                return pd.to_datetime(value)
            elif column_name in ['TotalSpent', 'SubtotalSpent', 'TotalAmountRefunded', 'balance', 'refundPropensity']:
                return float(value) if value else 0
            elif column_name in ['TotalOrders', 'OrdersWithRefunds', 'OrdersWithDiscounts', 'earned', 'used']:
                return int(value) if value else 0
            else:
                return value
        return row
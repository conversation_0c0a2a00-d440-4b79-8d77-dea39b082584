from io import BytesIO
import os
import pandas as pd
import numpy as np
import boto3
import time
import athena_helper
import raleon_helper
import psycopg2
import psycopg2.extras
from datetime import datetime, timedelta
from sklearn.model_selection import TimeSeriesSplit
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import GradientBoostingClassifier
from sklearn.metrics import roc_auc_score
from functools import wraps
import logging

def timer(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start = time.time()
        result = func(*args, **kwargs)
        duration = time.time() - start
        print(f"{func.__name__} took {duration:.2f} seconds")
        return result
    return wrapper

class SubscriptionUpsellScorer:
    VERSION = "2.0.0"
    
    def __init__(self):
        self.model = GradientBoostingClassifier(
            n_estimators=100,
            max_depth=3,
            learning_rate=0.1,
            subsample=0.8,
            min_samples_split=50,
            min_samples_leaf=20,
            max_features='sqrt',
            random_state=42
        )
        self.scaler = StandardScaler()
        self.label_encoders = {}
        self.feature_version = self.VERSION
        self.feature_columns = [
            'loyaltysegment_encoded',
            'engagement_30_days',
            'days_since_last_purchase',
            'days_active',
            'total_orders',
            'avg_order_value',
            'purchase_frequency',
            'total_spent_30d',
            'order_count_30d'
        ]
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    def _validate_data_types(self, df: pd.DataFrame) -> pd.DataFrame:
        """Validate and convert DataFrame column types safely"""
        try:
            # Convert numeric columns
            numeric_columns = ['price', 'engagement_30_days']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0.0)
            
            # Handle is_subscriber field specifically
            if 'is_subscriber' in df.columns:
                # Log original values
                self.logger.info("Original is_subscriber values:")
                self.logger.info(df['is_subscriber'].value_counts(dropna=False))
                
                # Clean and standardize is_subscriber values
                df['is_subscriber'] = df['is_subscriber'].fillna('No')
                df['is_subscriber'] = df['is_subscriber'].astype(str).str.strip()
                
                # Map various possible values to standardized form
                yes_values = ['yes', 'y', 'true', '1']
                df['is_subscriber'] = df['is_subscriber'].str.lower().map(
                    lambda x: 'Yes' if x in yes_values else 'No'
                )
                
                # Log cleaned values
                self.logger.info("Cleaned is_subscriber values:")
                self.logger.info(df['is_subscriber'].value_counts())
            
            # Convert date columns
            date_columns = ['date']
            for col in date_columns:
                if col in df.columns:
                    df[col] = pd.to_datetime(df[col], errors='coerce')
            
            return df
        except Exception as e:
            self.logger.error(f"Error in _validate_data_types: {str(e)}")
            raise

    def _fit_transform_categorical(self, series: pd.Series, feature_name: str) -> np.ndarray:
        """
        Fit and transform categorical features, storing encoder for later use.
        """
        self.label_encoders[feature_name] = LabelEncoder()
        return self.label_encoders[feature_name].fit_transform(series)

    def _transform_categorical(self, series: pd.Series, feature_name: str) -> np.ndarray:
        """
        Transform categorical features using stored encoder.
        """
        if feature_name not in self.label_encoders:
            raise ValueError(f"No encoder found for feature: {feature_name}")
            
        known_categories = set(self.label_encoders[feature_name].classes_)
        series = series.map(lambda x: x if x in known_categories else 'Unknown')
        return self.label_encoders[feature_name].transform(series)

    @timer
    def getData(self, message):
        """Fetch data from Athena based on provided queries"""
        self.message = message
        database = os.environ["ATHENA_DB"]
        
        # Original orders query remains the same...
        orders_query = f"""
            WITH aggregated_refunds AS (
                SELECT 
                    order_id, 
                    COALESCE(SUM(refund_amount), 0) AS total_refund_amount  -- Pre-aggregate refund amounts by `order_id`
                FROM 
                    "{database}"."order_refunds"
                WHERE 
                    organization = '{str(message['orgId'])}'
                GROUP BY 
                    order_id
            ),
            aggregated_orders AS (
                SELECT 
                    o.id AS order_id,
                    o.customer AS user,
                    o.created_at AS date,
                    MAX(o.subtotal_price) AS subtotal_price,  -- Aggregate line items at the order level
                    MAX(o.is_subscription) AS is_subscription,  -- If there is any subscription, take `true`
                    ARRAY_AGG(o.item_sku) AS item_ids  -- Aggregate SKUs into an array
                FROM 
                    "{database}"."filtered_orders" o
                WHERE 
                    o.organization = '{str(message['orgId'])}'
                    AND o.customer != '0'
                    AND o.customer NOT IN (
                        SELECT customerId 
                        FROM "{database}".excluded_customers 
                        WHERE organization = '{str(message['orgId'])}'
                    )
                GROUP BY 
                    o.id, o.customer, o.created_at
            )
            SELECT 
                o.user, 
                o.date, 
                o.subtotal_price - COALESCE(r.total_refund_amount, 0) AS price,  -- Subtract refund amounts from subtotal price
                o.is_subscription,
                o.item_ids
            FROM 
                aggregated_orders o
            LEFT JOIN 
                aggregated_refunds r ON o.order_id = r.order_id  -- Join pre-aggregated refunds
            WHERE 
                o.user != '0'
                AND o.user NOT IN (
                    SELECT customerId 
                    FROM "{database}".excluded_customers 
                    WHERE organization = '{str(message['orgId'])}'
                )
            GROUP BY 
                o.user, o.date, o.subtotal_price, r.total_refund_amount, o.is_subscription, o.item_ids;
        """
        
        # Modified customer query to include all fields
        customer_query = f"""
        WITH OrderDiffs AS (
            SELECT
                o.customer AS CustomerID,
                o.created_at,
                LAG(o.created_at) OVER (PARTITION BY o.customer ORDER BY o.created_at) AS PreviousOrderDate
            FROM
                "{database}"."filtered_orders" o
            WHERE
                o.organization = '{str(message['orgId'])}'
        ),
        AvgTimeBetweenPurchases AS (
            SELECT
                CustomerID,
                AVG(EXTRACT(DAY FROM (created_at - PreviousOrderDate))) AS AvgTimeBetweenPurchases
            FROM
                OrderDiffs
            WHERE
                PreviousOrderDate IS NOT NULL
            GROUP BY
                CustomerID
        ),
        LoyaltyTransactions AS (
            SELECT
                ltx.loyaltycurrencybalanceid,
                SUM(CASE WHEN ltx.amount < 0 THEN 1 ELSE 0 END) AS used,
                SUM(CASE WHEN ltx.amount > 0 THEN 1 ELSE 0 END) AS earned
            FROM
                "posgresreadreplicav3"."public"."loyaltycurrencytxlog" ltx
            GROUP BY
                ltx.loyaltycurrencybalanceid
        ),
        LatestEngagement AS (
            SELECT 
                customer,
                engagement_30_days
            FROM (
                SELECT 
                    customer,
                    engagement_30_days,
                    ROW_NUMBER() OVER (PARTITION BY customer ORDER BY rundate DESC) as rn
                FROM "{database}"."engagement"
                WHERE organization = '{str(message['orgId'])}'
            ) ranked
            WHERE rn = 1
        )
        SELECT
            ri.id,
            ri.identityvalue,
            ri.loyaltysegment,
            ri.dayssincelastsubscription,
            COALESCE(e.engagement_30_days, 0) AS engagement_30_days
        FROM
            "posgresreadreplicav3"."public"."raleonuseridentity" ri
        LEFT JOIN
            LatestEngagement e ON ri.identityvalue = e.customer
        WHERE
            ri.orgid = {message['orgId']};
        """
        
        if 'queryOverride' in message and message['queryOverride']:
            orders_query = message['queryOverride'].replace("{orgId}", str(message['orgId']))
            orders_query = orders_query.replace("{database}", database)
            
        orders_df = self.query_athena_and_get_df(
            orders_query, 
            ['user', 'date', 'price', 'is_subscription', 'item_ids']
        )
        orders_df = orders_df[['user', 'date', 'price']]
        
        customer_df = self.query_athena_and_get_df(
            customer_query,
            ['id', 'identityvalue', 'loyaltysegment', 'dayssincelastsubscription', 'engagement_30_days']
        )
        
        # Process customer data to create is_subscriber field
        customer_df['dayssincelastsubscription'] = pd.to_numeric(
            customer_df['dayssincelastsubscription'], 
            errors='coerce'
        ).fillna(-1)
        
        # Create is_subscriber based on business logic
        customer_df['is_subscriber'] = np.where(
            (customer_df['dayssincelastsubscription'] >= 0) & (customer_df['dayssincelastsubscription'] < 60),
            'Yes',
            'No'
        )
        
        # Log detailed distribution information
        self.logger.info("\nDays since last subscription - Basic Stats:")
        self.logger.info(customer_df['dayssincelastsubscription'].describe())
        
        self.logger.info("\nDays since last subscription - Value Counts (top 20):")
        self.logger.info(customer_df['dayssincelastsubscription'].value_counts().head(20))
        
        self.logger.info("\nDays since last subscription - Distribution:")
        bins = [-np.inf, 0, 30, 60, 90, 180, 365, np.inf]
        labels = ['≤0', '1-30', '31-60', '61-90', '91-180', '181-365', '>365']
        customer_df['subscription_bins'] = pd.cut(
            customer_df['dayssincelastsubscription'], 
            bins=bins, 
            labels=labels
        )
        self.logger.info(customer_df['subscription_bins'].value_counts().sort_index())
        
        self.logger.info("\nSubscription status distribution:")
        self.logger.info(customer_df['is_subscriber'].value_counts())
        self.logger.info(f"Percentage subscribers: {(customer_df['is_subscriber'] == 'Yes').mean()*100:.1f}%")
        
        # Validate input data
        self._validate_input_data(customer_df, orders_df)
        
        return customer_df, orders_df

    def _calculate_features(self, orders_df: pd.DataFrame, reference_date: datetime, lookback_days: int = 30) -> tuple:
        """
        Calculate time-based features with proper temporal boundaries.
        """
        historical_orders = orders_df[pd.to_datetime(orders_df['date']) <= reference_date].copy()
        historical_orders['date'] = pd.to_datetime(historical_orders['date'])
        
        # Recent metrics
        lookback_date = reference_date - timedelta(days=lookback_days)
        recent_orders = historical_orders[historical_orders['date'] >= lookback_date]
        
        recent_metrics = recent_orders.groupby('user').agg({
            'price': ['count', 'sum'],
            'date': lambda x: (x.max() - x.min()).days if len(x) > 1 else 0
        })
        recent_metrics.columns = ['order_count_30d', 'total_spent_30d', 'days_span_30d']
        
        # Overall metrics
        overall_metrics = historical_orders.groupby('user').agg({
            'price': ['count', 'mean', 'sum'],
            'date': [
                lambda x: (reference_date - x.max()).days,
                lambda x: (x.max() - x.min()).days if len(x) > 1 else 0
            ]
        })
        overall_metrics.columns = [
            'total_orders', 'avg_order_value', 'total_spent',
            'days_since_last_purchase', 'days_active'
        ]
        
        overall_metrics['purchase_frequency'] = overall_metrics['total_orders'] / \
            np.maximum(overall_metrics['days_active'] / 30, 1)
            
        return recent_metrics, overall_metrics

    def prepare_features(
        self, 
        customer_df: pd.DataFrame, 
        orders_df: pd.DataFrame, 
        reference_date: datetime = None,
        is_training: bool = True
    ) -> pd.DataFrame:
        """
        Prepare features with temporal awareness and proper handling of categorical variables.
        """
        if reference_date is None:
            reference_date = datetime.now()
            
        # Handle categorical features
        if is_training:
            customer_df['loyaltysegment_encoded'] = self._fit_transform_categorical(
                customer_df['loyaltysegment'].fillna('Unknown'),
                'loyaltysegment'
            )
        else:
            customer_df['loyaltysegment_encoded'] = self._transform_categorical(
                customer_df['loyaltysegment'].fillna('Unknown'),
                'loyaltysegment'
            )
            
        recent_metrics, overall_metrics = self._calculate_features(orders_df, reference_date)
        
        # Combine features
        features_df = customer_df[['identityvalue', 'loyaltysegment_encoded', 'engagement_30_days']].copy()
        features_df = features_df.merge(recent_metrics, left_on='identityvalue', right_index=True, how='left')
        features_df = features_df.merge(overall_metrics, left_on='identityvalue', right_index=True, how='left')
        
        # Fill missing values
        features_df = features_df.fillna({
            'order_count_30d': 0,
            'total_spent_30d': 0,
            'days_span_30d': 0,
            'total_orders': 0,
            'avg_order_value': 0,
            'total_spent': 0,
            'days_since_last_purchase': 365,
            'days_active': 0,
            'purchase_frequency': 0
        })
        
        return features_df[self.feature_columns]

    @timer
    def processData(self, data: tuple) -> pd.DataFrame:
        
        try:
            customer_df, orders_df = data
            if customer_df.empty or orders_df.empty:
                print("No data to process")
                return
            if customer_df.shape[0] < 6 or orders_df.shape[0] < 6:
                print(f"Not enough data to train the model. "
                                    f"Customers: {customer_df.shape[0]}, Orders: {orders_df.shape[0]}")
                return None
            reference_date = datetime.now()

            # Train the model first
            self.fit(customer_df, orders_df, reference_date)
            
            # Validate data types early
            customer_df = self._validate_data_types(customer_df)
            orders_df = self._validate_data_types(orders_df)
            
            # Initialize scores array
            scores = np.zeros(len(customer_df))
            
            # Filter for non-subscribers (using string comparison)
            non_subscriber_mask = (customer_df['is_subscriber'].fillna('No').astype(str) != 'Yes')
            
            if non_subscriber_mask.any():
                # Prepare features only for non-subscribers
                non_sub_df = customer_df[non_subscriber_mask].copy()
                X = self.prepare_features(non_sub_df, orders_df, reference_date, is_training=False)
                X_scaled = self.scaler.transform(X)
                
                # Generate predictions
                raw_scores = self.model.predict_proba(X_scaled)[:, 1]
                
                # Convert to 0-1000 scale with adjustments
                base_scores = raw_scores * 800
                
                # Ensure numeric operations
                recent_activity = X['order_count_30d'].astype(float) * 20
                engagement = np.minimum(X['engagement_30_days'].astype(float) / 10, 10) * 10
                
                final_scores = np.clip(base_scores + recent_activity + engagement, 0, 1000).round()
                
                # Assign scores only to non-subscribers
                scores[non_subscriber_mask] = final_scores
            
            # Prepare final DataFrame with integer scores
            final_df = pd.DataFrame({
                'identityvalue': customer_df['identityvalue'],
                'propensity_score': scores.astype(int)
            })
            
            return final_df
            
        except Exception as e:
            self.logger.error(f"Error in processData: {str(e)}")
            raise

    def fit(
        self, 
        customer_df: pd.DataFrame, 
        orders_df: pd.DataFrame,
        reference_date: datetime = None
    ) -> 'SubscriptionUpsellScorer':
        """
        Train the model with proper temporal validation and class balance handling.
        """
        try:
            if reference_date is None:
                reference_date = pd.to_datetime(orders_df['date']).max()
                
            X = self.prepare_features(customer_df, orders_df, reference_date, is_training=True)
            
            # Log raw is_subscriber values before processing
            self.logger.info("Raw is_subscriber value counts:")
            self.logger.info(customer_df['is_subscriber'].value_counts())
            
            # Create and check target variable with case-insensitive comparison
            y = (customer_df['is_subscriber'].fillna('No').str.upper() == 'YES').astype(int)
            
            # Log class distribution
            class_counts = y.value_counts()
            self.logger.info(f"Target class distribution:\n{class_counts}")
            
            if len(class_counts) < 2:
                self.logger.warning("Only one class present in the target variable!")
                # Handle the single-class case
                if class_counts.index[0] == 0:  # If all non-subscribers
                    # Create a dummy subscriber record
                    X = pd.concat([X, X.iloc[0:1]], axis=0)
                    y = pd.concat([y, pd.Series([1])])
                else:  # If all subscribers
                    # Create a dummy non-subscriber record
                    X = pd.concat([X, X.iloc[0:1]], axis=0)
                    y = pd.concat([y, pd.Series([0])])
                
                self.logger.info("Added dummy record to ensure two classes for training")
            
            # Use time series cross-validation
            tscv = TimeSeriesSplit(n_splits=5)
            cv_scores = []

            for train_idx, val_idx in tscv.split(X):
                X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
                y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
                
                # Ensure both classes in training set
                if len(y_train.unique()) < 2:
                    self.logger.warning(f"Skipping fold due to single class in training set")
                    continue
                
                # Scale features
                X_train_scaled = self.scaler.fit_transform(X_train)
                X_val_scaled = self.scaler.transform(X_val)
                
                # Train model
                self.model.fit(X_train_scaled, y_train)
                
                # Evaluate if possible
                if len(y_val.unique()) >= 2:
                    val_proba = self.model.predict_proba(X_val_scaled)[:, 1]
                    cv_scores.append(roc_auc_score(y_val, val_proba))
            
            if cv_scores:
                self.logger.info(f"Cross-validation ROC-AUC scores: {cv_scores}")
                self.logger.info(f"Mean CV ROC-AUC: {np.mean(cv_scores):.3f} (+/- {np.std(cv_scores):.3f})")
            else:
                self.logger.warning("No valid cross-validation scores available")
            
            # Final fit on all data
            X_scaled = self.scaler.fit_transform(X)
            self.model.fit(X_scaled, y)
            
            return self
            
        except Exception as e:
            self.logger.error(f"Error in fit: {str(e)}")
            raise

    @timer
    def writeData(self, final_df):
        """Write results to Postgres and S3"""
        if final_df is None or final_df.empty:
            print("No data to write")
            return

        final_df['propensity_score'] = final_df['propensity_score'].round(0)
        
        # Update Postgres
        self.update_postgres(final_df)
        
        # Create a copy and rename column for S3
        s3_df = final_df.copy()
        s3_df = s3_df.rename(columns={'identityvalue': 'customer'})
        
        # Write to S3 with renamed column
        s3 = boto3.client('s3')
        parquet_buffer = BytesIO()
        s3_df.to_parquet(parquet_buffer, index=False, engine='pyarrow')
        
        if 'CURATED_BUCKET' not in os.environ:
            raise ValueError("CURATED_BUCKET environment variable is not set")
            
        formatted_time = pd.Timestamp.now().strftime('%Y-%m-%d_%H-%M')
        s3.put_object(
            Bucket=os.environ["CURATED_BUCKET"],
            Key=f"subscription_propensity/organization={self.message['orgId']}/{formatted_time}.parquet",
            Body=parquet_buffer.getvalue()
        )

    def update_postgres(self, final_df):
        """Update scores in Postgres database"""
        database_info = raleon_helper.get_database_info()
        conn = psycopg2.connect(
            user=database_info['username'],
            password=database_info['password'],
            host=database_info['host'],
            dbname=database_info['dbname'],
            port=database_info['port']
        )
        
        try:
            cur = conn.cursor()
            update_sql = """
                UPDATE raleonuseridentity 
                SET subscriptionpropensity = data.score,
                    metricsupdated = NOW()
                FROM (VALUES %s) AS data (identityvalue, score)
                WHERE raleonuseridentity.identityvalue = data.identityvalue 
                AND raleonuseridentity.orgid = {0};
            """.format(self.message['orgId'])
            
            update_data = [(str(row['identityvalue']), float(row['propensity_score'])) 
                          for _, row in final_df.iterrows()]
            
            if update_data:
                psycopg2.extras.execute_values(
                    cur,
                    update_sql,
                    update_data,
                    template=None,
                    page_size=1000
                )
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"An error occurred: {e}")
            conn.rollback()
        finally:
            cur.close()
            conn.close()

    def query_athena_and_get_df(self, query_string, columns):
        """Execute Athena query and return results as DataFrame"""
        if 'ATHENA_OUTPUT_BUCKET' not in os.environ:
            raise ValueError("ATHENA_OUTPUT_BUCKET environment variable is not set")
            
        response = athena_helper.run_athena_query_v2(
            query_string, 
            os.environ["ATHENA_OUTPUT_BUCKET"]
        )
        
        query_id = response['QueryExecutionId']
        
        if athena_helper.wait_for_query_execution(query_id):
            results = athena_helper.get_query_results(query_id)
            rows = [row['Data'] for row in results['ResultSet']['Rows'][1:]]
            df = pd.DataFrame(rows, columns=columns)
            
            for col in df.columns:
                if df[col].apply(isinstance, args=(dict,)).any():
                    df[col] = df[col].apply(lambda x: self.extract_value_from_dict(x, col))
            return df
        else:
            raise Exception("Query failed!")

    def extract_value_from_dict(self, row, column_name):
        """Extract values from Athena query results with proper type handling"""
        if not isinstance(row, dict):
            return row
                
        if 'VarCharValue' in row:
            value = row['VarCharValue']
            
            # Handle different column types appropriately
            if column_name == 'date':
                return pd.to_datetime(value)
            elif column_name == 'price':
                try:
                    return float(value)
                except ValueError:
                    return 0.0
            elif column_name == 'engagement_30_days':
                try:
                    return float(value)
                except ValueError:
                    return 0.0
            elif column_name == 'is_subscriber':
                # Keep as string, don't attempt float conversion
                return value
            
            return value
        
        return None
    def _validate_input_data(self, customer_df: pd.DataFrame, orders_df: pd.DataFrame) -> None:
        """
        Validate input data quality and required columns.
        
        Args:
            customer_df: Customer information DataFrame
            orders_df: Order history DataFrame
                
        Raises:
            ValueError: If data quality checks fail
        """
        required_customer_cols = ['id', 'identityvalue', 'loyaltysegment', 'engagement_30_days']
        required_order_cols = ['user', 'date', 'price']
        
        missing_customer_cols = [col for col in required_customer_cols if col not in customer_df.columns]
        missing_order_cols = [col for col in required_order_cols if col not in orders_df.columns]
        
        if missing_customer_cols or missing_order_cols:
            raise ValueError(
                f"Missing required columns. Customer: {missing_customer_cols}, Orders: {missing_order_cols}"
            )
        
        # Check for data quality issues
        if orders_df['date'].isna().any():
            raise ValueError("Order dates contain missing values")
            
        if orders_df['price'].isna().any():
            raise ValueError("Order prices contain missing values")
import json
import os
import sys
import traceback
from datetime import datetime

import boto3

from raleon_helper import query_database, update_last_run, update_last_run_error
from factory import metric_ingestion_factory
import athena_helper

batch_client = boto3.client("batch")


class Handler:
    """Batch-compatible metric-ingestion handler.

    * Reads one metric message from the `METRIC_MESSAGE` env (Batch) or falls
      back to an SQS event payload (Lambda/local test).
    * Processes the metric via factory pattern (getData → processData → writeData).
    * Updates Athena partitions on first run.
    * Submits the *next* metric as a new Batch job using `batch.submit_job()`.
    """

    # ---------------------------------------------------------------------
    # ENTRY POINT ---------------------------------------------------------
    # ---------------------------------------------------------------------
    def main(self, event=None, context=None):
        print(f"Environment variables: METRIC_MESSAGE={os.environ.get('METRIC_MESSAGE')}", file=sys.stderr)
        for message in self._get_inbound_messages(event):
            print("Processing message:", message)
            try:
                processor = metric_ingestion_factory(message["type"])
                raw = processor.getData(message)
                processed = processor.processData(raw)
                processor.writeData(processed)
                update_last_run(message["orgId"], message["name"])

                # update Athena partitions only on first run
                if not message.get("lastRunDate") and message["name"] not in {
                    "klaviyo_campaign_classifier",
                    "customer_io",
                    "hubspot",
                    "klaviyo_referral",
                    "klaviyo_sync_attributes",
                    "rebuy_propensity",
                    "update_metafield",
                }:
                    athena_helper.update_athena_partitions(
                        f"{os.environ['ATHENA_DB']}.{message['name']}",
                        message["orgId"],
                        message["name"],
                        os.environ["CURATED_BUCKET"],
                        os.environ["ATHENA_OUTPUT_BUCKET"],
                    )
                    if(message["name"] == "klaviyo_flows"):
                        athena_helper.update_athena_partitions(
                        f"{os.environ['ATHENA_DB']}.klaviyo_flows_series",
                        message["orgId"],
                        'klaviyo_flows_series',
                        os.environ["CURATED_BUCKET"],
                        os.environ["ATHENA_OUTPUT_BUCKET"],
                    )
            except Exception:
                err = traceback.format_exc()
                print("\n💥 ERROR processing metric:\n", err)
                update_last_run_error(message["orgId"], message["name"], err)

            # enqueue next metric
            self._submit_next_metric(message)

        print("✔️  Batch metric-ingestion job complete")

    # ------------------------------------------------------------------
    # INTERNALS ---------------------------------------------------------
    # ------------------------------------------------------------------
    def _get_inbound_messages(self, event):
        """Return a list[dict] with exactly one metric payload."""
        # Batch path: get complete metric data from database
        msg_env = os.getenv("METRIC_MESSAGE")
        if msg_env:
            message = json.loads(msg_env)
            if not isinstance(message, dict):
                message = json.loads(message)
            
            if 'orgId' in message and 'metricId' in message:
                rows = query_database(
                    """
                    SELECT 
                        m.id, m.name, m.metricsegmentid, m.type, m.query, m.variables,
                        m.catalog, m.datastructure, m.defaultrunfrequency, m.priority,
                        om.orgId, om.metricId, om.variableOverride,
                        om.lastRunDate, om.runFrequency, ms.name AS ms_name,
                        ms.query AS ms_query, ms.variables AS ms_variables,
                        lp.activationdate, om.queryOverride,
                        m.fieldMappings
                    FROM Metric m
                    LEFT JOIN OrganizationMetric om ON m.id = om.metricId
                    LEFT JOIN MetricSegment ms ON m.metricsegmentId = ms.id
                    LEFT JOIN loyaltyprogram lp ON om.orgId = lp.orgid
                    WHERE om.orgId = %s AND m.id = %s
                    """, 
                    (message['orgId'], message['metricId'])
                )
                if not rows:
                    raise RuntimeError(f"No metric found for orgId {message['orgId']} and metricId {message['metricId']}")
                    
                row = rows[0]
                return [{
                    "orgId": row[10],
                    "query": row[4] or "",
                    "catalog": row[6] or "AWSDataCatalog",
                    "variables": self._safe_json(row[5] or ""),
                    "name": row[1],
                    "type": row[3] or "default",
                    "segment": row[15] or "",
                    "dataStructure": self._safe_json(row[7] or ""),
                    "lastRunDate": row[13].isoformat() if row[13] else None,
                    "activationDate": row[18].isoformat() if row[18] else None,
                    "priority": row[9] or 0,
                    "queryOverride": row[19] or "",
                    "fieldMappings": row[20] or "",
                    "segment": row[15] or "",
                    "queue": message['queue']
                }]
            # Full message provided, process as before
            return [{
                "orgId": message.get("orgId"),
                "query": message.get("query", ""),
                "catalog": message.get("catalog", "AWSDataCatalog"),
                "variables": self._safe_json(message.get("variables", "")),
                "name": message.get("name", ""),
                "type": message.get("type", "default"),
                "segment": message.get("segment", ""),
                "dataStructure": self._safe_json(message.get("dataStructure", "")),
                "lastRunDate": message.get("lastRunDate", ""),
                "activationDate": message.get("activationDate", ""),
                "priority": message.get("priority", 0),
                "queryOverride": message.get("queryOverride", ""),
                "fieldMappings": message.get("fieldMappings", ""),
                "segment": message.get("segment", ""),
                "queue": message.get("queue", os.environ["BATCH_JOB_QUEUE"])
            }]

        # Lambda/SQS path 
        if event and isinstance(event, dict) and "Records" in event:
            return [
                self._sqs_body_to_metric(json.loads(rec["body"]))
                for rec in event["Records"]
            ]
        raise RuntimeError("No metric supplied via METRIC_MESSAGE or event. Aborting.")

    @staticmethod
    def _sqs_body_to_metric(body):
        """Map legacy SQS body → metric dict."""
        return {
            "orgId": body.get("orgId"),
            "query": body.get("query", ""),
            "catalog": body.get("catalog", "AWSDataCatalog"),
            "variables": Handler._safe_json(body.get("variables", "")),
            "name": body.get("name", ""),
            "type": body.get("type", "default"),
            "segment": body.get("segment", ""),
            "dataStructure": Handler._safe_json(body.get("dataStructure", "")),
            "lastRunDate": body.get("lastRunDate", ""),
            "activationDate": body.get("activationDate", ""),
            "priority": body.get("priority", 0),
            "queryOverride": body.get("queryOverride", ""),
            "fieldMappings": body.get("fieldMappings", ""),
            "segment": body.get("segment", ""),
            "queue": body.get("queue", os.environ["BATCH_JOB_QUEUE"])
        }

    # -------------------- NEXT-METRIC PIPELINE ------------------------
    def _submit_next_metric(self, message):
        # Find next metric with minimal data
        rows = query_database(self._next_metric_sql(), (message['orgId'], message['priority']))
        filtered = self._filter_rows(rows)
        if not filtered:
            print("No subsequent metrics to queue.")
            return

        # Only send minimal data needed to identify the metric
        next_msg = {
            'orgId': message['orgId'],
            'metricId': filtered[0][0],
            'queue': message['queue']
        }
        batch_client.submit_job(
            jobName=f"metric-{filtered[0][1]}-{message['orgId']}",  # using metric.name for job name
            jobQueue=message["queue"],
            jobDefinition=os.environ["BATCH_JOB_DEFINITION"],
            containerOverrides={
                "environment": [
                    {"name": "METRIC_MESSAGE", "value": json.dumps(next_msg)}
                ]
            },
        )
        print("Queued follow-up metric → Batch")

    # -------------------- BUSINESS LOGIC HELPERS ---------------------
    @staticmethod
    def _next_metric_sql():
        return """
            SELECT
                m.id, m.name, m.metricsegmentid, m.type, m.query, m.variables,
                m.catalog, m.datastructure, m.defaultrunfrequency, m.priority,
                om.orgId, om.metricId, om.variableOverride AS om_variableOverride,
                om.lastRunDate, om.runFrequency, ms.name AS ms_name,
                ms.query AS ms_query, ms.variables AS ms_variables,
                lp.activationdate, om.queryOverride AS om_queryOverride,
                m.fieldMappings AS fieldMappings
            FROM Metric m
            LEFT JOIN OrganizationMetric om  ON m.id = om.metricId
            LEFT JOIN MetricSegment ms       ON m.metricsegmentId = ms.id
            LEFT JOIN loyaltyprogram lp      ON om.orgId = lp.orgid
            WHERE om.orgId = %s
              AND m.priority > %s
              AND (DATE(om.lastRunDate) < CURRENT_DATE OR om.lastRunDate IS NULL)
            ORDER BY m.priority ASC
        """

    @staticmethod
    def _filter_rows(rows):
        out = []
        now = datetime.now().replace(tzinfo=None)
        for r in rows:
            last_run = r[13]
            if not last_run:
                out.append(r); continue
            if isinstance(last_run, datetime):
                last_run = last_run.replace(tzinfo=None)
            else:
                last_run = datetime.strptime(last_run, "%Y-%m-%d").replace(tzinfo=None)

            run_frequency = r[14]
            if run_frequency == "day" and now.date() > last_run.date():
                out.append(r)
            elif run_frequency == "week" and (now - last_run).days >= 7:
                out.append(r)
            elif run_frequency == "monday" and now.weekday() == 0:
                out.append(r)
            elif run_frequency == "month" and (now.month != last_run.month or now.year != last_run.year):
                out.append(r)
            elif run_frequency == "year" and now.year != last_run.year:
                out.append(r)
        return out

    @staticmethod
    def _safe_json(val):
        if isinstance(val, dict):
            return val
        try:
            parsed = json.loads(val)
            if isinstance(parsed, str):
                parsed = json.loads(parsed)
            return parsed
        except Exception:
            return {}



handler = Handler()
main = handler.main  # AWS Batch entry point
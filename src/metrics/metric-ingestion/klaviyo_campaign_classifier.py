import os
import json
import pandas as pd
import boto3
import athena_helper
from datetime import datetime
from io import BytesIO
import asyncio
import aioboto3

class KlaviyoCampaignClassifier:
    def __init__(self):
        self.s3_client = boto3.client('s3')
        self.lambda_client = boto3.client('lambda')
        self.message = None
        self.session = aioboto3.Session()

    def getData(self, message):
        """Fetch campaign data from Athena"""
        self.message = message
        database = os.environ["ATHENA_DB"]
        
        query = f"""
        SELECT 
            id, 
            type, 
            name, 
            status, 
            archived, 
            audiences_included, 
            audiences_excluded, 
            send_options_use_smart_sending, 
            send_options_ignore_unsubscribes, 
            tracking_options_add_tracking_params, 
            tracking_options_custom_tracking_params, 
            tracking_options_is_tracking_clicks, 
            tracking_options_is_tracking_opens, 
            send_strategy_method, 
            send_strategy_options_static_datetime, 
            send_strategy_options_static_is_local, 
            send_strategy_options_static_send_past_recipients_immediately, 
            send_strategy_options_throttled, 
            send_strategy_options_sto, 
            created_at, 
            scheduled_at, 
            updated_at, 
            send_time, 
            messages, 
            stats_average_order_value, 
            stats_bounce_rate, 
            stats_bounced, 
            stats_bounced_or_failed, 
            stats_bounced_or_failed_rate, 
            stats_click_rate, 
            stats_click_to_open_rate, 
            stats_clicks, 
            stats_clicks_unique, 
            stats_conversion_rate, 
            stats_conversion_uniques, 
            stats_conversion_value, 
            stats_conversions, 
            stats_delivered, 
            stats_delivery_rate, 
            stats_failed, 
            stats_failed_rate, 
            stats_open_rate, 
            stats_opens, 
            stats_opens_unique, 
            stats_recipients, 
            stats_revenue_per_recipient, 
            stats_spam_complaint_rate, 
            stats_spam_complaints, 
            stats_unsubscribe_rate, 
            stats_unsubscribe_uniques, 
            stats_unsubscribes, 
            send_strategy_options_static, 
            rundate, 
            send_strategy_options_sto_date, 
            tracking_options_is_add_utm, 
            tracking_options_utm_params, 
            send_strategy_options_throttled_datetime, 
            send_strategy_options_throttled_throttle_percentage
        FROM "{database}"."klaviyo_campaigns"
        WHERE organization = '{message['orgId']}'
        ORDER BY created_at DESC
        """
        
        columns = ['id', 
            'type', 
            'name', 
            'status', 
            'archived', 
            'audiences_included', 
            'audiences_excluded', 
            'send_options_use_smart_sending', 
            'send_options_ignore_unsubscribes', 
            'tracking_options_add_tracking_params', 
            'tracking_options_custom_tracking_params', 
            'tracking_options_is_tracking_clicks', 
            'tracking_options_is_tracking_opens', 
            'send_strategy_method', 
            'send_strategy_options_static_datetime', 
            'send_strategy_options_static_is_local', 
            'send_strategy_options_static_send_past_recipients_immediately', 
            'send_strategy_options_throttled', 
            'send_strategy_options_sto', 
            'created_at', 
            'scheduled_at', 
            'updated_at', 
            'send_time', 
            'messages', 
            'stats_average_order_value', 
            'stats_bounce_rate', 
            'stats_bounced', 
            'stats_bounced_or_failed', 
            'stats_bounced_or_failed_rate', 
            'stats_click_rate', 
            'stats_click_to_open_rate', 
            'stats_clicks', 
            'stats_clicks_unique', 
            'stats_conversion_rate', 
            'stats_conversion_uniques', 
            'stats_conversion_value', 
            'stats_conversions', 
            'stats_delivered', 
            'stats_delivery_rate', 
            'stats_failed', 
            'stats_failed_rate', 
            'stats_open_rate', 
            'stats_opens', 
            'stats_opens_unique', 
            'stats_recipients', 
            'stats_revenue_per_recipient', 
            'stats_spam_complaint_rate', 
            'stats_spam_complaints', 
            'stats_unsubscribe_rate', 
            'stats_unsubscribe_uniques', 
            'stats_unsubscribes', 
            'send_strategy_options_static', 
            'rundate', 
            'send_strategy_options_sto_date', 
            'tracking_options_is_add_utm', 
            'tracking_options_utm_params', 
            'send_strategy_options_throttled_datetime', 
            'send_strategy_options_throttled_throttle_percentage']
                  
        return self.query_athena_and_get_df(query, columns)

    async def process_batch(self, batch):
        """Process a single batch of campaigns using Lambda"""
        try:
            async with self.session.client('lambda') as lambda_client:
                lambda_request = {
                    "messages": [{
                        "role": "user",
                        "content": f"""
                        Analyze these email campaigns and classify each one into exactly one category:
                        - Promotional (sales, discounts, special offers, deals)
                        - Educational (how-tos, guides, tips, product education)
                        - Awareness (brand stories, announcements, newsletters)

                        Campaigns to analyze: {json.dumps(batch)}

                        Return only a JSON object with campaign IDs as keys and classification category as values.
                        Classify ALL CAMPAIGNS in the batch.
                        """
                    }],
                    "params": {
                        "models": ["anthropic/claude-3.5-sonnet"]
                    }
                }

                response = await lambda_client.invoke(
                    FunctionName=os.environ['LLM_ROUTER_LAMBDA'],
                    InvocationType='RequestResponse',
                    Payload=json.dumps({"body": json.dumps(lambda_request)})
                )

                if response.get('FunctionError'):
                    raise Exception(f"Lambda function error: {response.get('FunctionError')}")

                response_payload = json.loads(await response['Payload'].read())
                if 'body' in response_payload:
                    response_content = json.loads(response_payload['body'])
                    if 'content' in response_content:
                        return json.loads(response_content['content'])
                    
                raise Exception("No content field in response")
                
        except Exception as e:
            print(f"Error processing batch: {str(e)}")
            return {campaign['id']: 'Unknown' for campaign in batch}

    def processData(self, df):
        if df.empty:
            print("No campaigns found")
            raise ValueError("DataFrame is empty, no campaigns to classify")

        # Parse messages JSON column and extract email content
        def extract_email_content(messages_str):
            try:
                messages = json.loads(messages_str)
                if messages and len(messages) > 0:
                    message = messages[0]
                    content = message.get('attributes', {}).get('content', {})
                    return {
                        'subject': content.get('subject', ''),
                        'preview_text': content.get('preview_text', ''),
                    }
            except:
                return {'subject': '', 'preview_text': ''}

        # Only process messages if the column exists and has data
        if 'messages' in df.columns and not df['messages'].isna().all():
            # Create a list of dictionaries, ensuring no None values
            email_contents = []
            for messages_str in df['messages']:
                content = extract_email_content(messages_str)
                if content is None:
                    content = {'subject': '', 'preview_text': ''}
                email_contents.append(content)
            
            email_content = pd.DataFrame(email_contents)
            df = pd.concat([df, email_content], axis=1)
        else:
            df['subject'] = ''
            df['preview_text'] = ''

        # Prepare data for LLM classification
        campaigns_to_classify = []
        for _, row in df.iterrows():
            campaigns_to_classify.append({
                'id': row['id'],
                'name': row['name'],
                'subject': row['subject'],
                'preview_text': row['preview_text']
            })

        # Process batches in parallel
        batch_size = 50
        all_classifications = {}
        
        async def process_all_batches():
            tasks = []
            for i in range(0, len(campaigns_to_classify), batch_size):
                batch = campaigns_to_classify[i:i + batch_size]
                print(f"Queuing batch {i//batch_size + 1} of {(len(campaigns_to_classify) + batch_size - 1)//batch_size}")
                tasks.append(self.process_batch(batch))
            
            # Run all batches concurrently with a limit of 5 concurrent executions
            for batch_results in asyncio.as_completed(tasks):
                batch_classifications = await batch_results
                all_classifications.update(batch_classifications)
                print(f"Completed batch, current classifications: {len(all_classifications)}")

        # Run async processing
        asyncio.run(process_all_batches())

        # Add classifications to dataframe
        print(f"Final classifications: {all_classifications}")
        df['campaign_type'] = df['id'].map(lambda x: all_classifications.get(x, 'Unknown'))
        
        print("\nClassification distribution:")
        print(df['campaign_type'].value_counts())
        
        return df

    def writeData(self, df):
        if df.empty:
            print("No data to write")
            return

        # Delete existing files in the organization's directory
        prefix = f"klaviyo_campaigns/organization={self.message['orgId']}/"
        print(f"Deleting existing files with prefix: {prefix}")
        
        # List and delete existing objects
        paginator = self.s3_client.get_paginator('list_objects_v2')
        for page in paginator.paginate(Bucket=os.environ["CURATED_BUCKET"], Prefix=prefix):
            if 'Contents' in page:
                for obj in page['Contents']:
                    print(f"Deleting {obj['Key']}")
                    self.s3_client.delete_object(
                        Bucket=os.environ["CURATED_BUCKET"],
                        Key=obj['Key']
                    )

        # Convert DataFrame to parquet
        parquet_buffer = BytesIO()
        df.to_parquet(parquet_buffer, index=False)
        
        # Write new file to S3
        formatted_time = datetime.now().strftime('%Y-%m-%d_%H-%M')
        s3_key = f"{prefix}{formatted_time}.parquet"
        
        self.s3_client.put_object(
            Bucket=os.environ["CURATED_BUCKET"],
            Key=s3_key,
            Body=parquet_buffer.getvalue()
        )
        
        print(f"Written classified campaigns to s3://{os.environ['CURATED_BUCKET']}/{s3_key}")

    def query_athena_and_get_df(self, query_string, columns):
        if 'ATHENA_OUTPUT_BUCKET' not in os.environ:
            raise ValueError("ATHENA_OUTPUT_BUCKET environment variable is not set")
            
        response = athena_helper.run_athena_query_v2(query_string, os.environ["ATHENA_OUTPUT_BUCKET"])
        query_id = response['QueryExecutionId']

        if athena_helper.wait_for_query_execution(query_id):
            results = athena_helper.get_query_results(query_id)
            rows = [row['Data'] for row in results['ResultSet']['Rows'][1:]]
            df = pd.DataFrame(rows, columns=columns)
            
            for col in df.columns:
                if df[col].apply(isinstance, args=(dict,)).any():
                    df[col] = df[col].apply(lambda x: self.extract_value_from_dict(x, col))
            return df
        else:
            raise Exception("Query failed!")

    def extract_value_from_dict(self, row, column_name):
        if not isinstance(row, dict):
            return row
            
        if 'VarCharValue' in row:
            value = row['VarCharValue']
            
            # Handle null/empty values
            if value is None or value == '':
                return None
                
            # Handle different column types
            if column_name == 'rundate':
                return pd.to_datetime(value)
            elif value.lower() == 'true' or value.lower() == 'false':
                return value.lower() == 'true'
            elif column_name.startswith('stats_') or column_name.endswith('percentage') or column_name == 'send_strategy_options_static' or column_name == 'send_strategy_options_sto' or column_name == 'send_strategy_options_throttled':
                return float(value) if value else 0.0
                
            return value
        
        return None

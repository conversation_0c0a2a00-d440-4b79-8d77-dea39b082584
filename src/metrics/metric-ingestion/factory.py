from loyalty_segment import LoyaltySegment
from default_metric import DefaultMetric
from default_postgres_metric import DefaultPostgresMetric
from loyalty_points import LoyaltyPoints
from customer_io import CustomerIo
from hubspot import <PERSON><PERSON><PERSON>
from update_metafield import UpdateMetaField
from excluded_customers import ExcludedCustomers
from klaviyo_referral import KlaviyoReferral
from refund_propensity import RefundPropensityScorer
from churn_risk import ChurnRiskScorer
from replenish_score import ReplenishmentSeeker
from user_ltv_revenue import UserLTVRevenue
from rebuy_propensity import PurchasePropensityScorer
from subscription_upsell import SubscriptionUpsellScorer
from shop_orders_variants import ShopOrdersFitAnalysis
from excluded_orders import ExcludedOrders
from product_sequence_analysis import ProductSequenceAnalysis
from klaviyo_data_pull import KlaviyoData
from klaviyo_campaign_classifier import KlaviyoCampaignClassifier

def metric_ingestion_factory(type_str):
    metric_processors = {
        "loyalty-segment": LoyaltySegment,
        "loyalty-points": LoyaltyPoints,
        "customer-io": CustomerIo,
        "hubspot": <PERSON><PERSON><PERSON>,
        "update-metafield": UpdateMetaField,
        "excluded-customers": ExcludedCustomers,
        "excluded-orders": ExcludedOrders,
        "klaviyo-referral": KlaviyoReferral,
        "refund-propensity": RefundPropensityScorer,
        "churn-risk": ChurnRiskScorer,
        "replenish-score": ReplenishmentSeeker,
        "user-ltv-revenue": UserLTVRevenue,
        "rebuy-propensity": PurchasePropensityScorer,
        "default-postgres": DefaultPostgresMetric,
        "subscription-propensity": SubscriptionUpsellScorer,
        "shop-orders-variants": ShopOrdersFitAnalysis,
        "product-sequence": ProductSequenceAnalysis,
        "klaviyo-data": KlaviyoData,
        "klaviyo-campaign-classifier": KlaviyoCampaignClassifier,
        "default": DefaultMetric
    }
    
    processor_class = metric_processors.get(type_str)
    if not processor_class:
        raise ValueError(f"No processor found for type: {type_str}")
    
    return processor_class()

from io import BytesIO
import os
import pandas as pd
import boto3
import psycopg2
import athena_helper
from shopify_helper import get_shop_info_by_org_id, get_api_access_token
import raleon_helper
import requests
import json

class ShopOrdersFitAnalysis:
    def __init__(self):
        self.shop_info = None
        self.session_token = None
        self.message = None

    def initialize_shop_connection(self, org_id):
        """Initialize shop connection with org_id"""
        self.shop_info = get_shop_info_by_org_id(org_id)
        self.session_token = get_api_access_token(self.shop_info['shopDomain'])

    def get_product_details(self, product_ids):
        """Fetch product details and tags from Shopify GraphQL API"""
        print(f"Fetching details for {len(product_ids)} products")
        
        # GraphQL query for batch fetching product details
        query = """
        query getProductDetails($productIds: [ID!]!) {
          products: nodes(ids: $productIds) {
            ... on Product {
              id
              title
              tags
            }
          }
        }
        """
        
        # Convert IDs to Shopify Global IDs
        shopify_product_ids = [f"gid://shopify/Product/{pid}" for pid in product_ids if pid]
        
        # Split into chunks of 50 to avoid query complexity limits
        chunk_size = 50
        all_products = []
        
        # Process in chunks
        for i in range(0, len(shopify_product_ids), chunk_size):
            product_chunk = shopify_product_ids[i:i + chunk_size]
            
            variables = {
                "productIds": product_chunk
            }
            
            headers = {
                "X-Shopify-Access-Token": self.session_token,
                "Content-Type": "application/json",
            }
            
            response = requests.post(
                f"https://{self.shop_info['shopDomain']}/admin/api/2024-10/graphql.json",
                json={"query": query, "variables": variables},
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                if 'data' in data and 'products' in data['data']:
                    products = [p for p in data['data']['products'] if p]
                    all_products.extend(products)
            else:
                print(f"Error fetching products: {response.text}")
        
        # Process product data into a DataFrame
        product_data = []
        products_with_fit = 0
        total_products = len(all_products)
        
        for product in all_products:
            if product:  # Check if product exists
                product_id = product['id'].split('/')[-1]  # Extract numeric ID
                
                # Extract and analyze fit information from tags
                fit_info = next(
                    (tag.split(':')[1].strip() for tag in product['tags']
                     if tag.lower().startswith('fit:')),
                    None
                )
                
                # Debug logging for tag analysis
                if fit_info:
                    products_with_fit += 1
                else:
                    print(f"\nProduct {product['title']} has no 'fit:' tag. Available tags:")
                    print(json.dumps(product['tags'], indent=2))
                
                product_data.append({
                    'item_id': product_id,
                    'product_title': product['title'],
                    'fit_type': fit_info,
                    'product_tags': json.dumps(product['tags'])
                })
        
        print("\nProduct Tag Analysis Summary:")
        print(f"Total products: {total_products}")
        print(f"Products with 'fit:' tag: {products_with_fit}")
        print(f"Products missing 'fit:' tag: {total_products - products_with_fit}")
        print(f"Percentage with 'fit:' tag: {(products_with_fit / total_products * 100):.2f}%")
        
        return pd.DataFrame(product_data)

    def getData(self, message):
        if not isinstance(message, dict):
            raise ValueError("Message must be a dictionary")
            
        required_fields = ['name', 'orgId']
        missing_fields = [field for field in required_fields if field not in message]
        if missing_fields:
            raise ValueError(f"Missing required fields in message: {', '.join(missing_fields)}")
            
        self.message = {
            'name': str(message.get('name')),
            'orgId': str(message.get('orgId'))
        }
        
        # Initialize shop connection
        self.initialize_shop_connection(self.message['orgId'])
        
        database = os.environ["ATHENA_DB"]
        
        # Get all order line items
        query_string = f"""
            SELECT
                id,
                created_at,
                customer,
                item_price as total_price,
                item_quantity,
                item_id,
                member,
                item_name,
                item_sku
            FROM
                "{database}".filtered_orders
            WHERE
                organization = '{self.message['orgId']}'
                AND item_id IS NOT NULL
                AND cancelled_at IS NULL
            ORDER BY
                created_at DESC;
        """
        
        columns = [
            'id', 'created_at', 'customer', 'total_price',
            'item_quantity', 'item_id', 'member', 'item_name', 'item_sku'
        ]
        
        # Get orders data
        orders_df = self.query_athena_and_get_df(query_string, columns)
        
        # Get unique product IDs
        unique_products = orders_df['item_id'].dropna().unique()
        print(f"Found {len(unique_products)} unique products")
        products_df = self.get_product_details(unique_products)
        
        # Merge orders with product details
        merged_df = orders_df.merge(
            products_df,
            on='item_id',
            how='left'
        )
        
        return merged_df

    def processData(self, df):
        # Convert numeric columns
        numeric_columns = [
            'total_price', 'item_quantity'
        ]
        for col in numeric_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Convert datetime columns
        datetime_columns = ['created_at']
        for col in datetime_columns:
            df[col] = pd.to_datetime(df[col], errors='coerce')
        
        # Convert boolean columns
        boolean_columns = ['member']
        for col in boolean_columns:
            df[col] = df[col].map({'true': True, 'false': False})
        
        # Add analysis columns
        df['has_fit_info'] = df['fit_type'].notna()
        
        # Calculate top products analysis - group by product first to get total unique buyers
        product_analysis = df.groupby(['item_id', 'product_title']).agg({
            'id': 'count',  # Number of orders
            'item_quantity': 'sum',  # Total quantity sold
            'total_price': 'sum',  # Total revenue
            'customer': 'nunique',  # Unique buyers
            'fit_type': lambda x: x.mode().iloc[0] if not x.mode().empty else None  # Most common fit type
        }).reset_index()

        # Rename columns for clarity
        product_analysis.columns = ['item_id', 'product_title', 'order_count',
                            'total_sold', 'total_revenue', 'unique_buyers',
                            'fit_type']

        # Sort by total revenue to get top products
        top_products = product_analysis.sort_values('total_revenue', ascending=False)

        # Print top products summary
        print("\nTop 10 Products by Revenue:")
        print(top_products.head(10).to_string(index=False))

        # Print top products without fit tags
        products_no_fit = top_products[top_products['fit_type'].isna()].copy()
        print("\nTop 10 Products by Revenue (No Fit Tag):")
        print(products_no_fit.head(10).to_string(index=False))

        # Calculate purchase counts and metrics per fit type per user
        fit_purchases = df.groupby(['customer', 'fit_type']).agg({
            'id': 'count',  # Count of orders
            'item_quantity': 'sum',  # Total items purchased
            'total_price': 'sum',  # Total spent
            'created_at': ['min', 'max']  # First and last purchase dates
        }).reset_index()
        
        # Rename columns for clarity
        fit_purchases.columns = ['customer', 'fit_type', 'order_count', 'total_items',
                               'total_spent', 'first_purchase', 'last_purchase']
        
        # Calculate recency weight (more recent purchases get higher weight)
        current_date = pd.Timestamp.now()
        fit_purchases['days_since_last_purchase'] = (current_date - fit_purchases['last_purchase']).dt.days
        fit_purchases['recency_weight'] = 1 / (1 + 0.01 * fit_purchases['days_since_last_purchase'])
        
        # Calculate customer totals for percentages
        customer_totals = fit_purchases.groupby('customer').agg({
            'order_count': 'sum',
            'total_items': 'sum',
            'total_spent': 'sum'
        }).reset_index()
        
        # Merge totals back to calculate percentages
        fit_purchases = fit_purchases.merge(customer_totals,
                                          on='customer',
                                          suffixes=('', '_total'))
        
        # Calculate percentage metrics
        fit_purchases['order_percentage'] = fit_purchases['order_count'] / fit_purchases['order_count_total']
        fit_purchases['items_percentage'] = fit_purchases['total_items'] / fit_purchases['total_items_total']
        fit_purchases['spent_percentage'] = fit_purchases['total_spent'] / fit_purchases['total_spent_total']
        
        # Calculate confidence score (0-100)
        fit_purchases['confidence_score'] = (
            # Base confidence from purchase frequency (40%)
            (40 * fit_purchases['order_percentage']) +
            # Items purchased weight (20%)
            (20 * fit_purchases['items_percentage']) +
            # Spending pattern weight (20%)
            (20 * fit_purchases['spent_percentage']) +
            # Recency weight (20%)
            (20 * fit_purchases['recency_weight'])
        )
        
        # Determine primary fit preference (highest confidence score)
        primary_preferences = fit_purchases.loc[
            fit_purchases.groupby('customer')['confidence_score'].idxmax()
        ][['customer', 'fit_type', 'confidence_score']]
        primary_preferences.columns = ['customer', 'primary_fit_preference', 'preference_confidence']

        # Print user fit preference analysis
        print("\nUser Fit Preference Analysis:")
        clear_fit_users = primary_preferences[primary_preferences['preference_confidence'] > 50]
        unclear_fit_users = primary_preferences[primary_preferences['preference_confidence'] <= 50]
        
        print(f"\nUsers with Clear Fit Preferences (Confidence > 50):")
        fit_type_counts = clear_fit_users.groupby('primary_fit_preference').size()
        for fit_type, count in fit_type_counts.items():
            print(f"{fit_type}: {count} users")
        print(f"Total users with clear fit: {len(clear_fit_users)}")
        print(f"Total users without clear fit: {len(unclear_fit_users)}")
        print(f"Percentage of users with clear fit: {(len(clear_fit_users) / len(primary_preferences) * 100):.2f}%")

        # Print detailed analysis
        print("\nFit Type Purchase Analysis:")
        summary = fit_purchases.groupby('fit_type').agg({
            'order_count': 'sum',
            'total_items': 'sum',
            'total_spent': 'sum',
            'customer': 'nunique',  # Count unique customers
            'confidence_score': ['mean', 'max']
        })
        summary.columns = ['total_orders', 'total_items', 'total_spent',
                          'unique_customers', 'avg_confidence', 'max_confidence']
        print(summary)
        
        # Print top customers for each fit type
        print("\nTop Customers by Fit Type (Confidence > 50):")
        high_confidence = fit_purchases[fit_purchases['confidence_score'] > 50]
        for fit_type in high_confidence['fit_type'].unique():
            fit_customers = high_confidence[high_confidence['fit_type'] == fit_type]
            print(f"\n{fit_type}:")
            print(fit_customers[['customer', 'confidence_score', 'order_count', 'total_spent']]
                  .sort_values('confidence_score', ascending=False)
                  .head(5)
                  .to_string(index=False))

        # Add top products analysis to the main dataframe
        df = df.merge(product_analysis.add_prefix('product_'),
                     left_on=['item_id', 'product_title', 'fit_type'],
                     right_on=['product_item_id', 'product_product_title', 'product_fit_type'],
                     how='left')
        
        # Calculate unique customers per fit type
        unique_customers = fit_purchases.groupby('fit_type')['customer'].nunique().reset_index()
        unique_customers.columns = ['fit_type', 'unique_customers']
        
        # Add the purchase counts and unique customers back to the main dataframe
        df = df.merge(fit_purchases, on=['customer', 'fit_type'], how='left')
        df = df.merge(unique_customers, on='fit_type', how='left')
        
        df['rundate'] = pd.Timestamp.now()
        
        return df

    def writeData(self, final_df):
        if final_df.empty:
            print("No data to write.")
            return
        
        
        # Select only required columns and ensure correct types
        output_df = final_df[['customer', 'fit_type']].copy()
        
        # Ensure customer and fit_type are strings
        output_df['customer'] = output_df['customer'].astype(str)
        output_df['fit_type'] = output_df['fit_type'].fillna('unknown').astype(str)
        
        # Group by customer and get first entry (since we removed confidence score sorting)
        output_df = output_df.groupby('customer').first().reset_index()
        
        # Add organization and rundate
        output_df['organization'] = str(self.message['orgId'])
        # Add rundate as timestamp
        output_df['rundate'] = pd.Timestamp.now()
            
        # Print preview of data being written to parquet
        print("\nPreview of data being written to parquet (top 10 rows):")
        pd.set_option('display.max_columns', None)
        print(output_df.head(10).to_string(index=False))
        print(f"\nTotal number of unique customers: {len(output_df)}")
            
        s3 = boto3.client('s3')
        print("\nWriting data to parquet in S3")
        
        parquet_buffer = BytesIO()
        output_df.to_parquet(
            parquet_buffer,
            index=False,
            engine='pyarrow'
        )
        
        if 'CURATED_BUCKET' not in os.environ:
            raise ValueError("CURATED_BUCKET environment variable is not set")
            
        # Reset buffer position to beginning before reading
        parquet_buffer.seek(0)
        
        formatted_time = pd.Timestamp.now().strftime('%Y-%m-%d_%H-%M')
        s3_key = f"custom_donahue_fit/organization={self.message['orgId']}/{formatted_time}.parquet"
        print(f"Writing to S3 bucket: {os.environ['CURATED_BUCKET']}, key: {s3_key}")
        
        try:
            s3.put_object(
                Bucket=os.environ["CURATED_BUCKET"],
                Key=s3_key,
                Body=parquet_buffer.getvalue()
            )
            print("Successfully wrote parquet file to S3")
        except Exception as e:
            print(f"Error writing to S3: {str(e)}")
            raise

        print("Updating Postgres with new scores")
        self.update_postgres(output_df)

    def update_postgres(self, final_df):
        print("Updating Postgres")
        database_info = raleon_helper.get_database_info()
        conn = psycopg2.connect(
            user=database_info['username'],
            password=database_info['password'],
            host=database_info['host'],
            dbname=database_info['dbname'],
            port=database_info['port']
        )
        
        try:
            cur = conn.cursor()
            print("Connected to database")
            
            # First, update metricsupdated in raleonuseridentity table
            metrics_update_sql = """
                UPDATE raleonuseridentity 
                SET metricsupdated = NOW()
                FROM (VALUES %s) AS data (identityvalue)
                WHERE raleonuseridentity.identityvalue = data.identityvalue 
                AND raleonuseridentity.orgid = '{0}'
            """.format(self.message['orgId'])
            
            # Modified SQL to handle updates using a two-step process
            find_existing_attributes_sql = """
                WITH user_ids AS (
                    SELECT id
                    FROM raleonuseridentity 
                    WHERE identityvalue = ANY(%s)
                    AND orgid = %s
                )
                SELECT rua.id, rua.raleonuseridentityid
                FROM raleonuseridentityattributes rua
                JOIN user_ids ON user_ids.id = rua.raleonuseridentityid
                WHERE rua.key = 'fit_type';
            """
            
            insert_new_attributes_sql = """
                WITH user_ids AS (
                    SELECT id, identityvalue
                    FROM raleonuseridentity 
                    WHERE identityvalue = ANY(%s)
                    AND orgid = %s
                )
                INSERT INTO raleonuseridentityattributes (raleonuseridentityid, key, value)
                SELECT 
                    user_ids.id,
                    'fit_type',
                    data_values.fit_type
                FROM user_ids
                JOIN (
                    SELECT unnest(%s) as identityvalue,
                        unnest(%s) as fit_type
                ) data_values ON user_ids.identityvalue = data_values.identityvalue;
            """
            
            update_existing_attributes_sql = """
                UPDATE raleonuseridentityattributes
                SET value = data_values.new_value
                FROM (
                    SELECT unnest(%s::integer[]) as id,
                        unnest(%s::text[]) as new_value
                ) data_values
                WHERE raleonuseridentityattributes.id = data_values.id;
            """
            
            # Prepare data for updates
            metrics_update_data = [(str(row['customer']),) 
                                for _, row in final_df.iterrows()]
            
            print('Processing batch of', len(metrics_update_data), 'records')
            
            if metrics_update_data:
                # Execute metrics update
                psycopg2.extras.execute_values(
                    cur,
                    metrics_update_sql,
                    metrics_update_data,
                    template=None,
                    page_size=1000,
                    fetch=False
                )
                
                # Prepare data for attribute updates
                identityvalues = [str(row['customer']) for _, row in final_df.iterrows()]
                fit_types = [row['fit_type'] for _, row in final_df.iterrows()]
                
                # Find existing attributes
                cur.execute(find_existing_attributes_sql, (identityvalues, self.message['orgId']))
                existing_attrs = cur.fetchall()
                existing_ids = {row[1]: row[0] for row in existing_attrs}  # Map raleonuseridentityid to attribute id
                
                # Prepare data for updates and inserts
                to_update_ids = []
                to_update_values = []
                to_insert_identityvalues = []
                to_insert_fit_types = []
                
                # Process each record
                for customer, fit_type in zip(identityvalues, fit_types):
                    # Get the user's ID from raleonuseridentity
                    cur.execute("""
                        SELECT id FROM raleonuseridentity 
                        WHERE identityvalue = %s AND orgid = %s
                    """, (customer, self.message['orgId']))
                    user_result = cur.fetchone()
                    
                    if user_result:
                        user_id = user_result[0]
                        if user_id in existing_ids:
                            # Update existing attribute
                            to_update_ids.append(existing_ids[user_id])
                            to_update_values.append(fit_type)
                        else:
                            # Insert new attribute
                            to_insert_identityvalues.append(customer)
                            to_insert_fit_types.append(fit_type)
                
                # Execute updates for existing attributes
                if to_update_ids:
                    cur.execute(update_existing_attributes_sql, (to_update_ids, to_update_values))
                
                # Execute inserts for new attributes
                if to_insert_identityvalues:
                    cur.execute(
                        insert_new_attributes_sql,
                        (
                            to_insert_identityvalues,
                            self.message['orgId'],
                            to_insert_identityvalues,
                            to_insert_fit_types
                        )
                    )
                
                conn.commit()
                print(f"Updated {len(to_update_ids)} existing records")
                print(f"Inserted {len(to_insert_identityvalues)} new records")
            else:
                print("No records to update")
            
        except Exception as e:
            print(f"An error occurred: {e}")
            print(f"Update data sample: {metrics_update_data[:5] if metrics_update_data else 'No data'}")
            conn.rollback()
        finally:
            print("Closing connection")
            cur.close()
            conn.close()

    def query_athena_and_get_df(self, query_string, columns):
        if 'ATHENA_OUTPUT_BUCKET' not in os.environ:
            raise ValueError("ATHENA_OUTPUT_BUCKET environment variable is not set")
            
        response = athena_helper.run_athena_query_v2(
            query_string, 
            os.environ["ATHENA_OUTPUT_BUCKET"]
        )
        query_id = response['QueryExecutionId']

        if athena_helper.wait_for_query_execution(query_id):
            results = athena_helper.get_query_results(query_id)
            
            # Extract rows and log the first few for debugging
            try:
                rows = [row['Data'] for row in results['ResultSet']['Rows'][1:]]  # Skip header
                print("First few rows structure:", json.dumps(rows[:3], indent=2))
                
                # Convert rows to a list of dictionaries for better DataFrame creation
                processed_rows = []
                for row in rows:
                    processed_row = {}
                    for i, col in enumerate(columns):
                        processed_row[col] = self.extract_value_from_dict(row[i], col)
                    processed_rows.append(processed_row)
                
                df = pd.DataFrame(processed_rows)
                print("DataFrame creation successful. First few rows:")
                print(df.head())
                return df
            except Exception as e:
                print(f"Error processing Athena results: {str(e)}")
                print(f"Error type: {type(e)}")
                raise Exception(f"Failed to process Athena results: {str(e)}")
        else:
            raise Exception("Query failed!")

    def extract_value_from_dict(self, row, column_name):
        if isinstance(row, dict):
            if 'VarCharValue' in row:
                value = row['VarCharValue']
                if column_name in ['created_at']:
                    return pd.to_datetime(value)
                elif column_name in ['total_price', 'item_quantity']:
                    return float(value)
                else:
                    return value
            # Handle other possible dictionary structures from Athena
            elif 'isNull' in row:
                return None
            else:
                print(f"Unexpected dictionary structure for column {column_name}: {row}")
                return str(row)  # Convert to string as fallback
        return row

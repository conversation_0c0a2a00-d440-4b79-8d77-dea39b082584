from io import BytesIO
import os
import pandas as pd
import boto3
import athena_helper
import json
import logging
import math
from collections import defaultdict

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger('ProductSequenceAnalysis')

def replace_nan(data):
    """Helper to replace NaN with None for JSON serialization."""
    if isinstance(data, dict):
        return {k: replace_nan(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [replace_nan(item) for item in data]
    elif isinstance(data, float) and math.isnan(data):
        return None
    return data

class ProductSequenceAnalysis:
    def __init__(self):
        self.s3_client = boto3.client('s3')
        self.message = None
        logger.info("Initializing ProductSequenceAnalysis")
    
    def getData(self, message):
        """
        Validates the incoming message and stores it for later use.
        Returns an (empty) DataFrame to maintain the interface.
        """
        logger.info("Starting getData with message: %s", json.dumps(message, default=str))
        if not isinstance(message, dict):
            raise ValueError("Message must be a dictionary")
        required_fields = ['name', 'orgId']
        missing_fields = [field for field in required_fields if field not in message]
        if missing_fields:
            raise ValueError(f"Missing required fields in message: {', '.join(missing_fields)}")
        
        self.message = message
        return pd.DataFrame()
    
    def processData(self, df_unused):
        """
        Pulls all required purchase data (up to sequence = 3) in a single Athena query. 
        Then uses Python to build the hierarchical purchase path for:
          - 'ALL' (across all categories)
          - Each distinct category
        """
        logger.info("Starting processData with a single large Athena query.")
        if self.message is None:
            raise ValueError("Message not set. Please call getData first.")
        
        org_id = self.message['orgId']
        database = os.environ["ATHENA_DB"]

        # 1) Retrieve data for up to the 3rd purchase from Athena (one single query).
        logger.info("Querying up to purchase_sequence = 3 for organization=%s", org_id)
        df_all = self.query_athena_and_get_df(
            self.build_combined_query(org_id, database),
            columns=[
                'customer', 'purchase_sequence', 'item_id', 'item_name',
                'item_price', 'item_quantity', 'is_subscription', 'member',
                'product_category_clean'
            ]
        )

        if df_all.empty:
            logger.warning("No purchases found for org=%s up to 3rd purchase. Returning empty result.", org_id)
            return {}

        # 2) Build hierarchy for “ALL” category
        logger.info("Building hierarchy for ALL categories.")
        all_hierarchy = self.build_hierarchy_for_category(df_all, category="ALL")

        # 3) Build hierarchy for each distinct category
        category_hierarchies = {}
        unique_categories = df_all['product_category_clean'].dropna().unique().tolist()
        for cat in unique_categories:
            cat_subset = df_all[df_all['product_category_clean'] == cat]
            cat_hierarchy = self.build_hierarchy_for_category(cat_subset, category=cat)
            category_hierarchies[cat] = cat_hierarchy

        result = {
            "ALL": {"products": all_hierarchy},
        }
        for cat in unique_categories:
            result[cat] = {"products": category_hierarchies[cat]}

        logger.info("Completed processData. Built hierarchy for ALL + %d categories.", len(unique_categories))
        return result

    def writeData(self, final_data):
        """
        Writes the final hierarchical JSON result (wrapped in a DataFrame with a timestamp)
        to S3 as a parquet file.
        """
        logger.info("Starting writeData")
        if not final_data:
            logger.warning("No data to write")
            return

        final_data = replace_nan(final_data)
        json_data = json.dumps(final_data)
        df = pd.DataFrame({
            'rundate': [pd.Timestamp.now()],
            'data': [json_data]
        })

        parquet_buffer = BytesIO()
        df.to_parquet(parquet_buffer, index=False)
        parquet_buffer.seek(0)

        if 'CURATED_BUCKET' not in os.environ:
            raise ValueError("CURATED_BUCKET environment variable is not set")

        formatted_time = pd.Timestamp.now().strftime('%Y-%m-%d_%H-%M')
        s3_key = f"product_sequence/organization={self.message['orgId']}/{formatted_time}.parquet"

        try:
            self.s3_client.put_object(
                Bucket=os.environ["CURATED_BUCKET"],
                Key=s3_key,
                Body=parquet_buffer.getvalue()
            )
            logger.info("Successfully wrote parquet file to S3: %s/%s", os.environ["CURATED_BUCKET"], s3_key)
        except Exception as e:
            logger.error("Error writing to S3: %s", str(e))
            raise

    # ---------------- Helper Methods Below ----------------

    def build_combined_query(self, org_id, database):
        """
        Returns a single query that retrieves:
          - Up to the 3rd purchase (purchase_sequence <= 3) per customer.
          - Purchase sequence is now computed based on distinct orders (using id) so that
            items from the same order count as a single purchase.
          - All relevant columns for building the hierarchy.
        """
        return f"""
            WITH orders AS (
                SELECT
                    customer,
                    id,
                    MIN(created_at) AS order_created_at
                FROM "{database}".filtered_orders
                WHERE organization = '{org_id}'
                  AND item_id IS NOT NULL
                  AND cancelled_at IS NULL
                GROUP BY customer, id
            ),
            base AS (
                SELECT
                    fo.customer,
                    fo.id,
                    fo.created_at,
                    fo.item_id,
                    fo.item_name,
                    CAST(fo.item_price AS double) AS item_price,
                    CAST(fo.item_quantity AS double) AS item_quantity,
                    (CASE WHEN fo.member = true THEN true ELSE false END) AS member,
                    (CASE WHEN fo.is_subscription = true THEN true ELSE false END) AS is_subscription,
                    COALESCE(fo.product_category, 'Uncategorized') AS product_category_clean,
                    DENSE_RANK() OVER (PARTITION BY fo.customer ORDER BY ord.order_created_at) AS purchase_sequence
                FROM "{database}".filtered_orders fo
                JOIN orders ord
                    ON fo.customer = ord.customer AND fo.id = ord.id
                WHERE fo.organization = '{org_id}'
                  AND fo.item_id IS NOT NULL
                  AND fo.cancelled_at IS NULL
            )
            SELECT
                customer,
                purchase_sequence,
                item_id,
                item_name,
                item_price,
                item_quantity,
                is_subscription,
                member,
                product_category_clean
            FROM base
            WHERE purchase_sequence <= 3
        """

    def build_hierarchy_for_category(self, df, category="ALL"):
        """
        Given a DataFrame with up to 3 purchase sequences, build the nested “product path” structure:
         - For sequence=1, pick top 3 products by number of customers
         - For sequence=2, for each of those top 3, pick top 3 products among that cohort’s next purchase, etc.
        This is done entirely in Python to minimize queries.
        """
        # Convert data types
        df['purchase_sequence'] = df['purchase_sequence'].astype(int)
        df['item_price'] = df['item_price'].astype(float)
        df['item_quantity'] = df['item_quantity'].astype(float)

        # Build a mapping for each purchase sequence (1 -> 2 -> 3)
        seq_map = {}
        for seq in [1, 2, 3]:
            seq_map[seq] = df[df['purchase_sequence'] == seq].copy()

        # Precompute list of all customers in sequence 1 (for “ALL” to get the denominator).
        all_customers_seq1 = seq_map[1]['customer'].unique().tolist()
        total_customers_seq1 = len(all_customers_seq1)

        # Helper: analyze the top products for a given purchase_sequence’s df 
        # (filtered to a specific set of customers).
        def get_top_products(seq, customer_subset=None):
            if seq not in seq_map:
                return pd.DataFrame(columns=df.columns)

            sub_df = seq_map[seq]
            if customer_subset is not None:
                sub_df = sub_df[sub_df['customer'].isin(customer_subset)]

            if sub_df.empty:
                return pd.DataFrame()

            # Group by item_id and item_name to compute metrics
            grouped = sub_df.groupby(['item_id', 'item_name'], as_index=False)
            metrics = grouped.agg({
                'customer': 'nunique', 
                'item_quantity': 'sum',
                'item_price': 'sum',
                'is_subscription': 'mean',
                'member': 'mean'
            })
            # Rename columns for clarity
            metrics.rename(columns={
                'customer': 'purchases',
                'item_quantity': 'quantity',
                'item_price': 'total_price',
                'is_subscription': 'subscription_rate',
                'member': 'member_rate'
            }, inplace=True)

            # Also store the set of distinct customers for each product
            cust_groups = sub_df.groupby(['item_id', 'item_name'])['customer'].agg(lambda x: set(x))
            cust_dict = cust_groups.to_dict()

            metrics['customers'] = metrics.apply(
                lambda row: list(cust_dict[(row['item_id'], row['item_name'])]),
                axis=1
            )

            # Convert rates to percentages
            metrics['subscription_rate'] = metrics['subscription_rate'] * 100.0
            metrics['member_rate'] = metrics['member_rate'] * 100.0

            # Sort by number of purchases descending and keep top 3
            metrics.sort_values(by='purchases', ascending=False, inplace=True)
            top3 = metrics.head(3)
            return top3

        # Recursively build the sequence tree from sequence 1 to 3
        def build_sequence_tree(seq, customer_subset):
            if seq > 3:
                return []

            top_products_df = get_top_products(seq, customer_subset=customer_subset)
            if top_products_df.empty:
                return []

            results = []
            for _, row in top_products_df.iterrows():
                product_info = {
                    # For sequence 1, we label 'id' as the category (or 'ALL'); for subsequent sequences, use the actual item_id.
                    'id': (category if seq == 1 else row['item_id']),
                    'name': row['item_name'],
                    'purchases': int(row['purchases']),
                    'quantity': float(row['quantity']),
                    'revenue': round(row['total_price'], 2),
                    'conversionRate': 0.0,   # Will fill below
                    'subscriptionRate': round(row['subscription_rate'], 2),
                    'memberRate': round(row['member_rate'], 2),
                    'nextpurchases': [],
                }
                
                # conversionRate = (# distinct customers who bought this product) / (cohort size)
                if seq == 1:
                    cohort_size = total_customers_seq1
                else:
                    cohort_size = len(customer_subset)

                if cohort_size > 0:
                    product_info['conversionRate'] = round(
                        (row['purchases'] / cohort_size) * 100.0, 
                        2
                    )

                # Recursively build the next purchase sequence tree
                product_info['nextpurchases'] = build_sequence_tree(
                    seq + 1,
                    customer_subset=row['customers']
                )
                results.append(product_info)

            return results

        # Kick off from sequence=1, starting with all customers
        hierarchy = build_sequence_tree(seq=1, customer_subset=None)
        return hierarchy

    def query_athena_and_get_df(self, query_string, columns):
        """
        Executes the provided Athena query and returns a DataFrame with the specified columns.
        """
        if 'ATHENA_OUTPUT_BUCKET' not in os.environ:
            raise ValueError("ATHENA_OUTPUT_BUCKET environment variable is not set")
            
        response = athena_helper.run_athena_query_v2(query_string, os.environ["ATHENA_OUTPUT_BUCKET"])
        query_id = response['QueryExecutionId']

        if athena_helper.wait_for_query_execution(query_id):
            results = athena_helper.get_query_results(query_id)
            try:
                # The first row is the header; skip it
                rows = [row['Data'] for row in results['ResultSet']['Rows'][1:]]
                processed_rows = []
                for row in rows:
                    processed_row = {}
                    for i, col in enumerate(columns):
                        processed_row[col] = self.extract_value_from_dict(row[i], col)
                    processed_rows.append(processed_row)
                df = pd.DataFrame(processed_rows)
                return df
            except Exception as e:
                logger.error("Error processing Athena results: %s", str(e))
                raise
        else:
            raise Exception("Query failed!")

    def extract_value_from_dict(self, row, column_name):
        if isinstance(row, dict):
            if 'VarCharValue' in row:
                value = row['VarCharValue']
                
                # Convert certain columns to bool
                if column_name in ['is_subscription', 'member']:
                    if value.lower() == 'true':
                        return True
                    elif value.lower() == 'false':
                        return False
                    else:
                        return None

                if column_name in ['item_price', 'item_quantity']:
                    try:
                        return float(value)
                    except ValueError:
                        return 0.0
                    
                elif column_name in ['purchase_sequence']:
                    try:
                        return int(value)
                    except ValueError:
                        return 0

                return value
            elif row.get('isNull'):
                return None
        return None

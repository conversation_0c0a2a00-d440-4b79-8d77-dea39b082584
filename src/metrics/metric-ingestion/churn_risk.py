from io import BytesIO
import os
import pandas as pd
import numpy as np
import boto3
import athena_helper
import raleon_helper
import psycopg2
import psycopg2.extras
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestClassifier
from datetime import datetime, timedelta

class ChurnRiskScorer:
    def __init__(self):
        self.model = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            class_weight='balanced',
            random_state=42
        )
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        self.churn_threshold = None
        
        # Instance variables to store data
        self.message = None
        self.customer_df = None
        self.orders_df = None
        self.purchase_patterns = None
    
    def calculate_time_decay(self, days_since_purchase, half_life=30):
        """Calculate time decay factor using exponential decay."""
        return np.exp(-np.log(2) * days_since_purchase / half_life)


    def getData(self, message):
        print("Getting data...")
        self.message = message
        database = os.environ["ATHENA_DB"]
        pgdatabase = os.environ["POSGRES_DB"]
        
        # Customer data query
        customer_query = f"""
        WITH OrderDiffs AS (
            SELECT
                o.customer AS CustomerID,
                o.created_at,
                LAG(o.created_at) OVER (PARTITION BY o.customer ORDER BY o.created_at) AS PreviousOrderDate
            FROM
                "{database}"."filtered_orders" o
            WHERE
                o.organization = '{message['orgId']}'
        ),
        AvgTimeBetweenPurchases AS (
            SELECT
                CustomerID,
                AVG(EXTRACT(DAY FROM (created_at - PreviousOrderDate))) AS AvgTimeBetweenPurchases
            FROM
                OrderDiffs
            WHERE
                PreviousOrderDate IS NOT NULL
            GROUP BY
                CustomerID
        ),
        LoyaltyTransactions AS (
            SELECT
                ltx.loyaltycurrencybalanceid,
                SUM(CASE WHEN ltx.amount < 0 THEN 1 ELSE 0 END) AS used,
                SUM(CASE WHEN ltx.amount > 0 THEN 1 ELSE 0 END) AS earned
            FROM
                "{pgdatabase}"."public"."loyaltycurrencytxlog" ltx
            GROUP BY
                ltx.loyaltycurrencybalanceid
        ),
        RefundDetails AS (
            SELECT
                o.customer AS CustomerID,
                COUNT(DISTINCT r.order_id) AS OrdersWithRefunds,
                SUM(r.refund_amount) AS TotalAmountRefunded
            FROM
                "{database}"."filtered_orders" o
            LEFT JOIN
                "{database}"."order_refunds" r ON o.id = r.order_id
            WHERE
                r.organization = '{message['orgId']}'
                AND o.organization = '{message['orgId']}'
            GROUP BY
                o.customer
        )
        SELECT
            ri.id,
            ri.orgid,
            ri.raleonuserid,
            ri.identityvalue,
            ri.loyaltysegment,
            lb.loyaltycurrencyid,
            lb.balance,
            lb.trailingtwelvemonthgranttotal,
            COALESCE(tx.earned, 0) AS earned,
            COALESCE(tx.used, 0) AS used,
            COALESCE(ord.TotalOrders, 0) AS TotalOrders,
            COALESCE(ord.OrdersWithDiscounts, 0) AS OrdersWithDiscounts,
            COALESCE(ord.TotalSpent, 0) AS TotalSpent,
            COALESCE(ord.SubtotalSpent, 0) AS SubtotalSpent,
            COALESCE(ord.OrdersWithFreeShipping, 0) AS OrdersWithFreeShipping,
            COALESCE(ref.OrdersWithRefunds, 0) AS OrdersWithRefunds,
            COALESCE(ref.TotalAmountRefunded, 0) AS TotalAmountRefunded,
            COALESCE(ord.DaysSinceLastPurchase, NULL) AS DaysSinceLastPurchase,
            COALESCE(atbp.AvgTimeBetweenPurchases, NULL) AS AvgTimeBetweenPurchases
        FROM
            "{pgdatabase}"."public"."raleonuseridentity" ri
        LEFT JOIN
            "{pgdatabase}"."public"."loyaltycurrencybalance" lb ON ri.raleonuserid = lb.raleonuserid
        LEFT JOIN
            LoyaltyTransactions tx ON lb.id = tx.loyaltycurrencybalanceid
        LEFT JOIN
            (
            SELECT
                o.customer AS CustomerID,
                COUNT(DISTINCT o.id) AS TotalOrders,
                COUNT(DISTINCT CASE WHEN o.total_discounts > 0 THEN o.id END) AS OrdersWithDiscounts,
                SUM(DISTINCT o.total_price) AS TotalSpent,
                SUM(DISTINCT o.subtotal_price) AS SubtotalSpent,
                COUNT(DISTINCT CASE WHEN o.total_shipping_price = 0 THEN o.id END) AS OrdersWithFreeShipping,
                MAX(o.created_at) AS LastOrderDate,
                EXTRACT(DAY FROM CURRENT_DATE - MAX(o.created_at)) AS DaysSinceLastPurchase
            FROM
                "{database}"."filtered_orders" o
            WHERE
                o.organization = '{message['orgId']}'
            GROUP BY
                o.customer
            ) ord ON ri.identityvalue = ord.CustomerID
        LEFT JOIN
            AvgTimeBetweenPurchases atbp ON ri.identityvalue = atbp.CustomerID
        LEFT JOIN
            RefundDetails ref ON ri.identityvalue = ref.CustomerID
        WHERE
            ri.orgid = {message['orgId']};

        """
        
        # Orders data query
        orders_query = f"""
        SELECT 
            o.customer AS user, 
            'PRODUCT' AS product, 
            o.created_at AS date, 
            totals.price AS price, 
            o.id
        FROM "{database}".filtered_orders o
        JOIN (
            SELECT 
                o.id, 
                MAX(o.subtotal_price) - COALESCE(SUM(r.refund_amount), 0) AS price
            FROM 
                "{database}".filtered_orders o
            LEFT JOIN 
                "{database}"."order_refunds" r ON o.id = r.order_id AND r.organization = '{message['orgId']}'
            WHERE 
                o.organization = '{message['orgId']}' 
                AND o.customer != '0'
            GROUP BY 
                o.id
        ) totals ON totals.id = o.id
        WHERE 
            o.organization = '{message['orgId']}' 
            AND o.customer != '0'
        GROUP BY 
            o.id, o.customer, o.created_at, totals.price;
        """

        if 'queryOverride' in message and message['queryOverride']:
            customer_query = message['queryOverride'].replace("{orgId}", str(message['orgId']))
            customer_query = customer_query.replace("{database}", database)

        self.customer_df = self.query_athena_and_get_df(customer_query, [
            'id', 'orgid', 'raleonuserid', 'identityvalue', 'loyaltysegment',
            'loyaltycurrencyid', 'balance', 'trailingtwelvemonthgranttotal',
            'earned', 'used', 'TotalOrders', 'OrdersWithDiscounts', 'TotalSpent',
            'SubtotalSpent', 'OrdersWithFreeShipping', 'OrdersWithRefunds',
            'TotalAmountRefunded', 'DaysSinceLastPurchase', 'AvgTimeBetweenPurchases'
        ])

        self.orders_df = self.query_athena_and_get_df(orders_query, [
            'user', 'product', 'date', 'price', 'id'
        ])

        return self.customer_df

    def calculate_purchase_patterns(self, orders_df):
        """Calculate purchase patterns and time-based metrics for each user."""
        # Original purchase pattern calculations
        orders_df['date'] = pd.to_datetime(orders_df['date'])
        orders_df = orders_df.sort_values(['user', 'date'])
        orders_df['days_between_orders'] = orders_df.groupby('user')['date'].diff().dt.days
        
        purchase_patterns = orders_df.groupby('user').agg({
            'date': ['min', 'max', 'count'],
            'price': ['sum', 'mean', 'std'],
            'days_between_orders': ['mean', 'median', 'std']
        }).round(2)
        
        purchase_patterns.columns = [
            'first_purchase_date',
            'last_purchase_date',
            'total_orders',
            'total_spent',
            'avg_order_value',
            'order_value_std',
            'avg_days_between_orders',
            'median_days_between_orders',
            'days_between_orders_std'
        ]
        
        # Calculate days since last purchase and time decay
        current_date = datetime.now()
        purchase_patterns['days_since_last_purchase'] = (
            current_date - pd.to_datetime(purchase_patterns['last_purchase_date'])
        ).dt.days
        
        # Add time decay factor
        purchase_patterns['time_decay_factor'] = self.calculate_time_decay(
            purchase_patterns['days_since_last_purchase']
        )
        
        # Handle single purchase customers
        single_purchase_mask = purchase_patterns['total_orders'] == 1
        purchase_patterns.loc[single_purchase_mask, 'avg_days_between_orders'] = \
            purchase_patterns.loc[single_purchase_mask, 'days_since_last_purchase']
        purchase_patterns.loc[single_purchase_mask, 'median_days_between_orders'] = \
            purchase_patterns.loc[single_purchase_mask, 'days_since_last_purchase']
        purchase_patterns.loc[single_purchase_mask, 'days_between_orders_std'] = 0
        
        # Calculate purchase frequency consistency
        purchase_patterns['purchase_frequency_consistency'] = np.where(
            purchase_patterns['days_between_orders_std'].notna(),
            1 / (1 + purchase_patterns['days_between_orders_std']),
            0
        )
        
        return purchase_patterns.reset_index()

    def determine_churn_threshold(self, purchase_patterns):
        print("\nDetermining churn threshold...")
        multi_purchase_patterns = purchase_patterns[purchase_patterns['total_orders'] > 1].copy()
        print(f"Total customers: {len(purchase_patterns)}")
        print(f"Customers with multiple purchases: {len(multi_purchase_patterns)}")
        
        valid_gaps = multi_purchase_patterns['avg_days_between_orders'].dropna()
        
        if len(valid_gaps) > 0:
            print("\nAvg days between orders statistics (multiple purchase customers):")
            print(valid_gaps.describe())
            
            typical_max_gap = valid_gaps.quantile(0.75)
            self.churn_threshold = max(typical_max_gap * 1.5, 60)
        else:
            self.churn_threshold = 180
        
        print(f"Churn threshold determined: {self.churn_threshold:.1f} days")
        
        return self.churn_threshold

    def prepare_features(self, df, purchase_patterns):
        """Prepare features combining customer data and purchase patterns."""
        print("\nPreparing features...")
        combined_df = df.merge(
            purchase_patterns,
            left_on='identityvalue',
            right_on='user',
            how='left'
        )

        print("\nPreparing features 1:")
        
        # Calculate refund-related features
        combined_df['refund_rate'] = np.where(
            combined_df['TotalOrders'] > 0,
            combined_df['OrdersWithRefunds'] / combined_df['TotalOrders'],
            0
        )
        print("\nPreparing features 2:")
        
        combined_df['avg_refund_amount'] = np.where(
            combined_df['OrdersWithRefunds'] > 0,
            combined_df['TotalAmountRefunded'] / combined_df['OrdersWithRefunds'],
            0
        )

        print("\nPreparing features 3:")
        
        combined_df['refund_to_spent_ratio'] = np.where(
            combined_df['TotalSpent'] > 0,
            combined_df['TotalAmountRefunded'] / combined_df['TotalSpent'],
            0
        )

        print("\nPreparing features 4:")
        
        # Fill NaN values
        combined_df = combined_df.fillna({
            'days_since_last_purchase': 999999,
            'avg_days_between_orders': 999999,
            'purchase_frequency_consistency': 0,
            'avg_order_value': 0,
            'order_value_std': 0,
            'total_orders': 0,
            'total_spent': 0,
            'time_decay_factor': 0,
            'refund_rate': 0,
            'avg_refund_amount': 0,
            'refund_to_spent_ratio': 0
        })

        print("\nPreparing features 5:")
        
        # Encode loyalty segment
        combined_df['loyaltysegment'] = combined_df['loyaltysegment'].apply(
            lambda x: str(x['VarCharValue']) if isinstance(x, dict) and 'VarCharValue' in x else str(x)
        )

        combined_df['loyaltysegment_encoded'] = self.label_encoder.fit_transform(
            combined_df['loyaltysegment'].fillna('Unknown')
        )

        print("\nPreparing features 6:")
        
        # Calculate time-decayed metrics
        combined_df['decayed_total_spent'] = combined_df['total_spent'] * combined_df['time_decay_factor']
        combined_df['decayed_order_frequency'] = combined_df['total_orders'] * combined_df['time_decay_factor']

        print("\nPreparing features 7:")
        
        # Calculate purchase frequency
        date_diff = (pd.to_datetime(combined_df['last_purchase_date']) - 
                    pd.to_datetime(combined_df['first_purchase_date'])).dt.days
        
        print("\nPreparing features 8:")
        
        combined_df['purchase_frequency'] = np.where(
            date_diff > 0,
            combined_df['total_orders'] / date_diff,
            0
        )
        
        # Normalize days since purchase
        combined_df['normalized_days_since_purchase'] = np.clip(
            combined_df['days_since_last_purchase'] / self.churn_threshold,
            0,
            10
        )

        print("\nPreparing features 9:")
        
        self.feature_columns = [
            'loyaltysegment_encoded',
            'normalized_days_since_purchase',
            'purchase_frequency',
            'purchase_frequency_consistency',
            'avg_order_value',
            'order_value_std',
            'total_orders',
            'total_spent',
            'time_decay_factor',
            'decayed_total_spent',
            'decayed_order_frequency',
            'refund_rate',
            'avg_refund_amount',
            'refund_to_spent_ratio',
            'TotalOrders',
            'OrdersWithDiscounts',
            'TotalSpent',
            'OrdersWithRefunds'
        ]
        
        return combined_df[self.feature_columns]

    def calculate_churn_risk(self, df, purchase_patterns):
        """Calculate churn risk score (0-1000) based on various factors using ML."""
        print("\nCalculating churn risk scores...")
        # Prepare features with enhanced metrics
        X = self.prepare_features(df, purchase_patterns)
        print("\nprepared features")
        X_scaled = self.scaler.fit_transform(X)
        print("\nscaled features")
        
        # Calculate refund metrics first
        df['refund_rate'] = np.where(
            df['TotalOrders'] > 0,
            df['OrdersWithRefunds'] / df['TotalOrders'],
            0
        )
        
        df['refund_to_spent_ratio'] = np.where(
            df['TotalSpent'] > 0,
            df['TotalAmountRefunded'] / df['TotalSpent'],
            0
        )
        
        # Get purchase history data with additional metrics
        merged_data = df.merge(
            purchase_patterns[['user', 'days_since_last_purchase', 'total_orders', 
                             'avg_days_between_orders', 'time_decay_factor']],
            left_on='identityvalue',
            right_on='user',
            how='left'
        )

        print("\nMerged data:")
        
        # Add refund metrics to merged_data
        merged_data['refund_rate'] = df['refund_rate']
        merged_data['refund_to_spent_ratio'] = df['refund_to_spent_ratio']
        
        # Mark customers with no purchases as churned
        no_purchases = merged_data['total_orders'].isna() | (merged_data['total_orders'] == 0)
        days_since_last = merged_data['days_since_last_purchase']
        days_since_last.fillna(self.churn_threshold * 1.5, inplace=True)

        # Identify churned customers first
        churned_mask = (days_since_last > (self.churn_threshold * 1.5)) | no_purchases
        
        # Only fit model on non-churned customers
        active_mask = ~churned_mask
        if active_mask.any():
            print("\nFitting model on active customers...")
            active_customers = merged_data[active_mask]
            X_active = X_scaled[active_mask]

            # Enhanced training labels using multiple risk factors
            days_ratio = active_customers['days_since_last_purchase'] / self.churn_threshold
            purchase_ratio = 1 / active_customers['total_orders']
            freq_ratio = active_customers['avg_days_between_orders'] / self.churn_threshold
            
            # Add refund impact
            refund_impact = (
                active_customers['refund_rate'] * 0.6 +  # Higher weight for frequency of refunds
                active_customers['refund_to_spent_ratio'] * 0.4  # Lower weight for refund amounts
            )
            
            # Time-decay adjusted metrics
            time_decay = active_customers['time_decay_factor']
            
            # Combine into risk levels (0-4) with updated weights
            y_active = (
                (days_ratio * 0.35) +                # recency
                (purchase_ratio * 0.2) +            # number of purchases
                (freq_ratio * 0.10) +              # purchase frequency
                (refund_impact * 0.10) +           # refund behavior
                ((1 - time_decay) * 0.25)           # time decay
            )
            
            # Calculate bin edges before applying qcut
            bins = np.quantile(y_active, np.linspace(0, 1, 6))  # Get 5 quantiles

            # Check for duplicate bin edges
            if len(set(bins)) < len(bins):
                print("Duplicate bin edges detected. Returning None.")
                return None
            # Convert to 5 risk levels
            y_active = pd.qcut(y_active, q=5, labels=[0, 1, 2, 3, 4])

            
            # Print training data distribution
            print("\nRisk Level Distribution in Training Data:")
            print(pd.Series(y_active).value_counts().sort_index())
            
            # Fit model
            self.model.fit(X_active, y_active)
            
            # Get probabilities for each risk level
            probabilities = self.model.predict_proba(X_scaled)
            
            # Calculate risk scores using weighted probabilities
            risk_scores = np.zeros(len(df))
            for i, prob in enumerate(probabilities.T):
                risk_scores += prob * (i * 250)  # Spread classes across 0-1000 range
            
            # Apply enhanced business rules to adjust scores
            active_scores = risk_scores[active_mask]
            
            # High risk adjustments - now including refund behavior
            high_risk_mask = (
                ((active_customers['total_orders'] == 1) & 
                 (active_customers['days_since_last_purchase'] > (self.churn_threshold * 0.75))) |
                ((active_customers['refund_rate'] > 0.5) &  # High refund rate
                 (active_customers['days_since_last_purchase'] > (self.churn_threshold * 0.5))) |
                (active_customers['refund_to_spent_ratio'] > 0.4)  # High refund amount relative to spending
            )
            active_scores[high_risk_mask] = np.maximum(active_scores[high_risk_mask], 900)
            
            # Low risk adjustments - now considering time decay
            low_risk_mask = (
                (active_customers['total_orders'] > 3) & 
                (active_customers['days_since_last_purchase'] < (self.churn_threshold * 0.25)) &
                (active_customers['avg_days_between_orders'] < (self.churn_threshold * 0.25)) &
                (active_customers['refund_rate'] < 0.1) &  # Low refund rate
                (active_customers['time_decay_factor'] > 0.7)  # Recent activity
            )
            active_scores[low_risk_mask] = np.minimum(active_scores[low_risk_mask], 100)
            
            # Final score assignment
            scores = np.zeros(len(df))
            scores[churned_mask] = -1
            scores[active_mask] = active_scores
            
            # Print distribution analysis
            print("\nScore Distribution Analysis:")
            active_scores = scores[scores != -1]
            print("\nScore ranges (excluding churned):")
            for range_start in range(0, 1001, 100):
                range_end = range_start + 100
                count = np.sum((active_scores >= range_start) & (active_scores < range_end))
                print(f"{range_start}-{range_end-1}: {count} customers")
                
            # Print feature importance
            print("\nFeature Importance:")
            for feature, importance in zip(self.feature_columns, self.model.feature_importances_):
                print(f"{feature}: {importance:.4f}")
            
            return np.clip(scores, -1, 1000).astype(int)
        
        else:
            return np.full(len(df), -1)

    def assign_risk_category(self, score):
        """Assign risk category based on score."""
        if score == -1:
            return 'Churned'
        elif score == 0:
            return 'Very Low'
        elif score < 250:
            return 'Low'
        elif score < 500:
            return 'Medium'
        elif score < 750:
            return 'High'
        else:
            return 'Very High'

    def processData(self, customer_df):
        print("Processing data...")
        if self.customer_df is None or self.customer_df.empty or self.customer_df.shape[0] <50 or self.orders_df is None or self.orders_df.empty or self.orders_df.shape[0] <50:
            print("No data to process")
            return None
        results_df = self.fit_predict(self.customer_df, self.orders_df)
        
        df_prev_scores = self.get_previous_scores()
        results_df = results_df.merge(df_prev_scores, on='identityvalue', how='left')
        
        # Fill null previousChurnRisk values with 0
        results_df['previousChurnRisk'] = results_df['previousChurnRisk'].apply(
            lambda x: 0 if pd.isna(x) or isinstance(x, dict) and not x else x
        )

        
        results_df = results_df[
            (results_df['previousChurnRisk'] != results_df['churn_risk_score'])
        ]
        
        results_df['rundate'] = pd.Timestamp.now()
        
        return results_df

    def fit_predict(self, customer_df, orders_df):
        """Main method to calculate churn risk scores."""

        print("Fitting model and predicting churn risk scores...")
        # Calculate purchase patterns
        purchase_patterns = self.calculate_purchase_patterns(orders_df)
        
        # Determine churn threshold
        self.determine_churn_threshold(purchase_patterns)
        
        # Calculate churn risk scores
        scores = self.calculate_churn_risk(customer_df, purchase_patterns)
        
        # Create results DataFrame
        results_df = customer_df.copy()
        results_df['churn_risk_score'] = scores
        results_df['churn_risk_score'] = results_df['churn_risk_score'].fillna(-1)
        results_df['risk_category'] = results_df['churn_risk_score'].apply(self.assign_risk_category)
        
        # Add key metrics from purchase patterns
        results_df = results_df.merge(
            purchase_patterns[['user', 'days_since_last_purchase', 'avg_days_between_orders', 
                             'purchase_frequency_consistency', 'total_orders', 'total_spent']],
            left_on='identityvalue',
            right_on='user',
            how='left'
        ).fillna({
            'days_since_last_purchase': 999999,
            'avg_days_between_orders': 999999,
            'purchase_frequency_consistency': 0,
            'total_orders': 0,
            'total_spent': 0
        })
        
        return results_df

    def get_previous_scores(self):
        print("Getting previous scores...")
        pgdatabase = os.environ["POSGRES_DB"]
        query_string = f"""
        SELECT 
            identityvalue,
            churnrisk as previousChurnRisk 
        FROM 
            "{pgdatabase}"."public"."raleonuseridentity" 
        WHERE 
            orgid = {str(self.message['orgId'])};
        """
        columns = ['identityvalue', 'previousChurnRisk']
        return self.query_athena_and_get_df(query_string, columns)

    def writeData(self, final_df):
        print("Writing data...")
        if final_df is None or final_df.empty:
            print("No changes in scores detected. No data written.")
            return
        
        print("Updating Postgres with new scores")
        self.update_postgres(final_df)
        
        try:
            s3 = boto3.client('s3')
            parquet_buffer = BytesIO()
            
            output_df = final_df[[
                'identityvalue',
                'churn_risk_score',
                'previousChurnRisk',
                'risk_category',
                'days_since_last_purchase',
                'rundate'
            ]]
            output_df = output_df.rename(columns={'identityvalue': 'customer'})
            
            output_df.to_parquet(parquet_buffer, index=False)
            
            if 'CURATED_BUCKET' not in os.environ:
                raise ValueError("CURATED_BUCKET environment variable is not set")
            
            formatted_time = pd.Timestamp.now().strftime('%Y-%m-%d_%H-%M')
            s3_key = f"churn_risk/organization={self.message['orgId']}/{formatted_time}.parquet"
            
            s3.put_object(
                Bucket=os.environ["CURATED_BUCKET"],
                Key=s3_key,
                Body=parquet_buffer.getvalue()
            )
            
            database = os.environ["ATHENA_DB"]
            print(f"Updating Athena partitions for {database}.{self.message['name']}")
            athena_helper.update_athena_partitions(
                f"{database}.{self.message['name']}", 
                self.message['orgId'],
                self.message['name'],
                os.environ["CURATED_BUCKET"],
                os.environ["ATHENA_OUTPUT_BUCKET"]
            )
            
        except Exception as e:
            print(f"Error saving to S3: {e}")
            raise

    def update_postgres(self, final_df):
        print("Updating Postgres")
        database_info = raleon_helper.get_database_info()
        conn = psycopg2.connect(
            user=database_info['username'],
            password=database_info['password'],
            host=database_info['host'],
            dbname=database_info['dbname'],
            port=database_info['port']
        )
        
        try:
            cur = conn.cursor()
            print("Connected to database")
            
            update_sql = """
                UPDATE raleonuseridentity 
                SET churnrisk = data.score,
                    metricsupdated = NOW()
                FROM (VALUES %s) AS data (identityvalue, score)
                WHERE raleonuseridentity.identityvalue = data.identityvalue 
                AND raleonuseridentity.orgid = {0};
            """.format(self.message['orgId'])
            
            update_data = [(str(row['identityvalue']), int(row['churn_risk_score'])) 
                        for _, row in final_df.iterrows()]
            
            if update_data:
                psycopg2.extras.execute_values(
                    cur,
                    update_sql,
                    update_data,
                    template=None,
                    page_size=1000,
                    fetch=False
                )
                
                cur.execute("SELECT 1", (self.message['orgId'],))
                conn.commit()
                print(f"Updated {len(update_data)} records")
            else:
                print("No records to update")
            
        except Exception as e:
            print(f"An error occurred: {e}")
            conn.rollback()
        finally:
            cur.close()
            conn.close()

    def query_athena_and_get_df(self, query_string, columns):
        print(f"Running query: {query_string}")
        if 'ATHENA_OUTPUT_BUCKET' not in os.environ:
            raise ValueError("ATHENA_OUTPUT_BUCKET environment variable is not set")
        response = athena_helper.run_athena_query_v2(query_string, os.environ["ATHENA_OUTPUT_BUCKET"])
        query_id = response['QueryExecutionId']

        if athena_helper.wait_for_query_execution(query_id):
            results = athena_helper.get_query_results(query_id)
            rows = [row['Data'] for row in results['ResultSet']['Rows'][1:]]
            df = pd.DataFrame(rows, columns=columns)
            for col in df.columns:
                if df[col].apply(isinstance, args=(dict,)).any():
                    df[col] = df[col].apply(lambda x: self.extract_value_from_dict(x, col))
            return df
        else:
            raise Exception("Query failed!")

    def extract_value_from_dict(self, row, column_name):
        if isinstance(row, dict) and 'VarCharValue' in row:
            value = row['VarCharValue']
            if column_name in ['created_at', 'rundate', 'date']:
                return pd.to_datetime(value)
            elif column_name in ['price', 'TotalSpent', 'SubtotalSpent', 'TotalAmountRefunded', 'balance']:
                return float(value) if value else 0
            elif column_name in ['TotalOrders', 'OrdersWithRefunds', 'OrdersWithDiscounts', 'earned', 'used', 'previousChurnRisk']:
                return int(value) if value else 0
            elif column_name == 'loyaltysegment':
                return str(value) if value else 'Unknown'
            else:
                return value
        return row
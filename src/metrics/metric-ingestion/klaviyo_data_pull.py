import os
import json
import time
import boto3
import asyncio
import base64
import requests
import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
import traceback
from io import BytesIO
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
from raleon_helper import decrypt, get_klaviyo_integration_by_org_id
import athena_helper

class KlaviyoData:
    def __init__(self):

        self.encrypted_api_key = None
        self.encryption_key = None
        self.api_key = None
        self.s3_bucket = os.environ["CURATED_BUCKET"]
        self.s3_client = boto3.client('s3')
        self.shopify_placed_order_metric_id = None
        self.message = None

    def delay(self, ms):
        time.sleep(ms / 1000)

    async def initialize(self):
        try:
            klaviyo_info = get_klaviyo_integration_by_org_id(self.message['orgId'])
            self.api_key = decrypt(klaviyo_info['value'])
            print(self.api_key)
            print('Decrypted Klaviyo API Key successfully')
            
            await self.find_shopify_placed_order_metric()
            
            return True
        except Exception as e:
            raise Exception(f"Error initializing KlaviyoData: {str(e)}")

    async def retrieve_cursor_paginated_data(self, url, additional_params=None, include_relationships=None):
        if additional_params is None:
            additional_params = {}
        if include_relationships is None:
            include_relationships = []
            
        all_results = []
        current_url = url

        while current_url:
            try:
                headers = {
                    'Accept': 'application/json',
                    'Revision': '2024-02-15',
                    'Authorization': f'Klaviyo-API-Key {self.api_key}'
                }

                # For first page, add additional params
                params = {}
                if current_url == url:
                    params = additional_params.copy()
                    if include_relationships:
                        params['include'] = ','.join(include_relationships)
                
                response = requests.get(current_url, params=params, headers=headers)
                response.raise_for_status()
                response_data = response.json()

                self.delay(500)

                data = response_data.get('data', [])
                links = response_data.get('links', {})
                
                all_results.extend(data)
                
                # Get next page URL from links
                current_url = links.get('next')
                if current_url:
                    print(f"Next page cursor found: {current_url}")
                else:
                    print("No more pages to fetch")

            except Exception as e:
                print(f'Error retrieving page: {str(e)}')
                break
                
        print(f"Total items retrieved: {len(all_results)}")
        return all_results

    async def find_shopify_placed_order_metric(self):
        try:
            print('Searching for Shopify Placed Order metric...')
            metrics = await self.retrieve_cursor_paginated_data('https://a.klaviyo.com/api/metrics')
            
            for metric in metrics:
                attributes = metric.get('attributes', {})
                integration = attributes.get('integration', {})
                
                if (attributes.get('name') == 'Placed Order' and 
                    integration.get('key') == 'shopify'):
                    self.shopify_placed_order_metric_id = metric.get('id')
                    print(f"Found Shopify Placed Order metric with ID: {self.shopify_placed_order_metric_id}")
                    return self.shopify_placed_order_metric_id
            
            print('Shopify Placed Order metric not found')
            return None
        except Exception as e:
            print(f'Error finding Shopify Placed Order metric: {str(e)}')
            return None

    async def retrieve_segments(self):
        last_year = pd.Timestamp.now().replace(day=1) - pd.DateOffset(years=1)
        last_year = last_year.strftime('%Y-%m-%dT00:00:00Z')
        filter_query = f"greater-than-or-equal(created_at,{last_year})"
        return await self.retrieve_cursor_paginated_data(
            'https://a.klaviyo.com/api/segments',
            {'filter': filter_query}
        )

    async def retrieve_campaigns(self):
        last_year = pd.Timestamp.now().replace(day=1) - pd.DateOffset(years=1)
        last_year = last_year.strftime('%Y-%m-%dT00:00:00Z')
        filter_query = f"and(equals(messages.channel,'email'),greater-or-equal(created_at,{last_year}),equals(status,'Sent'))"
        return await self.retrieve_cursor_paginated_data(
            'https://a.klaviyo.com/api/campaigns',
            {'filter': filter_query}
        )

    async def retrieve_flows(self):
        last_year = pd.Timestamp.now().replace(day=1) - pd.DateOffset(years=1)
        last_year = last_year.strftime('%Y-%m-%dT00:00:00Z')
        filter_query = f"and(greater-or-equal(created,{last_year}),equals(status,'live'))"
        return await self.retrieve_cursor_paginated_data(
            'https://a.klaviyo.com/api/flows',
            {'filter': filter_query, 'sort': 'created'}
        )
    
    async def retrieve_campaign_messages(self, campaign_id):
        """Retrieve campaign messages for a specific campaign ID"""
        try:
            url = f'https://a.klaviyo.com/api/campaigns/{campaign_id}/campaign-messages'
            return await self.retrieve_cursor_paginated_data(url)
        except Exception as e:
            print(f'Error retrieving campaign messages for campaign {campaign_id}: {str(e)}')
            return []

    async def retrieve_flow_messages(self, flow_id):
        """Retrieve flow messages for a specific flow ID"""
        try:
            url = f'https://a.klaviyo.com/api/flows/{flow_id}/flow-messages'
            return await self.retrieve_cursor_paginated_data(url)
        except Exception as e:
            print(f'Error retrieving flow messages for flow {flow_id}: {str(e)}')
            return []

    async def retrieve_all_campaign_messages(self, campaigns):
        """Retrieve all campaign messages for all campaigns"""
        all_messages = {}
        for campaign in campaigns:
            campaign_id = campaign.get('id')
            if campaign_id:
                messages = await self.retrieve_campaign_messages(campaign_id)
                all_messages[campaign_id] = messages
        return all_messages

    async def retrieve_all_flow_messages(self, flows):
        """Retrieve all flow messages for all flows"""
        all_messages = {}
        for flow in flows:
            flow_id = flow.get('id')
            if flow_id:
                messages = await self.retrieve_flow_messages(flow_id)
                all_messages[flow_id] = messages
        return all_messages

    async def get_campaign_values_report(self):
        if not self.shopify_placed_order_metric_id:
            print('Cannot get campaign values report: Shopify Placed Order metric ID not found')
            return None

        try:
            headers = {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Revision': '2024-10-15',
                'Authorization': f'Klaviyo-API-Key {self.api_key}'
            }
            
            payload = {
                "data": {
                    "type": "campaign-values-report",
                    "attributes": {
                        "timeframe": {
                            "key": "last_365_days"
                        },
                        "statistics": [
                            "average_order_value", "bounce_rate", "bounced", "bounced_or_failed", "bounced_or_failed_rate", "click_rate", "click_to_open_rate", "clicks", "clicks_unique", "conversion_rate", "conversion_uniques", "conversion_value", "conversions", "delivered", "delivery_rate", "failed", "failed_rate", "open_rate", "opens", "opens_unique", "recipients", "revenue_per_recipient", "spam_complaint_rate", "spam_complaints", "unsubscribe_rate", "unsubscribe_uniques", "unsubscribes"
                        ],
                        "conversion_metric_id": self.shopify_placed_order_metric_id
                    }
                }
            }
            
            response = requests.post(
                'https://a.klaviyo.com/api/campaign-values-reports',
                json=payload,
                headers=headers
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f'Error retrieving campaign values report: {str(e)}')
            return None

    async def get_flow_values_report(self):
        if not self.shopify_placed_order_metric_id:
            print('Cannot get flow values report: Shopify Placed Order metric ID not found')
            return None

        try:
            headers = {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Revision': '2024-10-15',
                'Authorization': f'Klaviyo-API-Key {self.api_key}'
            }
            
            payload = {
                "data": {
                    "type": "flow-values-report",
                    "attributes": {
                        "timeframe": {
                            "key": "last_365_days"
                        },
                        "statistics": [
                            "average_order_value", "bounce_rate", "bounced", "bounced_or_failed", "bounced_or_failed_rate", "click_rate", "click_to_open_rate", "clicks", "clicks_unique", "conversion_rate", "conversion_uniques", "conversion_value", "conversions", "delivered", "delivery_rate", "failed", "failed_rate", "open_rate", "opens", "opens_unique", "recipients", "revenue_per_recipient", "spam_complaint_rate", "spam_complaints", "unsubscribe_rate", "unsubscribe_uniques", "unsubscribes"
                        ],
                        "conversion_metric_id": self.shopify_placed_order_metric_id
                    }
                }
            }
            
            response = requests.post(
                'https://a.klaviyo.com/api/flow-values-reports',
                json=payload,
                headers=headers
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f'Error retrieving flow values report: {str(e)}')
            return None

    async def get_flow_series_report(self):
        if not self.shopify_placed_order_metric_id:
            print('Cannot get flow series report: Shopify Placed Order metric ID not found')
            return None

        try:
            headers = {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Revision': '2024-10-15',
                'Authorization': f'Klaviyo-API-Key {self.api_key}'
            }

            # Calculate 60-day date ranges for the last 365 days
            end_date = pd.Timestamp.now()
            start_date = end_date - pd.DateOffset(days=365)
            
            all_series_data = []
            current_start = start_date
            
            while current_start < end_date:
                current_end = min(current_start + pd.DateOffset(days=60), end_date)
                
                # Format dates for API
                start_str = current_start.strftime('%Y-%m-%dT00:00:00Z')
                end_str = current_end.strftime('%Y-%m-%dT23:59:59Z')
                
                print(f"Requesting flow series data for period: {start_str} to {end_str}")
                
                payload = {
                    "data": {
                        "type": "flow-series-report",
                        "attributes": {
                            "timeframe": {
                                "start": start_str,
                                "end": end_str
                            },
                            "interval": "daily",
                            "statistics": [
                                "average_order_value", "bounce_rate", "bounced", "bounced_or_failed",
                                "bounced_or_failed_rate", "click_rate", "click_to_open_rate", "clicks",
                                "clicks_unique", "conversion_rate", "conversion_uniques", "conversion_value",
                                "conversions", "delivered", "delivery_rate", "failed", "failed_rate",
                                "open_rate", "opens", "opens_unique", "recipients", "revenue_per_recipient",
                                "spam_complaint_rate", "spam_complaints", "unsubscribe_rate",
                                "unsubscribe_uniques", "unsubscribes"
                            ],
                            "conversion_metric_id": self.shopify_placed_order_metric_id
                        }
                    }
                }

                # Retry logic with exponential backoff for rate limiting
                max_retries = 5
                retry_count = 0
                
                while retry_count < max_retries:
                    try:
                        response = requests.post(
                            'https://a.klaviyo.com/api/flow-series-reports',
                            json=payload,
                            headers=headers
                        )
                        
                        if response.status_code == 429:
                            # Rate limited - wait longer before retrying
                            retry_delay = (2 ** retry_count) * 5000  # Exponential backoff starting at 5 seconds
                            print(f"Rate limited. Retrying in {retry_delay/1000} seconds... (attempt {retry_count + 1}/{max_retries})")
                            self.delay(retry_delay)
                            retry_count += 1
                            continue
                        
                        response.raise_for_status()
                        response_data = response.json()
                        print(f"Successfully retrieved data for period: {start_str} to {end_str}")
                        print(f"Response data: {response_data}")
                        
                        # Extract series data from this period
                        if 'data' in response_data and 'attributes' in response_data['data']:
                            attributes = response_data['data']['attributes']
                            if 'results' in attributes and 'date_times' in attributes:
                                results = attributes['results']
                                date_times = attributes['date_times']
                                
                                # Transform the data structure to flatten it
                                for result in results:
                                    groupings = result.get('groupings', {})
                                    statistics = result.get('statistics', {})
                                    
                                    # Create one row per date for each result
                                    for i, date_time in enumerate(date_times):
                                        row = {
                                            'date_time': date_time,
                                            **groupings  # Add all grouping fields
                                        }
                                        
                                        # Add statistics for this date index
                                        for stat_name, stat_values in statistics.items():
                                            if i < len(stat_values):
                                                row[stat_name] = stat_values[i]
                                            else:
                                                row[stat_name] = None
                                        
                                        all_series_data.append(row)
                                
                                print(f"Retrieved {len(results)} result groups with {len(date_times)} dates each")
                            else:
                                print("No results or date_times found in response")
                        else:
                            print("No data/attributes found in response")
                        
                        break  # Success, exit retry loop
                        
                    except requests.exceptions.RequestException as e:
                        if retry_count >= max_retries - 1:
                            print(f"Failed to retrieve data after {max_retries} attempts: {str(e)}")
                            break
                        retry_count += 1
                        retry_delay = (2 ** retry_count) * 2000  # Exponential backoff
                        print(f"Request failed. Retrying in {retry_delay/1000} seconds... (attempt {retry_count}/{max_retries})")
                        self.delay(retry_delay)
                
                # Move to next period
                current_start = current_end
                
                # Add 30+ second delay between requests to respect 2/minute rate limit
                self.delay(35000)  # 35 second delay between periods to stay under 2/minute
            
            # Return the flattened data directly since it's already processed
            print(f"Total flow series data points retrieved: {len(all_series_data)}")
            return all_series_data
            
        except Exception as e:
            print(f'Error retrieving flow series report: {str(e)}')
            return None

    async def get_segment_values_report(self):
        if not self.shopify_placed_order_metric_id:
            print('Cannot get segment values report: Shopify Placed Order metric ID not found')
            return None

        try:
            headers = {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Revision': '2024-10-15',
                'Authorization': f'Klaviyo-API-Key {self.api_key}'
            }
            
            payload = {
                "data": {
                    "type": "segment-values-report",
                    "attributes": {
                        "timeframe": {
                            "key": "last_365_days"
                        },
                        "statistics": [
                            "members_added", "members_removed", "net_members_changed", "total_members"
                        ]
                    }
                }
            }
            
            response = requests.post(
                'https://a.klaviyo.com/api/segment-values-reports',
                json=payload,
                headers=headers
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f'Error retrieving segment values report: {str(e)}')
            return None

    def _flatten_object(self, obj, prefix=''):
        flattened = {}
        
        for key, value in obj.items():
            new_key = f"{prefix}_{key}" if prefix else key
            
            if isinstance(value, dict):
                flattened.update(self._flatten_object(value, new_key))
            elif isinstance(value, list):
                # For arrays, convert to a string representation
                flattened[new_key] = json.dumps(value)
            else:
                flattened[new_key] = value
                
        return flattened

    def prepare_parquet_data(self, items, values_report=None, messages_data=None):
        results = []
        
        # Process each item from the API
        for item in items:
            item_id = item.get('id')
            flattened_data = {
                'id': item_id,
                'type': item.get('type'),
                **self._flatten_object(item.get('attributes', {}))
            }
            
            # If we have messages data for this item, add it as a JSON string
            if messages_data and item_id in messages_data:
                flattened_data['messages'] = json.dumps(messages_data[item_id])
            
            # If we have a values report, find and add the corresponding statistics
            if values_report and 'data' in values_report:
                report_data = values_report['data']
                if 'attributes' in report_data and 'results' in report_data['attributes']:
                    for stats in report_data['attributes']['results']:
                        if stats.get("groupings").get(item.get('type')+'_id') == item_id:
                            for stat_key, stat_value in stats.get('statistics', {}).items():
                                flattened_data[f'stats_{stat_key}'] = stat_value
            
            results.append(flattened_data)
            
        return results

    def prepare_series_parquet_data(self, series_data):
        print(f"Processing series data: {len(series_data) if series_data else 0} records")
        
        if not series_data:
            print("No series data found")
            return []

        # Convert date_time strings to datetime objects
        for record in series_data:
            if 'date_time' in record and record['date_time']:
                try:
                    record['date_time'] = pd.to_datetime(record['date_time'])
                except Exception as e:
                    print(f"Error converting date_time: {record['date_time']}, error: {e}")
                    record['date_time'] = None

        print(f'Total series entries to process: {len(series_data)}')
        return series_data

    # Main interface methods
    def getData(self, message):
        try:
            print("Starting Klaviyo data retrieval...")
            self.message = message
            initialized = asyncio.run(self.initialize())
            if not initialized:
                print("Failed to initialize the processor")
                return None

            raw_data = {}
            
            # Process based on message name
            if message.get('name') == 'klaviyo_campaigns':
                print("Retrieving campaigns...")
                campaigns = asyncio.run(self.retrieve_campaigns())
                
                if campaigns:
                    print("Retrieving campaign messages...")
                    campaign_messages = asyncio.run(self.retrieve_all_campaign_messages(campaigns))
                    
                    print("Retrieving campaign values report...")
                    campaign_values_report = asyncio.run(self.get_campaign_values_report())
                    
                    raw_data = {
                        'campaigns': campaigns,
                        'campaign_messages': campaign_messages,
                        'campaign_values_report': campaign_values_report
                    }
                    
            elif message.get('name') == 'klaviyo_flows':
                print("Retrieving flows...")
                flows = asyncio.run(self.retrieve_flows())

                if flows:
                    print("Retrieving flow values report...")
                    flow_values_report = asyncio.run(self.get_flow_values_report())

                    print("Retrieving flow series report...")
                    flow_series_report = asyncio.run(self.get_flow_series_report())

                    raw_data = {
                        'flows': flows,
                        'flow_values_report': flow_values_report,
                        'flow_series_report': flow_series_report
                    }
                    
            elif message.get('name') == 'klaviyo_segments':
                print("Retrieving segments...")
                segments = asyncio.run(self.retrieve_segments())
                
                if segments:
                    print("Retrieving segment values report...")
                    segment_values_report = asyncio.run(self.get_segment_values_report())
                    
                    raw_data = {
                        'segments': segments,
                        'segment_values_report': segment_values_report
                    }
            else:
                print(f"Unsupported message name: {message.get('name')}")
                return None
            
            print("Klaviyo data retrieval completed successfully")
            return raw_data
            
        except Exception as e:
            print(f'Error in data retrieval: {str(e)}')
            traceback.print_exc() 
            return None

    def processData(self, raw_data):
        try:
            print("Processing Klaviyo data...")
            
            if not raw_data:
                print("No data to process")
                return None
                
            processed_data = {}
            
            # Process campaigns data
            if 'campaigns' in raw_data and raw_data['campaigns']:
                print("Processing campaign data...")
                campaign_data = self.prepare_parquet_data(
                    raw_data['campaigns'], 
                    raw_data.get('campaign_values_report'),
                    raw_data.get('campaign_messages')
                )
                processed_data['campaigns'] = campaign_data
                
            # Process flows data
            if 'flows' in raw_data and raw_data['flows']:
                print("Processing flow data...")
                flow_data = self.prepare_parquet_data(
                    raw_data['flows'],
                    raw_data.get('flow_values_report'),
                    raw_data.get('flow_messages')
                )
                processed_data['flows'] = flow_data
                
            # Process flow series data separately
            if 'flow_series_report' in raw_data and raw_data['flow_series_report']:
                print("Processing flow series report data...")
                flow_series_data = self.prepare_series_parquet_data(
                    raw_data['flow_series_report']
                )
                if flow_series_data:
                    processed_data['flow_series_reports'] = flow_series_data
                    print(f"Added {len(flow_series_data)} flow series records to processed data")
                else:
                    print("No flow series data was processed")
                
            # Process segments data
            if 'segments' in raw_data and raw_data['segments']:
                print("Processing segment data...")
                segment_data = self.prepare_parquet_data(
                    raw_data['segments'], 
                    raw_data.get('segment_values_report')
                )
                processed_data['segments'] = segment_data
                
            print("Data processing completed successfully")
            print(f"Processed data keys: {list(processed_data.keys())}")
            return processed_data
            
        except Exception as e:
            print(f'Error in data processing: {str(e)}')
            traceback.print_exc()
            return None

    def writeData(self, processed_data):
        try:
            print("Writing processed data to S3...")
            
            if not processed_data:
                print("No processed data to write")
                return None
                
            if not self.s3_bucket:
                print("No S3 bucket specified for writing data")
                return None
            
            # Get the current datetime
            rundate = pd.Timestamp.now()
            
            results = {}
            
            # Write each data type to a separate parquet file
            for data_type, data in processed_data.items():
                if not data:
                    print(f"No {data_type} data to write")
                    continue
                    
                # Convert to pandas DataFrame
                df = pd.DataFrame(data)

                df = self.convert_dataframe_types(df, self.message["dataStructure"])
                
                # Add rundate column to the DataFrame
                df['rundate'] = rundate
                
                # Convert DataFrame to pyarrow Table
                table = pa.Table.from_pandas(df)
                
                # Write to BytesIO instead of file
                parquet_buffer = BytesIO()
                pq.write_table(table, parquet_buffer)
                
                # Reset buffer position
                parquet_buffer.seek(0)
                
                # Upload to S3
                formatted_time = pd.Timestamp.now().strftime('%Y-%m-%d_%H-%M')
                
                # Use different folder for flow series data
                if data_type == 'flow_series_reports':
                    folder_name = 'klaviyo_flows_series'
                else:
                    folder_name = self.message['name']
                
                s3_key = f"{folder_name}/organization={self.message['orgId']}/{formatted_time}-{data_type}.parquet"

                # Delete existing files in the specified partition
                self.delete_existing_files(self.s3_bucket, f"{folder_name}/organization={self.message['orgId']}/")
                
                self.s3_client.upload_fileobj(
                    parquet_buffer, 
                    self.s3_bucket, 
                    s3_key
                )
                
                results[data_type] = {
                    's3_location': f"s3://{self.s3_bucket}/{s3_key}",
                    'row_count': len(data)
                }
                
                print(f"Saved {data_type}.parquet with {len(data)} rows to S3")
                
            print("Data writing completed successfully")
            return results
            
        except Exception as e:
            print(f'Error in data writing: {str(e)}')
            return None

    def convert_dataframe_types(self, df, data_structure):

        for column, spec in data_structure.items():
            if column in df.columns:
                dtype = spec['type'].lower()
                
                # Type conversion mapping
                if dtype == 'utf8':
                    df[column] = df[column].astype(str)
                elif dtype == 'double':
                    df[column] = pd.to_numeric(df[column], errors='coerce').astype(float)
                elif dtype == 'boolean':
                    df[column] = df[column].astype(bool)
                elif dtype == 'bigint':
                    df[column] = pd.to_numeric(df[column], errors='coerce').astype('int64')
                elif dtype == 'datetime':
                    df[column] = pd.to_datetime(df[column])
        
        return df

    def delete_existing_files(self, bucket, path):
        # List all objects in the specified S3 path
        response = self.s3_client.list_objects_v2(Bucket=bucket, Prefix=path)
        if 'Contents' in response:
            for obj in response['Contents']:
                print(f"Deleting {obj['Key']} from {bucket}")
                self.s3_client.delete_object(Bucket=bucket, Key=obj['Key'])

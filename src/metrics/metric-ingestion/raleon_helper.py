from datetime import datetime
import psycopg2
import boto3
import json
import os
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding
import base64

def get_database_info():
    session = boto3.session.Session()
    client = session.client(
        service_name='secretsmanager',
        region_name='us-east-1',
        endpoint_url='https://vpce-060670840daa313c8-mcqyvy4b.secretsmanager.us-east-1.vpce.amazonaws.com'
    )
    response = client.get_secret_value(SecretId=os.environ['RALEON_DB'])
    print(response)
    return json.loads(response['SecretString'])

def query_database(query, values):
    """Execute single query with dedicated connection"""
    database_info = get_database_info()
    conn = psycopg2.connect(
        user=database_info['username'],
        password=database_info['password'],
        host=database_info['host'],
        dbname=database_info['dbname'],
        port=database_info['port']
    )
    try:
        with conn:  # handles commit/rollback
            with conn.cursor() as cursor:
                cursor.execute(query, values)
                if query.strip().upper().startswith('SELECT') or query.strip().upper().startswith('WITH'):
                    results = cursor.fetchall()
                    return results if results is not None else []
                return []  # Return empty list for non-SELECT queries
    finally:
        conn.close()

def update_last_run(org_id, metric_name):
    query_database("UPDATE organizationmetric SET lastrundate = %s WHERE orgid = %s and metricid = (Select id From metric where name=%s)", (datetime.now(), org_id, metric_name))

def update_last_run_error(org_id, metric_name, error):
    query_database("UPDATE organizationmetric SET errordate = %s, error = %s WHERE orgid = %s and metricid = (Select id From metric where name=%s)", (datetime.now(), error, org_id, metric_name))

def update_klaviyo_error(org_id: int, error_message: str):
    query = """
        INSERT INTO organizationsettings (key, value, organizationid)
        VALUES ('klaviyo_error', %s, %s)
        ON CONFLICT (key, organizationid) WHERE key = 'klaviyo_error' AND organizationid = %s 
        DO UPDATE SET value = EXCLUDED.value;
    """
    query_database(query, ('true', org_id, org_id))

def remove_klaviyo_error(org_id: int):
    query = """
        DELETE FROM organizationsettings 
        WHERE key = 'klaviyo_error' 
        AND organizationid = %s;
    """
    query_database(query, (org_id,))

def get_klaviyo_integration_by_org_id(org_id):
    query = '''SELECT "value" FROM "public"."organizationkeys" WHERE "organizationid" = %s AND key = %s AND secretkeyid = %s LIMIT 1'''
    rows = query_database(query, (org_id, 'Klaviyo-API-Key', 'default'))

    queryMappings = '''SELECT "fieldmappings" FROM "public"."integration" WHERE "name" = %s LIMIT 1'''
    mappingRow = query_database(queryMappings, ('Klaviyo',))  # Single-item tuple

    if rows:
        klaviyo_info = {
            'value': rows[0][0],
            'fieldmappings': mappingRow[0][0]
        }
        return klaviyo_info
    else:
        print(f"No information found for organization: {org_id}")
        return None

def get_excluded_product_ids(org_id):
    query = '''SELECT excludedproductids FROM "public"."loyaltyprogram" WHERE "orgid" = %s LIMIT 1'''
    rows = query_database(query, (org_id,))
    
    if rows and rows[0][0]:
        return rows[0][0]
    return []

def decrypt(text):
    session = boto3.session.Session()
    client = session.client(
        service_name='secretsmanager',
        region_name='us-east-1',
        endpoint_url='https://vpce-060670840daa313c8-mcqyvy4b.secretsmanager.us-east-1.vpce.amazonaws.com'
    )
    response = client.get_secret_value(SecretId=os.environ['ORGANIZATION_ENCRYPTION_KEY'])
    secret = json.loads(response['SecretString'])

    key = secret['default']

    text_parts = text.split(':')
    if len(text_parts) == 0:
        raise ValueError('Invalid encrypted text')

    iv = bytes.fromhex(text_parts.pop(0))
    encrypted_text = bytes.fromhex(':'.join(text_parts))
    key_bytes = key.encode()

    # Ensure the key length is suitable for AES-256
    if len(key_bytes) != 32:
        raise ValueError("Key must be 32 bytes (256 bits) for AES-256.")

    cipher = Cipher(algorithms.AES(key_bytes), modes.CBC(iv), backend=default_backend())
    decryptor = cipher.decryptor()
    decrypted = decryptor.update(encrypted_text) + decryptor.finalize()

    return decrypted.decode()
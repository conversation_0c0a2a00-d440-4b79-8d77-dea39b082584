from io import BytesIO
import json
import os
import pandas as pd
import requests
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
from scipy.stats import mstats
import datetime
import boto3
import athena_helper
import raleon_helper
import psycopg2
import psycopg2.extras
from shopify_helper import bulk_update_metafields, get_shop_info_by_org_id, get_api_access_token, add_tags_customer, remove_tags_customer
import asyncio
from segment_name_mapping import FRIENDLY_SEGMENT_NAMES

class LoyaltySegment:
	def getData(self, message):
		self.message = message
		database = os.environ["ATHENA_DB"]
		query_string = f"""
		WITH aggregated_refunds AS (
			SELECT 
				order_id, 
				COALESCE(SUM(refund_amount), 0) AS total_refund_amount  -- Pre-aggregate refund amounts by `order_id`
			FROM 
				"{database}"."order_refunds"
			WHERE 
				organization = '{message['orgId']}'
			GROUP BY 
				order_id
		),
		aggregated_orders AS (
			SELECT 
				o.id AS order_id,
				o.customer AS user,
				o.created_at AS date,
				MAX(o.subtotal_price) AS subtotal_price,  -- Aggregate line items at the order level
				ARRAY_AGG(o.item_sku) AS item_ids  -- Aggregate SKUs into an array
			FROM 
				"{database}"."filtered_orders" o
			WHERE 
				o.organization = '{message['orgId']}'
				AND o.customer != '0'
				AND o.customer NOT IN (
					SELECT customerId 
					FROM "{database}".excluded_customers 
					WHERE organization = '{message['orgId']}'
				)
			GROUP BY 
				o.id, o.customer, o.created_at
		)
		SELECT 
			o.user, 
			'PRODUCT' AS product, 
			o.date, 
			o.subtotal_price - COALESCE(r.total_refund_amount, 0) AS price,
			o.order_id AS id
		FROM 
			aggregated_orders o
		LEFT JOIN 
			aggregated_refunds r ON o.order_id = r.order_id  -- Ensure that orders without refunds are included
		WHERE 
			o.user != '0'
			AND o.user NOT IN (
				SELECT customerId 
				FROM "{database}".excluded_customers 
				WHERE organization = '{message['orgId']}'
			);
		"""
		if 'queryOverride' in message and message['queryOverride']:
			query_string = message['queryOverride']
			if query_string:
				query_string = query_string.replace("{orgId}", str(message['orgId']))
				query_string = query_string.replace("{database}", os.environ["ATHENA_DB"])
			else:
				print("query_string is empty or None")
		columns = ['customer', 'item_name', 'created_at', 'total_price', 'id']
		return self.query_athena_and_get_df(query_string, columns)
	
	def processData(self, df):
		if df is None or df.empty or df.shape[0] < 50:
			print("No data to process")
			return
		df_prev_segment = self.get_previous_segment()
		# 1. Compute RFM metrics
		max_date = df['created_at'].max()
		rfm = df.groupby('customer').agg({
			'created_at': lambda x: (max_date - x.max()).days,  # recency
			'customer': 'count',  # frequency
			'total_price': 'sum'  # monetary
		}).rename(columns={
			'created_at': 'recency',
			'customer': 'frequency',
			'total_price': 'monetary'
		})

		rfm['aov'] = rfm['monetary'] / rfm['frequency'].replace(0, 1) 
		if 'variableOverride' in self.message and self.message['variableOverride'].strip():
			variableOverride = self.message['variableOverride']
			try:
				# Parse the JSON string into a dictionary
				variableOverride_dict = json.loads(variableOverride)
				
				# Access the 'skipwinsorize' key
				skipwinsorize = variableOverride_dict.get('skipwinsorize', None)
				
				print(skipwinsorize)
			except json.JSONDecodeError:
				print("Invalid JSON format")

		else:
			rfm['frequency'] = mstats.winsorize(rfm['frequency'], limits=[0.0, 0.01])
			rfm['recency'] = mstats.winsorize(rfm['recency'], limits=[0.0, 0.01])

		# Calculate the median of AOV
		median_aov = rfm['aov'].median()

		# Calculate the absolute deviation from the median
		abs_deviation = abs(rfm['aov'] - median_aov)

		# Calculate the MAD (Median Absolute Deviation)
		# Multiply by 40 (need to figure out how to make this dynamic, but this removes extreme outliers on the high end)
		MAD = abs_deviation.median()
		multiplier = 40 * MAD

		outliers = rfm[rfm['aov'] > (median_aov + multiplier)]
		remaining_users = rfm[~rfm.isin(outliers)].dropna()
		new_users = remaining_users[(remaining_users['recency'] <= 90) & (remaining_users['frequency'] == 1)]
		remaining_users = remaining_users.drop(new_users.index)
		if len(remaining_users) < 10:
			print("Not enough data to segment")
			return
		# Cluster Remaining Users
		scaler = StandardScaler()
		rfm_scaled = scaler.fit_transform(remaining_users)

		remaining_users['composite_score'] = (
			(remaining_users['aov'] / remaining_users['aov'].max() if remaining_users['aov'].max() > 0 else 0) +
			(remaining_users['monetary'] / remaining_users['monetary'].max() if remaining_users['monetary'].max() > 0 else 0)
		)

		features_for_clustering = remaining_users[['recency', 'frequency', 'composite_score']]
		rfm_scaled = scaler.fit_transform(features_for_clustering)
		if features_for_clustering.isna().any().any():
			print(f"NaN values detected in clustering features. Rows with NaN: {features_for_clustering.isna().any(axis=1).sum()}")
			features_for_clustering = features_for_clustering.dropna()
			remaining_users = remaining_users.loc[features_for_clustering.index]
		if len(features_for_clustering) < 10:
			print("Not enough data for clustering after removing NaN values")
			return None
		kmeans = KMeans(n_clusters=3, random_state=42)
		remaining_users['Cluster'] = kmeans.fit_predict(rfm_scaled)

		# Label clusters (This is a basic labeling based on common RFM principles. You might adjust after inspecting clusters.)
		# Sort by cluster centers
		# sorted_centers = remaining_users.groupby('Cluster').mean().sort_values(['recency', 'frequency', 'monetary'], 
		# 																	ascending=[True, False, False])
		try:
			remaining_users['recency_score'] = pd.qcut(remaining_users['recency'], 5, labels=[5, 4, 3, 2, 1])
		except ValueError:
			remaining_users['recency_score'] = pd.cut(remaining_users['recency'], 5, labels=[5, 4, 3, 2, 1])

		try:
			remaining_users['frequency_score'] = pd.qcut(remaining_users['frequency'], 5, labels=[1, 2, 3, 4, 5])
		except ValueError:
			remaining_users['frequency_score'] = pd.cut(remaining_users['frequency'], 5, labels=[1, 2, 3, 4, 5])

		try:
			remaining_users['monetary_score'] = pd.qcut(remaining_users['monetary'], 5, labels=[1, 2, 3, 4, 5])
		except ValueError:
			remaining_users['monetary_score'] = pd.cut(remaining_users['monetary'], 5, labels=[1, 2, 3, 4, 5])

		# Compute initial weights based on correlation with AOV
		correlation_matrix = remaining_users[['recency', 'frequency', 'monetary', 'aov']].corr()
		importance_scores = correlation_matrix['aov'].abs().drop('aov')
		initial_weights = importance_scores / importance_scores.sum()

		# Adjust weights manually based on business insight
		adjusted_weights = {
			'recency': max(0.2, initial_weights['recency']),  # Ensure Recency has at least 20% weight
			'frequency': max(0.2, initial_weights['frequency']),  # Ensure Frequency has at least 20% weight
			'monetary': max(0.2, initial_weights['monetary'])  # Ensure Monetary has at least 20% weight
		}

		# Normalize the adjusted weights to sum to 1
		total_weight = sum(adjusted_weights.values())
		weights = {k: v / total_weight for k, v in adjusted_weights.items()}

		# Calculate weighted score
		remaining_users['weighted_score'] = (
			remaining_users['recency_score'].astype(int) * weights['recency'] +
			remaining_users['frequency_score'].astype(int) * weights['frequency'] +
			remaining_users['monetary_score'].astype(int) * weights['monetary']
		)
		
		
		# Sort customers based on the weighted score
		cluster_means = remaining_users.groupby('Cluster')['weighted_score'].mean().reset_index()
		sorted_cluster_means = cluster_means.sort_values(by='weighted_score', ascending=False).reset_index(drop=True)

		cluster_map = {
			sorted_cluster_means.loc[0, 'Cluster']: 'Very Loyal',
			sorted_cluster_means.loc[1, 'Cluster']: 'Growth',
			sorted_cluster_means.loc[2, 'Cluster']: 'Not Loyal'
		}


		remaining_users['segment'] = remaining_users['Cluster'].map(cluster_map)

		# Add New Users back with the 'New Users' label
		new_users['Cluster'] = 'New'
		new_users['segment'] = 'New Users'

		# Add Outliers back with the 'Unclassified' label
		outliers['Cluster'] = 'Unclassified'
		outliers['segment'] = 'Unclassified'

		# Combine the dataframes
		final_rfm = pd.concat([remaining_users, new_users, outliers])

		final_rfm.reset_index(inplace=True)
		final_rfm = final_rfm.merge(df_prev_segment, on='customer', how='left')
		# Filter rows where previous_segment is different from segment
		final_rfm = final_rfm[final_rfm['previoussegment'] != final_rfm['segment']]
		final_rfm['rundate'] = pd.Timestamp.now()
		
		return final_rfm

	def writeData(self, final_rfm):
		if final_rfm is None or final_rfm.empty:
			print("No changes in segments detected. No data written.")
			return
		s3 = boto3.client('s3')
		self.update_shopify(final_rfm)	
		self.update_postgres(final_rfm)
		self.update_shopify_tags(final_rfm)
		
		parquet_buffer = BytesIO()
		final_rfm[['customer', 'recency', 'frequency', 'monetary', 'segment', 'previoussegment', 'rundate']].to_parquet(parquet_buffer, index=False)
		if 'CURATED_BUCKET' not in os.environ:
			raise ValueError("CURATED_BUCKET environment variable is not set")
		formatted_time = pd.Timestamp.now().strftime('%Y-%m-%d_%H-%M')
		s3.put_object(Bucket=os.environ["CURATED_BUCKET"], Key=f"rfm_segments/organization={self.message['orgId']}/{formatted_time}.parquet", Body=parquet_buffer.getvalue())

	def get_previous_segment(self):
		database = os.environ["ATHENA_DB"]
		query_string = f"SELECT customer, max_by(segment, rundate) as previoussegment FROM \"{database}\".\"rfm_segments\" WHERE organization='{str(self.message['orgId'])}' Group by customer;"
		columns = ['customer', 'previoussegment']
		return self.query_athena_and_get_df(query_string, columns)
	
	def query_athena_and_get_df(self, query_string, columns):
		if 'ATHENA_OUTPUT_BUCKET' not in os.environ:
			raise ValueError("ATHENA_OUTPUT_BUCKET environment variable is not set")
		response = athena_helper.run_athena_query_v2(query_string, os.environ["ATHENA_OUTPUT_BUCKET"])
		query_id = response['QueryExecutionId']

		if athena_helper.wait_for_query_execution(query_id):
			results = athena_helper.get_query_results(query_id)
			rows = [row['Data'] for row in results['ResultSet']['Rows'][1:]]  # [1:] to skip header
			df = pd.DataFrame(rows, columns=columns)
			for col in df.columns:
				if df[col].apply(isinstance, args=(dict,)).any():
					print(f"Column {col} has dictionary values.")
				df[col] = df[col].apply(lambda x: self.extract_value_from_dict(x, col))
			return df
		else:
			raise Exception("Query failed!")

	def extract_value_from_dict(self, row, column_name):
		if isinstance(row, dict) and 'VarCharValue' in row:
			value = row['VarCharValue']
			if column_name == 'created_at':
				return pd.to_datetime(value)
			elif column_name == 'total_price':
				return float(value)
			else:
				return value
		return row

	def update_shopify(self, final_rfm):
		print("Updating Shopify meta fields...")
		shop_info = get_shop_info_by_org_id(self.message['orgId'])
		if(shop_info is None):
			print("Shop not found")
			return
		sessionToken = get_api_access_token(shop_info['shopDomain'])
		if(sessionToken is None):
			print("Session token not found")
			return
		jsonl_content = self.generate_jsonl_content(final_rfm[['customer', 'segment']].values.tolist())
		bulk_id = asyncio.run(bulk_update_metafields(jsonl_content, shop_info['shopDomain'], sessionToken))		
		print(f"Bulk ID: {bulk_id}")
	
	def generate_jsonl_content(self, customers_scores):
		jsonl_lines = []
		for customer_value, segment_value in customers_scores:
			friendly_segment_name = FRIENDLY_SEGMENT_NAMES.get(segment_value)
			metafield_data = { "input" : {
				"key": "loyalty_segment",
				"namespace": "raleonInfo",
				"ownerId": f"gid://shopify/Customer/{customer_value}",
				"type": "single_line_text_field",
				"value": friendly_segment_name
			}
			}
			jsonl_lines.append(json.dumps(metafield_data))

		return '\n'.join(jsonl_lines)

	def execute_chunked_query(self, cursor, query, data, orgid, chunk_size=1000):
		for i in range(0, len(data), chunk_size):
			chunk = data[i:i + chunk_size]
			cursor.execute(query, (list(chunk), orgid))
			for row in cursor.fetchall():
				yield row

	def update_postgres(self, final_rfm):
		print("Updating Postgres")
		database_info = raleon_helper.get_database_info()
		conn = psycopg2.connect(
			user=database_info['username'],
			password=database_info['password'],
			host=database_info['host'],
			dbname=database_info['dbname'],
			port=database_info['port']
		)
		
		try:
			cur = conn.cursor()
			print("Connected to database")
			customer_segment_mapping = {row['customer']: row['segment'] for index, row in final_rfm.iterrows()}

			query = """
			SELECT identityvalue, raleonuserid FROM raleonuseridentity
			WHERE identitytype = 'customer_id' AND identityvalue = ANY(%s) AND orgid = %s;
			"""
			unique_identityvalues = tuple(customer_segment_mapping.keys())
			
			existing_mapping = {}
			for row in self.execute_chunked_query(cur, query, unique_identityvalues, self.message['orgId'], chunk_size=1000):
				existing_mapping[row[0]] = row[1]

			print(f"Found {len(existing_mapping)} existing records")

			new_identityvalues = [iv for iv in unique_identityvalues if iv not in existing_mapping and iv != 0]
			print(f"Found {len(new_identityvalues)} new records")
			if new_identityvalues:
				values_list = ', '.join(
					cur.mogrify("(%s, %s, %s, %s)", (iv, customer_segment_mapping[iv], int(self.message['orgId']), i+1)).decode()
					for i, iv in enumerate(new_identityvalues)
				)
        
				cte_query = f"""
				WITH new_users AS (
					INSERT INTO raleonuser (name) VALUES {', '.join(['(DEFAULT)'] * len(new_identityvalues))} RETURNING id
				), numbered_new_users AS (
					SELECT id, ROW_NUMBER() OVER () AS rn
					FROM new_users
				), new_values(identityvalue, loyaltysegment, orgid, rn) AS (
					VALUES {values_list}
				), numbered_new_values AS (
					SELECT * FROM new_values
				)
				INSERT INTO raleonuseridentity (identityvalue, raleonuserid, identitytype, loyaltysegment, orgid, createddate)
				SELECT nv.identityvalue, nu.id, 'customer_id', nv.loyaltysegment, nv.orgid, NOW()
				FROM numbered_new_users nu
				JOIN numbered_new_values nv ON nu.rn = nv.rn;
				"""
				
				# Execute the CTE
				cur.execute(cte_query)

				print("Done inserting new records")

			update_data = [(segment, identityvalue) for identityvalue, segment in customer_segment_mapping.items() if identityvalue in existing_mapping]

			if update_data:
				update_sql = """
				UPDATE raleonuseridentity AS r
				SET loyaltysegment = data.loyaltysegment
				FROM (VALUES %s) AS data (loyaltysegment, identityvalue)
				WHERE r.identityvalue = data.identityvalue AND r.identitytype = 'customer_id';
				"""

				psycopg2.extras.execute_values(
					cur, update_sql, update_data, template=None, page_size=1000
				)

			conn.commit()
		except Exception as e:
			print(f"An error occurred: {e}")
			conn.rollback()
		finally:
			print("Closing connection")
			cur.close()
			conn.close()

	def update_shopify_tags(self, final_rfm):
		queue_url = os.environ['SEGMENT_WRITER_QUEUE_URL']
		#send message to queue
		sqs = boto3.client('sqs')
		message_body = {
			'orgId': self.message['orgId'],
			'runDate': final_rfm['rundate'].max().isoformat()
		}
		message_body_str = json.dumps(message_body)
		print(f"Sending message to queue: {message_body_str}")
		response = sqs.send_message(
			QueueUrl=queue_url,
			MessageBody=message_body_str
		)
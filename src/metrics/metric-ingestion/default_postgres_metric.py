import boto3
import time
import json
from typing import List, Dict, Any
import os
from datetime import datetime, timedelta
import pandas as pd
import psycopg2
import psycopg2.extras
import athena_helper
import raleon_helper

class AthenaWorkGroup:
    METRIC = 'metric'
    ADHOC = 'adhoc'

class DefaultPostgresMetric:
    def __init__(self):
        self.s3_client = boto3.client('s3')

    def getData(self, message):
        self.message = message
        if message["query"]:
            return self.get_metric_data(self.message)
        else:
            raise ValueError("Query is not set")
        
    def processData(self, data):
        return data
    
    def writeData(self, data):
        if not data:
            print("No data to write")
            return
        if self.message["dataStructure"]:
            metrics_data_url = self.write_metrics_data_to_s3(data, self.message)
            print(f"metricsDataUrl 👉 {metrics_data_url}")

        self.write_to_postgres(data)


    def write_to_postgres(self, data):
        print("Starting Postgres write...")
        database_info = raleon_helper.get_database_info()
        conn = psycopg2.connect(
            user=database_info['username'],
            password=database_info['password'],
            host=database_info['host'],
            dbname=database_info['dbname'],
            port=database_info['port']
        )

        print("Database connected")
        
        try:
            cur = conn.cursor()
            field_mapping = self.message.get('fieldMappings', {})
            field_mapping = json.loads(field_mapping)
            print("Field mapping:", field_mapping)
            if not field_mapping:
                raise ValueError("fieldMappings not provided in message")

            set_clauses = [f"{postgres_field} = data.{source_field}"
                          for postgres_field, source_field in field_mapping.items()]
            set_clauses.append("metricsupdated = NOW()")
            set_clause = ", ".join(set_clauses)
            print("Set clause:", set_clause)

            update_sql = f"""
                UPDATE raleonuseridentity 
                SET {set_clause}
                FROM (VALUES %s) AS data (identityvalue, {', '.join(field_mapping.values())})
                WHERE CAST(raleonuseridentity.identityvalue AS TEXT) = CAST(data.identityvalue AS TEXT)
                AND raleonuseridentity.orgid = {self.message['orgId']};
            """

            print("SQL query:", update_sql)

            df = pd.DataFrame(data)
            update_data = [(str(int(float(row['customer']))), *[int(float(row[field])) for field in field_mapping.values()])
                for _, row in df.iterrows()]
            

            if update_data:
                psycopg2.extras.execute_values(
                    cur, update_sql, update_data,
                    template=None, page_size=1000
                )
                conn.commit()
                print("Update committed successfully")
            else:
                print("No data to update")

        except Exception as e:
            print(f"Error in Postgres write: {str(e)}")
            raise e
        finally:
            cur.close()
            conn.close()
            print("Database connection closed")
    
    def write_metrics_data_to_s3(self, metrics_data, message):
        df = pd.DataFrame(metrics_data)
        print("DataFrame columns:", df.columns)  # Debug print
        print("DataFrame head:", df.head()) 
        # Dynamically convert columns based on dataStructure
        for column, dtype in message['dataStructure'].items():
            print(f"column: {column}, dtype: {dtype}")
            if dtype['type'] == 'float64':
                df[column] = df[column].astype('float64')
            elif dtype['type'] == 'DOUBLE':
                df[column] = df[column].astype('double')
            elif dtype['type'] == 'INT64':
                df[column] = df[column].astype('int64')
            elif dtype['type'] == 'datetime':
                df[column] = pd.to_datetime(df[column])
            elif dtype['type'] == 'UTF8':
                df[column] = df[column].astype(str).str.replace('.0', '')
            elif dtype['type'] == 'string':
                df[column] = df[column].astype('string')

        # Fix runDate conversion - first to int64, then to final type
        df['runDate'] = (df['runDate'].astype('int64').astype('float64') / 1e6).astype('int64')
        
        file_path = f"{message['orgId']}_{message['name']}.parquet"
        df.to_parquet(file_path)

        timestamp = self.generate_timestamp()
        s3_key = f"{message['name']}/organization={message['orgId']}/{message['name']}_{timestamp}.parquet"
        self.s3_client.upload_file(file_path, os.environ["CURATED_BUCKET"], s3_key)

    def get_metric_data(self, message: Dict[str, Any]) -> List[Dict[str, Any]]:
        query = message['query']
        if '{orgId}' not in query:
            raise ValueError("{orgId} placeholder is not in the query. Metric data must be filtered by organization.")
        else:
            query = query.replace("{orgId}", str(message['orgId']))
        if(message['lastRunDate'] != "" and message['lastRunDate'] != None):
            utc_time = datetime.fromisoformat(message['lastRunDate'].replace('Z', '+00:00'))
        else:
            utc_time = datetime.utcnow() - timedelta(days=1)
        if(message['activationDate'] != "" and message['activationDate'] != None):
            activationDate = datetime.fromisoformat(message['activationDate'].replace('Z', '+00:00'))
            query = query.replace("{activationDate}", activationDate.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3])
        else:
            activationDate = datetime(2023, 10, 1, 0, 0, 0, 0) #no activation date use beginning of time
            query = query.replace("{activationDate}", activationDate.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3])
        formatted_date = utc_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        query = query.replace("{lastRunDate}", formatted_date)
        query = query.replace("{database}", os.environ["ATHENA_DB"])
        for key, value in message['variables'].items():
            placeholder = f"{{{key}}}"
            query = query.replace(placeholder, str(value))
        
        print(f"athenaQuery 👉 {query}")
        column_names = list(message['dataStructure'].keys())
        
        if 'ATHENA_OUTPUT_BUCKET' not in os.environ:
            raise ValueError("ATHENA_OUTPUT_BUCKET environment variable is not set")
        
        athena_result = athena_helper.run_athena_query_v2(
            query,
            os.environ['ATHENA_OUTPUT_BUCKET'],
            0,
            AthenaWorkGroup.METRIC,
            message['catalog']
        )
        
        check_table_id = athena_result['QueryExecutionId']
        check_table_query_result = athena_helper.wait_for_query_execution(check_table_id)
        
        if not check_table_query_result:
            raise ValueError("Athena query failed to execute")
        else:
            query_execution_id = check_table_id
            query_result_data = athena_helper.get_query_results(query_execution_id)
            query_data = []
            batch_timestamp = self.generate_timestamp()
            
            if query_result_data['ResultSet'] and query_result_data['ResultSet']['Rows']:
                data = query_result_data['ResultSet']['Rows']
                
                for i in range(1, len(data)):
                    row_data = data[i]['Data']
                    if not row_data:
                        continue
                    
                    row = {}
                    for index, column in enumerate(row_data):
                        column_name = column_names[index]
                        row[column_name] = 0

                        if 'VarCharValue' not in column:
                            continue

                        value = column['VarCharValue']

                        try:
                            date_value = int(time.mktime(time.strptime(value, "%Y-%m-%d %H:%M:%S")))
                            row[column_name] = date_value
                            continue
                        except ValueError:
                            pass 
                        
                        try:
                            float_value = float(value)
                            row[column_name] = float_value
                        except ValueError:
                            row[column_name] = value
                    
                    row['runDate'] = datetime.now()
                    query_data.append(row)

            return query_data

    def generate_timestamp(self):
        date = datetime.now()
        return date.strftime("%Y%m%d_%H%M%S")

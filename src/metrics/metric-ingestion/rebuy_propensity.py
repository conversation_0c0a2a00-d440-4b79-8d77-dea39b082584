from io import BytesIO
import os
import pandas as pd
import numpy as np
import xgboost as xgb
import boto3
import time
import athena_helper
import raleon_helper
import psycopg2
import psycopg2.extras
from datetime import datetime, timedelta
from sklearn.preprocessing import StandardScaler
from sklearn.calibration import CalibratedClassifierCV
from sklearn.model_selection import train_test_split
from sklearn.metrics import (
    roc_auc_score,
    precision_score,
    recall_score,
    f1_score,
    precision_recall_curve,
)
from functools import wraps

def timer(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start = time.time()
        result = func(*args, **kwargs)
        duration = time.time() - start
        print(f"{func.__name__} took {duration:.2f} seconds")
        return result
    return wrapper

class PurchasePropensityScorer:
    def __init__(self):
        # Model parameters can be adjusted as needed
        self.model = xgb.XGBClassifier(
            n_estimators=200,
            max_depth=5,
            learning_rate=0.05,
            subsample=0.8,
            colsample_bytree=0.8,
            min_child_weight=5,
            random_state=42,
            use_label_encoder=False,
            eval_metric='logloss',
        )
        # Other initializations
        self.feature_window_days = 180
        self.prediction_window_days = 90
        self.feature_columns = None
        self.saved_feature_columns = None
        self.optimal_threshold = 0.5  # Default threshold
        self.location_categories = None
        self.loyaltysegment_categories = None
        self.scaler = StandardScaler()
        # Initialize message variable
        self.message = {}
        
    @timer
    def getData(self, message):
        self.message = message
        database = os.environ["ATHENA_DB"]
        pgdatabase = os.environ["POSGRES_DB"]
        
        # Get excluded product IDs
        excluded_products = raleon_helper.get_excluded_product_ids(message['orgId'])
        exclude_clause = ""
        if excluded_products:
            if isinstance(excluded_products, list):
                excluded_products = ','.join(map(str, excluded_products))
            exclude_clause = f"AND o.item_id NOT IN ({excluded_products})"
        
        # Query for orders
        orders_query = f"""
            WITH aggregated_refunds AS (
                SELECT 
                    order_id, 
                    COALESCE(SUM(refund_amount), 0) AS total_refund_amount  -- Pre-aggregate refund amounts by `order_id`
                FROM 
                    "{database}"."order_refunds"
                WHERE 
                    organization = '{message['orgId']}'
                GROUP BY 
                    order_id
            ),
            aggregated_orders AS (
                SELECT 
                    o.id AS order_id,
                    o.customer AS user,
                    o.created_at AS date,
                    MAX(o.subtotal_price) AS subtotal_price,  -- Aggregate line items at the order level
                    ARRAY_AGG(o.item_id) AS item_ids  -- Aggregate SKUs into an array
                FROM 
                    "{database}"."filtered_orders" o
                WHERE 
                    o.organization = '{message['orgId']}'
                    AND o.customer != '0'
                    AND o.customer NOT IN (
                        SELECT customerId 
                        FROM "{database}".excluded_customers 
                        WHERE organization = '{message['orgId']}'
                    )
                    AND item_id IS NOT NULL
                    {exclude_clause}
                GROUP BY 
                    o.id, o.customer, o.created_at
            )
            SELECT 
                o.user, 
                o.date, 
                o.subtotal_price - COALESCE(r.total_refund_amount, 0) AS price,  -- Subtract pre-aggregated refund amounts
                o.item_ids
            FROM 
                aggregated_orders o
            LEFT JOIN 
                aggregated_refunds r ON o.order_id = r.order_id  -- Join aggregated refunds
            WHERE 
                o.user != '0'
                AND o.user NOT IN (
                    SELECT customerId 
                    FROM "{database}".excluded_customers 
                    WHERE organization = '{message['orgId']}'
                )
            GROUP BY 
                o.user, o.date, o.subtotal_price, o.item_ids, r.total_refund_amount;
        """
        
        # Query for customer data
        customer_query = f"""
                    WITH aggregated_refunds AS (
            SELECT 
                order_id, 
                COALESCE(SUM(refund_amount), 0) AS total_refund_amount  -- Pre-aggregate refund amounts by `order_id`
            FROM 
                "{database}"."order_refunds"
            WHERE 
                organization = '{message['orgId']}'
            GROUP BY 
                order_id
        ),
        aggregated_orders AS (
            SELECT 
                o.id AS order_id,
                o.customer AS user,
                o.created_at AS date,
                MAX(o.subtotal_price) AS subtotal_price,  -- Aggregate line items at the order level
                MAX(o.total_discounts) AS total_discounts,
                MAX(o.total_shipping_price) AS total_shipping_price,
                MAX(o.total_price) AS total_price,
                ARRAY_AGG(o.item_id) AS item_ids  -- Aggregate SKUs into an array
            FROM 
                "{database}"."filtered_orders" o
            WHERE 
                o.organization = '{message['orgId']}'
                AND o.customer != '0'
                AND o.customer NOT IN (
                    SELECT customerId 
                    FROM "{database}".excluded_customers 
                    WHERE organization = '{message['orgId']}'
                )
                AND item_id IS NOT NULL
                {exclude_clause}
            GROUP BY 
                o.id, o.customer, o.created_at
        ),
        base_orders AS (
            SELECT 
                o.user,
                o.date,
                o.order_id,
                o.item_ids,
                o.subtotal_price - COALESCE(r.total_refund_amount, 0) AS price,  -- Subtract pre-aggregated refund amounts
                CASE WHEN o.total_discounts > 0 THEN 1 ELSE 0 END AS has_discount,
                CASE WHEN o.total_shipping_price = 0 THEN 1 ELSE 0 END AS has_free_shipping,
                CASE WHEN r.total_refund_amount > 0 THEN 1 ELSE 0 END AS has_refund,  -- Check if the order has refunds
                o.subtotal_price,
                COALESCE(r.total_refund_amount, 0) AS total_refunded
            FROM 
                aggregated_orders o
            LEFT JOIN 
                aggregated_refunds r ON o.order_id = r.order_id  -- Join aggregated refunds
        ),
        customer_metrics AS (
            SELECT 
                user,
                COUNT(*) AS total_orders,
                SUM(price) AS total_spent,
                AVG(price) AS avg_order_value,
                MAX(price) AS max_order_value,
                MIN(date) AS first_purchase,
                MAX(date) AS last_purchase,
                DATE_DIFF('day', MIN(date), CURRENT_DATE) AS days_since_first,
                DATE_DIFF('day', MAX(date), CURRENT_DATE) AS days_since_last,
                DATE_DIFF('day', MIN(date), MAX(date)) AS purchase_span,
                CAST(COUNT(*) AS DOUBLE) / NULLIF(DATE_DIFF('day', MIN(date), MAX(date)), 0) AS purchase_frequency,
                SUM(has_discount) AS orders_with_discounts,
                SUM(has_free_shipping) AS orders_with_free_shipping,
                SUM(has_refund) AS orders_with_refunds,
                SUM(subtotal_price) AS subtotal_spent,
                SUM(total_refunded) AS total_amount_refunded
            FROM 
                base_orders
            GROUP BY 
                user
        ),
        order_intervals AS (
            SELECT 
                user,
                AVG(days_between) AS avg_days_between_orders
            FROM (
                SELECT 
                    user,
                    DATE_DIFF('day', 
                        LAG(date) OVER (PARTITION BY user ORDER BY date),
                        date
                    ) AS days_between
                FROM 
                    base_orders
            ) intervals
            WHERE 
                days_between IS NOT NULL
            GROUP BY 
                user
        ),
        purchase_momentum AS (
            SELECT 
                user,
                CASE 
                    WHEN previous_period_orders = 0 THEN recent_period_orders
                    ELSE (CAST(recent_period_orders AS DOUBLE) / previous_period_orders) - 1
                END AS purchase_momentum
            FROM (
                SELECT 
                    user,
                    COUNT(CASE WHEN date >= DATE_ADD('day', -90, CURRENT_DATE) THEN 1 END) AS recent_period_orders,
                    COUNT(CASE WHEN date >= DATE_ADD('day', -180, CURRENT_DATE) 
                            AND date < DATE_ADD('day', -90, CURRENT_DATE) THEN 1 END) AS previous_period_orders
                FROM 
                    base_orders
                GROUP BY 
                    user
            )
        ),
        value_trajectory AS (
            SELECT 
                user,
                CASE 
                    WHEN COUNT(*) <= 1 THEN 0
                    ELSE REGR_SLOPE(
                        price,
                        order_number
                    )
                END AS value_trajectory
            FROM (
                SELECT 
                    user,
                    price,
                    ROW_NUMBER() OVER (PARTITION BY user ORDER BY date) AS order_number
                FROM 
                    base_orders
            )
            GROUP BY 
                user
        ),
        exploded_items AS (
            SELECT 
                user,
                item_id
            FROM 
                base_orders
            CROSS JOIN UNNEST(item_ids) AS t(item_id)  -- Explode item SKUs into individual rows
        ),
        category_metrics AS (
            SELECT 
                user,
                COUNT(DISTINCT item_id) AS unique_categories,
                CAST(COUNT(DISTINCT item_id) AS DOUBLE) / COUNT(*) AS category_diversity,
                CAST(SUM(CASE WHEN purchase_count > 1 THEN 1 ELSE 0 END) AS DOUBLE) / 
                COUNT(DISTINCT item_id) AS category_repeat_rate
            FROM (
                SELECT 
                    user,
                    item_id,
                    COUNT(*) AS purchase_count
                FROM 
                    exploded_items
                GROUP BY 
                    user, item_id
            )
            GROUP BY 
                user
        ),
        loyalty_data AS (
            SELECT
                ri.id,
                ri.orgid,
                ri.raleonuserid,
                ri.identityvalue AS user,
                ri.loyaltysegment,
                lb.id AS loyaltycurrencyid,
                lb.balance,
                lb.trailingtwelvemonthgranttotal,
                COALESCE(tx.earned, 0) AS earned,
                COALESCE(tx.used, 0) AS used,
                COALESCE(e.engagement_30_days, 0) AS engagement_30_days
            FROM 
                "{pgdatabase}"."public"."raleonuseridentity" ri
            LEFT JOIN 
                "{pgdatabase}"."public"."loyaltycurrencybalance" lb ON ri.raleonuserid = lb.raleonuserid
            LEFT JOIN (
                SELECT
                    loyaltycurrencybalanceid,
                    SUM(CASE WHEN amount < 0 THEN 1 ELSE 0 END) AS used,
                    SUM(CASE WHEN amount > 0 THEN 1 ELSE 0 END) AS earned
                FROM 
                    "{pgdatabase}"."public"."loyaltycurrencytxlog"
                GROUP BY 
                    loyaltycurrencybalanceid
            ) tx ON lb.id = tx.loyaltycurrencybalanceid
            LEFT JOIN (
                SELECT 
                    customer, 
                    engagement_30_days
                FROM (
                    SELECT 
                        customer,
                        engagement_30_days,
                        ROW_NUMBER() OVER (PARTITION BY customer ORDER BY rundate DESC) AS rn
                    FROM 
                        "{database}"."engagement"
                    WHERE 
                        organization = '{message['orgId']}'
                ) ranked
                WHERE 
                    rn = 1
            ) e ON ri.identityvalue = e.customer
            WHERE 
                ri.orgid = {message['orgId']}
        )
        SELECT 
            l.orgid,
            l.user AS identityvalue,
            l.loyaltysegment,
            COALESCE(l.balance, 0.0) AS balance,
            COALESCE(l.trailingtwelvemonthgranttotal, 0.0) AS trailingtwelvemonthgranttotal,
            l.earned,
            l.used,
            COALESCE(cm.total_orders, 0) AS TotalOrders,
            COALESCE(cm.orders_with_discounts, 0) AS OrdersWithDiscounts,
            COALESCE(cm.total_spent, 0) AS TotalSpent,
            COALESCE(cm.subtotal_spent, 0) AS SubtotalSpent,
            COALESCE(cm.orders_with_free_shipping, 0) AS OrdersWithFreeShipping,
            COALESCE(cm.orders_with_refunds, 0) AS OrdersWithRefunds,
            COALESCE(cm.total_amount_refunded, 0) AS TotalAmountRefunded,
            COALESCE(cm.days_since_last, 0) AS DaysSinceLastPurchase,
            COALESCE(oi.avg_days_between_orders, 365) AS AvgTimeBetweenPurchases,
            COALESCE(l.engagement_30_days, 0) AS engagement_30_days,
            COALESCE(cm.total_orders, 0) AS total_orders,
            COALESCE(cm.total_spent, 0) AS total_spent,
            COALESCE(cm.avg_order_value, 0) AS avg_order_value,
            COALESCE(cm.max_order_value, 0) AS max_order_value,
            cm.first_purchase,
            cm.last_purchase,
            COALESCE(cm.days_since_first, 0) AS days_since_first,
            COALESCE(cm.days_since_last, 0) AS days_since_last,
            COALESCE(cm.purchase_span, 0) AS purchase_span,
            COALESCE(cm.purchase_frequency, 0) AS purchase_frequency,
            COALESCE(oi.avg_days_between_orders, 365) AS avg_days_between_orders,
            COALESCE(pm.purchase_momentum, 0) AS purchase_momentum,
            COALESCE(vt.value_trajectory, 0) AS value_trajectory,
            COALESCE(cat.unique_categories, 0) AS unique_categories,
            COALESCE(cat.category_diversity, 0) AS category_diversity,
            COALESCE(cat.category_repeat_rate, 0) AS category_repeat_rate
        FROM 
            loyalty_data l
        LEFT JOIN 
            customer_metrics cm ON l.user = cm.user
        LEFT JOIN 
            order_intervals oi ON l.user = oi.user
        LEFT JOIN 
            purchase_momentum pm ON l.user = pm.user
        LEFT JOIN 
            value_trajectory vt ON l.user = vt.user
        LEFT JOIN 
            category_metrics cat ON l.user = cat.user;
        """
        
        if 'queryOverride' in message and message['queryOverride']:
            orders_query = message['queryOverride'].replace("{orgId}", str(message['orgId']))
            orders_query = orders_query.replace("{database}", database)
            
        orders_df = self.query_athena_and_get_df(orders_query, ['user', 'date', 'price', 'item_ids'])
        customer_df = self.query_athena_and_get_df(customer_query, 
            ['orgid', 'identityvalue', 'loyaltysegment', 'balance','trailingtwelvemonthgranttotal', 'earned', 'used', 'TotalOrders', 'OrdersWithDiscounts', 'TotalSpent', 'SubtotalSpent', 'OrdersWithFreeShipping', 'OrdersWithRefunds', 'TotalAmountRefunded', 'DaysSinceLastPurchase', 'AvgTimeBetweenPurchases', 'engagement_30_days', 'total_orders', 'total_spent', 'avg_order_value', 'max_order_value', 'first_purchase', 'last_purchase', 'days_since_first', 'days_since_last', 'purchase_span', 'purchase_frequency', 'avg_days_between_orders', 'purchase_momentum', 'value_trajectory', 'unique_categories', 'category_diversity', 'category_repeat_rate'])
        
        return customer_df, orders_df

    @timer
    def processData(self, data):
        customer_df, transactions_df = data
        if customer_df.empty or customer_df.shape[0] < 50 or transactions_df.empty or transactions_df.shape[0] < 50:
            print("No data available for processing")
            return None

        # Decide on a cutoff date for training
        cutoff_date = transactions_df['date'].max() - timedelta(days=self.prediction_window_days)
        print(f"Cutoff date for training: {cutoff_date}")

        # Fit the model
        self.fit(customer_df, transactions_df, cutoff_date)

        # Predict as of the latest date in the transactions
        prediction_date = transactions_df['date'].max()
        predictions = self.predict(customer_df, transactions_df, prediction_date)

        return predictions
    
    @timer
    def create_customer_features(self, customer_df, transactions_df, cutoff_date, training=True):
        """Create features with enhanced metrics and RFM segmentation."""
        feature_start_date = cutoff_date - timedelta(days=self.feature_window_days)

        # Ensure transactions only up to cutoff_date
        transactions_df = transactions_df[transactions_df['date'] <= cutoff_date].copy()

        # Filter transactions within the feature window
        feature_period_transactions = transactions_df[
            transactions_df['date'] >= feature_start_date
        ]

        # RFM Analysis
        rfm = feature_period_transactions.groupby('user').agg({
            'date': lambda x: (cutoff_date - x.max()).days,  # Recency
            'user': 'count',                                 # Frequency
            'price': 'sum'                                  # Monetary Value
        }).rename(columns={'date': 'Recency', 'user': 'Frequency', 'price': 'Monetary'})
        rfm['Recency'] = rfm['Recency'].astype(int)

        print("Data types of RFM columns:")
        print(rfm[['Recency', 'Frequency', 'Monetary']].dtypes)

        rfm['Monetary'] = pd.to_numeric(rfm['Monetary'], errors='coerce')

        # Handle NaNs and infinite values
        rfm['Monetary'].replace([np.inf, -np.inf], np.nan, inplace=True)
        rfm['Monetary'].fillna(0, inplace=True)

        # Handle negative values in 'Monetary'
        # rfm['Monetary'] = rfm['Monetary'].clip(lower=0)

        # Verify data types of RFM columns
        print("Data types of RFM columns after cleaning:")
        print(rfm[['Recency', 'Frequency', 'Monetary']].dtypes)

        print("First few rows of RFM data:")
        print(rfm[['Recency', 'Frequency', 'Monetary']].head())

        print("Check for NaN values in RFM columns:")
        print(rfm[['Recency', 'Frequency', 'Monetary']].isnull().sum())

        # Assign RFM scores
        rfm['RecencyScore'] = pd.qcut(rfm['Recency'], 5, labels=False, duplicates='drop')
        rfm['FrequencyScore'] = pd.qcut(rfm['Frequency'], 5, labels=False, duplicates='drop')
        rfm['MonetaryScore'] = pd.qcut(rfm['Monetary'], 5, labels=False, duplicates='drop')

        # Fill NaNs with median scores for new customers
        rfm[['RecencyScore', 'FrequencyScore', 'MonetaryScore']] = \
            rfm[['RecencyScore', 'FrequencyScore', 'MonetaryScore']].fillna(2)

        # Create RFM segment
        rfm['RFMScore'] = (
            rfm['RecencyScore'].astype(int) +
            rfm['FrequencyScore'].astype(int) +
            rfm['MonetaryScore'].astype(int)
        )

        # Reset index
        rfm.reset_index(inplace=True)

        # Merge RFM features with customer data
        features_df = customer_df.merge(rfm, left_on='identityvalue', right_on='user', how='left')

        # Fill missing values for customers with no transactions
        features_df[['Recency', 'Frequency', 'Monetary', 'RecencyScore',
                     'FrequencyScore', 'MonetaryScore', 'RFMScore']] = \
            features_df[['Recency', 'Frequency', 'Monetary', 'RecencyScore',
                         'FrequencyScore', 'MonetaryScore', 'RFMScore']].fillna(0)

        # Future, lets add in the following features, once we have them in our dataset it should automatically be added to the model:
        # Handle 'gender' column
        if 'gender' in features_df.columns:
            features_df['gender'] = features_df['gender'].map({'Male': 0, 'Female': 1}).fillna(-1)
        else:
            features_df['gender'] = -1  # Placeholder if gender is not available

        # Handle 'age' column
        if 'age' in features_df.columns:
            features_df['age'] = features_df['age'].fillna(features_df['age'].median())
        else:
            features_df['age'] = -1  # Placeholder if age is not available

        # Save the 'identityvalue' for labels
        self.identityvalues = features_df['identityvalue']

        # Handle 'location' column
        if 'location' in features_df.columns:
            features_df['location'] = features_df['location'].fillna('Unknown')

            if training:
                # During training, get all categories
                self.location_categories = sorted(features_df['location'].unique())
            features_df['location'] = pd.Categorical(features_df['location'],
                                                     categories=self.location_categories)

            # One-hot encode 'location'
            location_dummies = pd.get_dummies(features_df['location'], prefix='loc', dummy_na=False)
            features_df = pd.concat([features_df, location_dummies], axis=1)
            features_df.drop(columns=['location'], inplace=True)
        else:
            features_df['loc_Unknown'] = 1  # Placeholder if location is not available

        # Handle 'loyaltysegment' column
        if 'loyaltysegment' in features_df.columns:
            features_df['loyaltysegment'] = features_df['loyaltysegment'].fillna('Unknown')

            if training:
                # During training, get all categories
                self.loyaltysegment_categories = sorted(features_df['loyaltysegment'].unique())
            features_df['loyaltysegment'] = pd.Categorical(features_df['loyaltysegment'],
                                                           categories=self.loyaltysegment_categories)

            # One-hot encode 'loyaltysegment'
            loyalty_dummies = pd.get_dummies(features_df['loyaltysegment'], prefix='loyalty', dummy_na=False)
            features_df = pd.concat([features_df, loyalty_dummies], axis=1)
            features_df.drop(columns=['loyaltysegment'], inplace=True)
        else:
            features_df['loyalty_Unknown'] = 1  # Placeholder if loyaltysegment is not available

        # Interaction terms (optional)
        features_df['Recency*Frequency'] = features_df['Recency'] * features_df['Frequency']
        features_df['Frequency*Monetary'] = features_df['Frequency'] * features_df['Monetary']

        # Drop unneeded columns
        features_df.drop(columns=['first_purchase', 'user'], errors='ignore', inplace=True)

        # Exclude date columns from features
        date_cols = features_df.select_dtypes(include=['datetime', 'datetime64']).columns.tolist()
        if date_cols:
            print(f"Dropping date columns from features_df: {date_cols}")
            features_df.drop(columns=date_cols, inplace=True)

        print("\nColumn data types before conversion:")
        print(features_df.dtypes)

        # Convert columns to numeric
        numeric_columns = [
            'trailingtwelvemonthgranttotal', 'earned', 'used', 'TotalOrders', 
            'OrdersWithDiscounts', 'TotalSpent', 'SubtotalSpent', 
            'OrdersWithFreeShipping', 'OrdersWithRefunds', 'TotalAmountRefunded',
            'DaysSinceLastPurchase', 'AvgTimeBetweenPurchases', 
            'engagement_30_days', 'total_orders', 'total_spent', 
            'avg_order_value', 'max_order_value', 'days_since_first',
            'days_since_last', 'purchase_span', 'purchase_frequency',
            'avg_days_between_orders', 'purchase_momentum', 'value_trajectory',
            'unique_categories', 'category_diversity', 'category_repeat_rate'
        ]
        
        for col in numeric_columns:
            if col in features_df.columns:
                features_df[col] = pd.to_numeric(features_df[col], errors='coerce')

        # Ensure all features are numeric
        non_numeric_cols = features_df.select_dtypes(include=['object']).columns.tolist()
        non_numeric_cols = [col for col in non_numeric_cols 
                       if col not in ['identityvalue'] + numeric_columns]
        if non_numeric_cols:
            print(f"Dropping non-numeric columns from features_df: {non_numeric_cols}")
            features_df.drop(columns=non_numeric_cols, inplace=True)
        
        print(features_df.head())
        return features_df
    
    @timer
    def create_labels(self, customer_ids, transactions_df, cutoff_date):
        """Create labels indicating whether a customer made a purchase in the prediction window."""
        prediction_end_date = cutoff_date + timedelta(days=self.prediction_window_days)

        # Ensure transactions only in the prediction window
        transactions_df = transactions_df[
            (transactions_df['date'] > cutoff_date) & (transactions_df['date'] <= prediction_end_date)
        ].copy()

        customers_with_purchase = set(transactions_df['user'].unique())

        labels = pd.Series(
            [1 if cid in customers_with_purchase else 0 for cid in customer_ids],
            index=customer_ids
        )

        return labels.values

    @timer
    def fit(self, customer_df, transactions_df, cutoff_date):
        """Train the XGBoost model with enhanced features and evaluation."""
        print("\nData Statistics:")
        print(f"Transaction date range: {transactions_df['date'].min()} to {transactions_df['date'].max()}")
        print(f"Total customers: {len(customer_df)}")
        print(f"Total transactions: {len(transactions_df)}")
        

        # Create time-based splits
        total_timespan = transactions_df['date'].max() - transactions_df['date'].min()
        split_size = total_timespan / 3

        cv_splits = [
            transactions_df['date'].min() + split_size * 2,    # First cutoff
            transactions_df['date'].min() + split_size * 2.5   # Second cutoff
        ]

        metrics = {
            'auc': [], 'precision': [], 'recall': [], 'f1': [],
            'feature_importance': [], 'optimal_thresholds': []
        }

        print("\nTraining model with temporal cross-validation...")

        for split_idx, cutoff_date in enumerate(cv_splits, 1):
            print(f"\nFold {split_idx}")
            print(f"Cutoff date: {cutoff_date}")

            # Define validation cutoff date (after the prediction window)
            valid_cutoff_date = cutoff_date + timedelta(days=self.prediction_window_days)
            print(f"Validation cutoff date: {valid_cutoff_date}")

            # Create training features and labels (up to cutoff_date)
            train_features_df = self.create_customer_features(
                customer_df.copy(), transactions_df.copy(), cutoff_date, training=True)
            train_labels = self.create_labels(
                train_features_df['identityvalue'], transactions_df.copy(), cutoff_date)

            # Save feature columns after training set is created
            if self.saved_feature_columns is None:
                # Ensure 'identityvalue' is excluded from features
                self.saved_feature_columns = train_features_df.drop(columns=['identityvalue']).columns.tolist()
                self.feature_columns = self.saved_feature_columns.copy()

            # Prepare training data
            X_train = train_features_df.drop(columns=['identityvalue']).values
            y_train = train_labels

            # Calculate positive class rate for training data
            positive_rate = np.mean(y_train)
            print(f"Training positive class rate: {positive_rate:.3f}")

            # Handle class imbalance
            negative_class = (y_train == 0).sum()
            positive_class = (y_train == 1).sum()
            scale_pos_weight = negative_class / positive_class if positive_class != 0 else 1
            print(f"Scale Pos Weight: {scale_pos_weight:.2f}")

            # Instantiate XGBClassifier with scale_pos_weight
            xgb_model = xgb.XGBClassifier(
                n_estimators=200,
                max_depth=5,
                learning_rate=0.05,
                subsample=0.8,
                colsample_bytree=0.8,
                min_child_weight=5,
                random_state=42,
                use_label_encoder=False,
                eval_metric='logloss',
                scale_pos_weight=scale_pos_weight
            )

            # Scale training features
            self.scaler.fit(X_train)
            X_train_scaled = self.scaler.transform(X_train)

            # Train the XGBClassifier
            xgb_model.fit(X_train_scaled, y_train)

            # Prepare validation data
            valid_features_df = self.create_customer_features(
                customer_df.copy(), transactions_df.copy(), valid_cutoff_date, training=False)
            valid_labels = self.create_labels(
                valid_features_df['identityvalue'], transactions_df.copy(), valid_cutoff_date)

            # Align validation feature columns with training
            for col in self.saved_feature_columns:
                if col not in valid_features_df.columns:
                    valid_features_df[col] = 0
            extra_cols = [col for col in valid_features_df.columns
                        if col not in self.saved_feature_columns + ['identityvalue']]
            if extra_cols:
                valid_features_df.drop(columns=extra_cols, inplace=True)
            valid_features_df = valid_features_df[self.saved_feature_columns + ['identityvalue']]

            X_valid = valid_features_df.drop(columns=['identityvalue']).values
            y_valid = valid_labels

            # Scale validation features using the scaler fitted on training data
            X_valid_scaled = self.scaler.transform(X_valid)

            # Calibrate the model using validation data
            calibrated_model = CalibratedClassifierCV(
                estimator=xgb_model,
                method='isotonic',  # or 'sigmoid'
                cv='prefit'  # Since the model is already fitted
            )
            calibrated_model.fit(X_valid_scaled, y_valid)

            # Use the calibrated model for predictions
            y_valid_pred_proba = calibrated_model.predict_proba(X_valid_scaled)[:, 1]

            # Optimize threshold based on validation data
            precision_vals, recall_vals, thresholds = precision_recall_curve(y_valid, y_valid_pred_proba)
            f1_scores = 2 * (precision_vals * recall_vals) / (precision_vals + recall_vals + 1e-6)
            optimal_idx = np.argmax(f1_scores)
            if len(thresholds) > 0:
                optimal_threshold = thresholds[optimal_idx]
            else:
                optimal_threshold = 0.5  # Default value if thresholds are empty
            # Adjust the optimal threshold to prevent it from being too low
            min_threshold = 0.05  # Adjust this value as needed
            optimal_threshold = max(optimal_threshold, min_threshold)
            print(f"Adjusted Optimal Threshold: {optimal_threshold}")

            # Store the optimal threshold
            metrics['optimal_thresholds'].append(optimal_threshold)

            # Use the adjusted threshold to make predictions
            y_valid_pred = (y_valid_pred_proba >= optimal_threshold).astype(int)

            # Check if both classes are present in y_valid
            classes_in_y_valid = np.unique(y_valid)
            print(f"Classes in y_valid: {classes_in_y_valid}")

            if len(classes_in_y_valid) < 2:
                print(f"Only one class present in y_valid for fold {split_idx}. ROC AUC cannot be computed.")
                metrics['auc'].append(None)
            else:
                # Store ROC AUC metric
                auc_score = roc_auc_score(y_valid, y_valid_pred_proba)
                metrics['auc'].append(auc_score)

            # Store other metrics
            metrics['precision'].append(precision_score(y_valid, y_valid_pred, zero_division=0))
            metrics['recall'].append(recall_score(y_valid, y_valid_pred, zero_division=0))
            metrics['f1'].append(f1_score(y_valid, y_valid_pred, zero_division=0))

            # Store feature importances from XGBClassifier
            feature_importances = xgb_model.feature_importances_
            metrics['feature_importance'].append(
                dict(zip(self.feature_columns, feature_importances))
            )

            # Print validation metrics
            if metrics['auc'][-1] is not None:
                print(f"AUC: {metrics['auc'][-1]:.3f}")
            else:
                print("AUC: Not computed (only one class present in validation data)")
            print(f"Precision: {metrics['precision'][-1]:.3f}")
            print(f"Recall: {metrics['recall'][-1]:.3f}")
            print(f"F1 Score: {metrics['f1'][-1]:.3f}")

        # Exclude None values when computing the average AUC
        valid_auc_scores = [score for score in metrics['auc'] if score is not None]

        # Print aggregate metrics
        print("\nAverage Metrics:")
        for metric in ['auc', 'precision', 'recall', 'f1']:
            values = metrics[metric]
            # Exclude None values for AUC
            if metric == 'auc':
                values = [v for v in values if v is not None]
            if values:
                print(f"{metric.upper()}: {np.mean(values):.3f} (+/- {np.std(values):.3f})")
            else:
                print(f"{metric.upper()}: Not computed due to single-class issue.")

        # Print top features
        print("\nTop 10 Most Important Features (averaged across folds):")
        avg_importance = {}
        for feature in self.feature_columns:
            importance_values = [fold_imp.get(feature, 0) for fold_imp in metrics['feature_importance']]
            avg_importance[feature] = np.mean(importance_values)

        for feature, importance in sorted(avg_importance.items(),
                                        key=lambda x: x[1], reverse=True)[:10]:
            print(f"{feature}: {importance:.4f}")

        # Compute average optimal threshold
        optimal_thresholds = [thresh for thresh in metrics.get('optimal_thresholds', []) if thresh is not None]
        if optimal_thresholds:
            self.optimal_threshold = np.mean(optimal_thresholds)
        else:
            self.optimal_threshold = 0.5  # Default threshold
        print(f"\nFinal optimal threshold set to: {self.optimal_threshold}")

        # Train final model on all data up to the last cutoff date
        final_cutoff = cv_splits[-1]
        final_features = self.create_customer_features(
            customer_df.copy(), transactions_df.copy(), final_cutoff, training=True)
        final_labels = self.create_labels(
            final_features['identityvalue'], transactions_df.copy(), final_cutoff)

        X_final = final_features.drop(columns=['identityvalue']).values
        y_final = final_labels

        # Adjust scale_pos_weight for final model
        negative_class = (y_final == 0).sum()
        positive_class = (y_final == 1).sum()
        scale_pos_weight = negative_class / positive_class if positive_class != 0 else 1
        print(f"Final model Scale Pos Weight: {scale_pos_weight:.2f}")

        # Instantiate XGBClassifier with scale_pos_weight
        final_xgb_model = xgb.XGBClassifier(
            n_estimators=200,
            max_depth=5,
            learning_rate=0.05,
            subsample=0.8,
            colsample_bytree=0.8,
            min_child_weight=5,
            random_state=42,
            use_label_encoder=False,
            eval_metric='logloss',
            scale_pos_weight=scale_pos_weight
        )

        # Scale features
        self.scaler.fit(X_final)
        X_final_scaled = self.scaler.transform(X_final)

        # Split the final data into training and calibration sets
        X_final_train, X_calibration, y_final_train, y_calibration = train_test_split(
            X_final_scaled, y_final, test_size=0.1, shuffle=False
        )

        # Train the XGBClassifier on training data
        final_xgb_model.fit(X_final_train, y_final_train)

        # Calibrate the final model using calibration data
        calibrated_final_model = CalibratedClassifierCV(
            estimator=final_xgb_model,
            method='isotonic',
            cv='prefit'
        )
        calibrated_final_model.fit(X_calibration, y_calibration)

        # Update self.model for predictions
        self.model = calibrated_final_model

    @timer
    def predict(self, customer_df, transactions_df, prediction_date):
        """Predict propensity scores for customers as of the prediction_date."""
        # Create features
        features_df = self.create_customer_features(
            customer_df.copy(), transactions_df.copy(), prediction_date, training=False)
        identityvalues = features_df['identityvalue']

        # Align features with training columns
        for col in self.saved_feature_columns:
            if col not in features_df.columns:
                features_df[col] = 0
        # Remove any extra columns
        extra_cols = [col for col in features_df.columns
                    if col not in self.saved_feature_columns + ['identityvalue']]
        if extra_cols:
            features_df.drop(columns=extra_cols, inplace=True)

        # Ensure the order of columns matches saved_feature_columns
        features_df = features_df[self.saved_feature_columns + ['identityvalue']]

        X = features_df.drop(columns=['identityvalue']).values

        # Scale features using the scaler fitted during training
        X_scaled = self.scaler.transform(X)

        # Predict probabilities
        y_pred_proba = self.model.predict_proba(X_scaled)[:, 1]

        # Use the stored optimal threshold
        y_pred = (y_pred_proba >= self.optimal_threshold).astype(int)

        # Get recommended products for each customer
        recommended_products = self.get_recommended_products(transactions_df, identityvalues)

        # Prepare the output DataFrame with product recommendations
        predictions = pd.DataFrame({
            'identityvalue': identityvalues,
            'propensity_score': y_pred_proba,
            'prediction': y_pred,
            'recommended_product': recommended_products
        })
        return predictions

    @timer
    def writeData(self, final_df):
        if final_df is None or final_df.empty:
            print("No data to write")
            return

        final_df['propensity_score'] = (final_df['propensity_score'] * 1000).round(0)

        self.update_postgres(final_df)

        final_df = final_df.rename(columns={'identityvalue': 'customer'})
        final_df['customer'] = final_df['customer'].astype(str)
        final_df['propensity_score'] = final_df['propensity_score'].astype('float32')  # real in Hive
        final_df['prediction'] = final_df['prediction'].astype('int32')
        
        # Write to S3
        s3 = boto3.client('s3')
        
        parquet_buffer = BytesIO()
        final_df.to_parquet(parquet_buffer, index=False, engine='pyarrow')
        
        if 'CURATED_BUCKET' not in os.environ:
            raise ValueError("CURATED_BUCKET environment variable is not set")
            
        formatted_time = pd.Timestamp.now().strftime('%Y-%m-%d_%H-%M')
        s3.put_object(
            Bucket=os.environ["CURATED_BUCKET"],
            Key=f"rebuy_propensity/organization={self.message['orgId']}/{formatted_time}.parquet",
            Body=parquet_buffer.getvalue()
        )

    def create_features(self, customer_df, orders_df):
        feature_start_date = datetime.now() - timedelta(days=180)

    @timer
    def update_postgres(self, final_df):
        database_info = raleon_helper.get_database_info()
        conn = psycopg2.connect(
            user=database_info['username'],
            password=database_info['password'],
            host=database_info['host'],
            dbname=database_info['dbname'],
            port=database_info['port']
        )
        
        try:
            cur = conn.cursor()
            
            update_sql = """
                UPDATE raleonuseridentity 
                SET rebuypropensity = data.score,
                    rebuyproduct = data.product,
                    metricsupdated = NOW()
                FROM (VALUES %s) AS data (identityvalue, score, product)
                WHERE raleonuseridentity.identityvalue = data.identityvalue 
                AND raleonuseridentity.orgid = {0};
            """.format(self.message['orgId']) 
            
            update_data = [(str(row['identityvalue']), float(row['propensity_score']), str(row['recommended_product'])) 
                          for _, row in final_df.iterrows()]
            
            if update_data:
                psycopg2.extras.execute_values(
                    cur,
                    update_sql,
                    update_data,
                    template=None,
                    page_size=1000
                )
                
                conn.commit()
                
        except Exception as e:
            print(f"An error occurred: {e}")
            conn.rollback()
        finally:
            cur.close()
            conn.close()

    def get_previous_scores(self):
        database = os.environ["ATHENA_DB"]
        query_string = f"""
        SELECT 
            customer,
            max_by(rebuy_propensity, rundate) as rebuy_propensity
        FROM "{database}"."rebuy_propensity"
        WHERE organization='{str(self.message['orgId'])}'
        GROUP BY customer;
        """
        
        return self.query_athena_and_get_df(query_string, ['customer', 'rebuy_propensity'])

    def query_athena_and_get_df(self, query_string, columns):
        if 'ATHENA_OUTPUT_BUCKET' not in os.environ:
            raise ValueError("ATHENA_OUTPUT_BUCKET environment variable is not set")
            
        response = athena_helper.run_athena_query_v2(
            query_string, 
            os.environ["ATHENA_OUTPUT_BUCKET"]
        )
        
        query_id = response['QueryExecutionId']
        
        if athena_helper.wait_for_query_execution(query_id):
            results = athena_helper.get_query_results(query_id)
            rows = [row['Data'] for row in results['ResultSet']['Rows'][1:]]
            df = pd.DataFrame(rows, columns=columns)
            
            for col in df.columns:
                if df[col].apply(isinstance, args=(dict,)).any():
                    df[col] = df[col].apply(lambda x: self.extract_value_from_dict(x, col))
            return df
        else:
            raise Exception("Query failed!")

    def extract_value_from_dict(self, row, column_name):
        if not isinstance(row, dict):
            return row
            
        if 'VarCharValue' in row:
            value = row['VarCharValue']
            if column_name == 'date':
                return pd.to_datetime(value)
            elif column_name in ['price', 'balance', 'ttm_spend', 'totalspent']:
                return float(value)
            elif column_name == 'item_ids':
                # Handle array format from Athena
                if pd.isna(value) or value == '':
                    return []
                # Remove brackets if present
                if value.startswith('[') and value.endswith(']'):
                    value = value[1:-1]
                return [str(item).strip().strip('"\'') for item in value.split(',') if item.strip()]
            return value
        
        return None

    def get_recommended_products(self, transactions_df, customer_ids):
        """Determine the most likely product to be purchased next for each customer."""
        # Convert dates to datetime if needed
        if not pd.api.types.is_datetime64_any_dtype(transactions_df['date']):
            transactions_df['date'] = pd.to_datetime(transactions_df['date'])

        # Create a list of all products from the item_ids arrays
        all_purchases = []
        for _, row in transactions_df.iterrows():
            if isinstance(row['item_ids'], list):
                for item in row['item_ids']:
                    all_purchases.append({
                        'user': row['user'],
                        'item_id': item,
                        'date': row['date'],
                        'price': row['price'] / len(row['item_ids']) if row['item_ids'] else 0
                    })
        
        # Convert to DataFrame
        purchase_df = pd.DataFrame(all_purchases)
        if purchase_df.empty:
            return pd.Series('', index=customer_ids)

        # Get the most recent purchase for each customer-product combination
        recent_purchases = (purchase_df.groupby(['user', 'item_id'])
                          .agg({'date': 'max', 'price': 'mean'})
                          .reset_index())

        # Get the most purchased product for each customer
        product_frequency = (purchase_df.groupby(['user', 'item_id'])
                           .size()
                           .reset_index(name='frequency'))

        # Combine recency and frequency
        product_scores = recent_purchases.merge(product_frequency, on=['user', 'item_id'])
        
        # Calculate scores
        max_date = product_scores['date'].max()
        product_scores['days_since_purchase'] = (max_date - product_scores['date']).dt.days
        product_scores['recency_score'] = 1 / (1 + product_scores['days_since_purchase'])
        product_scores['total_score'] = product_scores['recency_score'] * product_scores['frequency']

        # Get the highest scoring product for each customer
        recommended_products = (product_scores.sort_values('total_score', ascending=False)
                              .groupby('user')
                              .first()
                              .reindex(customer_ids)
                              ['item_id']
                              .fillna('')
                              .values)

        return recommended_products
import boto3
import time
import json

client = boto3.client('athena', region_name='us-east-1')

class AthenaWorkGroup:
    METRIC = 'metric'
    ADHOC = 'adhoc'

def run_athena_query(query_string, output_location, fail_count=0):
    print(f"runAthenaQuery: {query_string}")
    params = {
        'QueryString': query_string,
        'ResultConfiguration': {
            'OutputLocation': output_location
        }
    }

    try:
        response = client.start_query_execution(**params)
        return response
    except client.exceptions.TooManyRequestsException:
        time.sleep(fail_count + 1)
        return run_athena_query(query_string, output_location, fail_count + 1)
    except client.exceptions.ThrottlingException:
        time.sleep(fail_count + 1)
        return run_athena_query(query_string, output_location, fail_count + 1)
    except Exception as e:
        print(f"Failed to run Athena Query: {e}")
        raise

def run_athena_query_v2(query_string, output_location, fail_count=0, workgroup=AthenaWorkGroup.ADHOC, catalog='AWSDataCatalog'):
    print(f"runAthenaQuery: {query_string}")
    params = {
        'QueryExecutionContext': {
            'Catalog': catalog
        },
        'QueryString': query_string,
        'ResultConfiguration': {
            'OutputLocation': output_location
        },
        'WorkGroup': workgroup
    }
    print(f"params: {json.dumps(params)}")

    try:
        response = client.start_query_execution(**params)
        print(f"Got Some data from runAthenaQuery: {json.dumps(response)}")
        return response
    except client.exceptions.TooManyRequestsException:
        time.sleep(fail_count + 1)
        return run_athena_query_v2(query_string, output_location, fail_count + 1)
    except Exception as e:
        print(f"Failed to run Athena Query: {e}")
        raise

def wait_for_query_execution(query_id, wait_ms=3000, retry_attempt=0):
    """Wait for query execution with one retry on internal error"""
    MAX_RETRIES = 1  # Only retry once
    
    while True:
        response = client.get_query_execution(QueryExecutionId=query_id)
        state = response['QueryExecution']['Status']['State']
        
        if state == 'SUCCEEDED':
            return True
        elif state in ['FAILED', 'CANCELLED']:
            error_info = response['QueryExecution']['Status'].get('StateChangeReason', 'No error message provided')
            error_details = response['QueryExecution']['Status'].get('AthenaError', {})
            
            # Check if this is an internal error (ErrorCategory 1) and we haven't retried yet
            if (retry_attempt < MAX_RETRIES and 
                error_details.get('ErrorCategory') == 1 and 
                'internal error' in error_info.lower()):
                
                print(f"Athena internal error detected, retrying query. Error: {error_info}")
                
                # Retry the query
                new_response = run_athena_query_v2(
                    response['QueryExecution']['Query'],
                    response['QueryExecution']['ResultConfiguration']['OutputLocation'],
                    0,
                    response['QueryExecution']['WorkGroup'],
                    response['QueryExecution']['QueryExecutionContext']['Catalog']
                )
                
                # Wait for the retried query
                return wait_for_query_execution(
                    new_response['QueryExecutionId'],
                    wait_ms,
                    retry_attempt + 1
                )
            
            print(f"Query execution {state}. Reason: {error_info}")
            print(f"Full response: {json.dumps(response, default=str)}")
            return False
        else:
            print(f"Query state: {state}")
            time.sleep(wait_ms / 1000)

def get_query_results(query_id, max_pages=None):
    params = {
        'QueryExecutionId': query_id
    }

    all_rows = []
    page_count = 0

    try:
        while True:
            response = client.get_query_results(**params)
            rows = response.get('ResultSet', {}).get('Rows', [])
            all_rows.extend(rows)
            next_token = response.get('NextToken')

            if next_token:
                params['NextToken'] = next_token
                page_count += 1

                # Break if the maximum number of pages is reached
                if max_pages is not None and page_count >= max_pages:
                    break
            else:
                break

        # Mimicking the original structure
        return {'ResultSet': {'Rows': all_rows}}
    except Exception as e:
        print(f"Failed to retrieve Athena Query Results: {e}")
        raise


def update_athena_partitions(table_name, org_id, metric_name, curated_bucket, athena_output_bucket, catalog='AWSDataCatalog'):
    athena_query = f"ALTER TABLE {table_name} ADD IF NOT EXISTS PARTITION (organization='{org_id}') LOCATION 's3://{curated_bucket}/{metric_name}/organization={org_id}/';"
    print(f"athenaQuery 👉 {athena_query}")

    if not athena_output_bucket:
        raise ValueError("ATHENA_OUTPUT_BUCKET environment variable is not set")

    athena_result = run_athena_query_v2(athena_query, athena_output_bucket, 0, AthenaWorkGroup.METRIC, catalog)
    print(f"athenaResult 👉 {athena_result}")

    check_table_id = athena_result['QueryExecutionId']
    check_table_query_result = wait_for_query_execution(check_table_id)

    if not check_table_query_result:
        raise ValueError("Athena query failed to execute")

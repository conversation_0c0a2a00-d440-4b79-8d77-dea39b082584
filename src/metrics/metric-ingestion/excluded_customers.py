
import aiohttp
import asyncio
import boto3
import json
from typing import List, Dict, Any
import os
from datetime import datetime
import pandas as pd
import athena_helper
from shopify_helper import bulk_update_metafields, get_shop_info_by_org_id, get_api_access_token
from raleon_helper import query_database

class AthenaWorkGroup:
    METRIC = 'metric'
    ADHOC = 'adhoc'

class ExcludedCustomers:
    def __init__(self):
        self.s3_client = boto3.client('s3')

    def getData(self, message):
        self.message = message
        shop_info = get_shop_info_by_org_id(self.message['orgId'])
        if not shop_info:
            return
        sessionToken = get_api_access_token(shop_info['shopDomain'])
        if not sessionToken:
            return
        return asyncio.run(self.load_excluded_customers(self.message['orgId'], shop_info['shopDomain'], sessionToken))
        
    def processData(self, data):
        return data
    
    def writeData(self, data):
        if not data:
            print("No data to write.")
            return
        self.write_metrics_data_to_s3(data, self.message)
    
    def write_metrics_data_to_s3(self, metrics_data, message):
        rundate = datetime.now()
        metrics_data = [{'customerId': cid, 'runDate': rundate} for cid in metrics_data]
        df = pd.DataFrame(metrics_data)
        df['runDate'] = (df['runDate'].astype(int) / 1e6).astype('int64')
        
        # Path where the files will be stored in S3
        s3_path = f"{message['name']}/organization={message['orgId']}/"
        bucket_name = os.environ["CURATED_BUCKET"]

        # Delete existing files in the specified partition
        self.delete_existing_files(bucket_name, s3_path)

        # Save new file and upload
        file_path = f"/tmp/{message['orgId']}_{message['name']}.parquet"
        df.to_parquet(file_path)
        timestamp = self.generate_timestamp()
        s3_key = f"{s3_path}{message['name']}_{timestamp}.parquet"
        self.s3_client.upload_file(file_path, bucket_name, s3_key)
        print(f"Uploaded new file to {bucket_name}/{s3_key}")

    def delete_existing_files(self, bucket, path):
        # List all objects in the specified S3 path
        response = self.s3_client.list_objects_v2(Bucket=bucket, Prefix=path)
        if 'Contents' in response:
            for obj in response['Contents']:
                print(f"Deleting {obj['Key']} from {bucket}")
                self.s3_client.delete_object(Bucket=bucket, Key=obj['Key'])


    async def load_excluded_customers(self, org_id, shop_domain, access_token):
        excluded_tags = set()

        try:
            customer_query = "SELECT exclusiontags FROM loyaltyprogram WHERE orgid = %s"
            rows = query_database(customer_query, (org_id,))
            for row in rows:
                # Assuming exclusiontags is the first column in the result
                exclusion_tags_string = row[0]
                if exclusion_tags_string:  # Ensure there's something to split
                    excluded_tags.update(exclusion_tags_string.split(','))
        except Exception as error:
            print('Error querying exclusion tags:', error)
            raise

        if not excluded_tags:
            return set()
        tag_query = ""

        for tag in excluded_tags:
            print(f'Excluded tag: {tag}')
            escaped_tag = tag.replace('"', '\\"')
            tag_query += f'tag:"{escaped_tag}" OR '

        if tag_query.endswith(" OR "):
            tag_query = tag_query[:-3]
        customer_ids = set()
        has_next_page = True
        cursor = None

        async with aiohttp.ClientSession() as session:
            while has_next_page:
                # Query includes tags field for post-filtering
                query = f"""
                query getCustomers($tagQuery: String!, $cursor: String) {{
                    customers(first: 250, query: $tagQuery, after: $cursor) {{
                        edges {{
                            node {{
                                id
                                tags
                            }}
                            cursor
                        }}
                        pageInfo {{
                            hasNextPage
                        }}
                    }}
                }}
                """
                variables = {'tagQuery': tag_query, 'cursor': cursor}
                headers = {
                    "X-Shopify-Access-Token": access_token,
                    "Content-Type": "application/json",
                }
                json_data = {'query': query, 'variables': variables}
                print(f'json_data 👉 {json.dumps(json_data)}')
                try:
                    response = await session.post(f"https://{shop_domain}/admin/api/2025-01/graphql.json", headers=headers, json=json_data)
                    json_response = await response.json()
                    print(f'jsonData 👉 {json.dumps(json_response)}')
                    if 'errors' in json_response:
                        print(f"Error fetching customers from Shopify: {json_response['errors']}")
                        return set()
                    edges = json_response['data']['customers']['edges']
                    for edge in edges:
                        customer_id = edge['node']['id']
                        customer_tags = set(edge['node']['tags'])
                        # Double-check tags match our criteria
                        if customer_tags & excluded_tags:
                            print(f'Excluded customer found - ID: {customer_id}, Tags: {customer_tags & excluded_tags}')
                            customer_ids.add(customer_id.split('/')[-1])
                    last_edge = edges[-1] if edges else None
                    cursor = last_edge['cursor'] if last_edge else None
                    has_next_page = json_response['data']['customers']['pageInfo']['hasNextPage']
                except Exception as error:
                    print('Error fetching customers from Shopify:', error)
                    raise

        return customer_ids
    
    def generate_timestamp(self):
        date = datetime.now()
        return date.strftime("%Y%m%d_%H%M%S")

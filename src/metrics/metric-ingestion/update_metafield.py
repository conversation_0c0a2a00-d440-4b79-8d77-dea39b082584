import asyncio
import aiohttp
import base64
import boto3
import time
import json
import certifi
import ssl
from typing import List, Dict, Any
import os
from datetime import datetime, timedelta
import pandas as pd
import requests
import athena_helper
from shopify_helper import bulk_update_metafields, get_shop_info_by_org_id, get_api_access_token, get_shopify_customers
from raleon_helper import query_database, decrypt, get_klaviyo_integration_by_org_id

# Configure requests to use system certificates
requests.packages.urllib3.util.ssl_.DEFAULT_CERTS = certifi.where()

class AthenaWorkGroup:
    METRIC = 'metric'
    ADHOC = 'adhoc'

class UpdateMetaField:
    def __init__(self):
        self.s3_client = boto3.client('s3')

    def getData(self, message):
        self.message = message
        if message["query"]:
            return self.get_metric_data(self.message)
        else:
            raise ValueError("Query is not set")
        
    def processData(self, data):
        return data
    
    def writeData(self, data):
        if not data:
            print("No data to write")
            return
        if self.message["dataStructure"]:
            df = pd.DataFrame(data)
            self.update_shopify(df)
            asyncio.run(self.update_klaviyo(df))

    def update_shopify(self, final_rfm):
        print("Updating Shopify meta fields...")
        self.shop_info = get_shop_info_by_org_id(self.message['orgId'])
        if not self.shop_info:
            print("Shop Info not found")
            return
        self.sessionToken = get_api_access_token(self.shop_info['shopDomain'])
        if not self.sessionToken:
            print("Session Token not found")
            return
        jsonl_content = self.generate_jsonl_content(final_rfm[['customer', 'balance']].values.tolist())
        bulk_id = asyncio.run(bulk_update_metafields(jsonl_content, self.shop_info['shopDomain'], self.sessionToken))
        print(f"Bulk ID: {bulk_id}")

    async def update_klaviyo(self, final_rfm):
        print("Updating Klaviyo Raleon Loyalty Points")
        klaviyo_info = get_klaviyo_integration_by_org_id(self.message['orgId'])
        if klaviyo_info is None:
            print("Klaviyo Integration not found")
            return
        klaviyo_api_key = decrypt(klaviyo_info['value'])
        customer_emails_with_balance = self.get_customer_emails(final_rfm[['customer', 'balance']].values.tolist())

        klaviyo_profile_data = []
        for email, balance in customer_emails_with_balance.items():
            balance_as_decimal = float(balance)
            balance_as_number = round(balance_as_decimal)
            klaviyo_profile_data.append({
                "type": "profile",
                "attributes": {
                    "email": email,
                    "properties": { "Raleon Loyalty Points": balance_as_number }
                }
            })

        url = 'https://a.klaviyo.com/api/profile-bulk-import-jobs/'
        chunked_klaviyo_profile_data = [klaviyo_profile_data[i:i + 10000] for i in range(0, len(klaviyo_profile_data), 10000)]
        ssl_context = ssl.create_default_context(cafile=certifi.where())
        conn = aiohttp.TCPConnector(ssl=ssl_context)
        async with aiohttp.ClientSession(connector=conn) as session:
            for chunk in chunked_klaviyo_profile_data:
                body = {
                    "data": {
                        "type": "profile-bulk-import-job",
                        "attributes": {
                            "profiles": {
                                "data": chunk
                            }
                        }
                    }
                }
                headers={
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Authorization': f"Klaviyo-API-Key {klaviyo_api_key.strip()}",
                    'revision': '2024-02-15'
                }
                try:
                    async with session.post(url, headers=headers, json=body) as response:
                        if response.status == 200 or response.status == 201:
                            try:
                                response_data = await response.json()
                                print(f"Klaviyo Response: {response_data}")
                            except aiohttp.ContentTypeError as e:
                                print(f"Warning: Content type error when parsing response: {e}")
                                response_text = await response.text()
                                print(f"Response text: {response_text}")
                        else:
                            print(f"Error: Klaviyo API returned status {response.status}")
                            response_text = await response.text()
                            print(f"Error response: {response_text}")
                except Exception as e:
                    print(f"Error making request to Klaviyo API: {str(e)}")
                await asyncio.sleep(1)  # Rate limiting
                    

    def get_customer_emails(self, customers_scores):
        customer_balance_map = {str(customer_value).strip(): balance for customer_value, balance in customers_scores}
        
        customer_records = get_shopify_customers(self.shop_info['shopDomain'], self.sessionToken, list(customer_balance_map.keys()))
        customers_emails = {}

        for customer in customer_records:
            customer_id = str(customer.get('id')).strip()
            if customer_id in customer_balance_map:
                customers_emails[customer['email']] = customer_balance_map[customer_id]
        return customers_emails

    def generate_jsonl_content(self, customers_scores):
        jsonl_lines = []
        for customer_value, balance in customers_scores:
            metafield_data = {
                "input" : {
                    "key": "loyalty_points",
                    "namespace": "raleonInfo",
                    "ownerId": f"gid://shopify/Customer/{customer_value}",
                    "type": "number_decimal",
                    "value": balance
                }
            }
            jsonl_lines.append(json.dumps(metafield_data))

        return '\n'.join(jsonl_lines)
        
    def get_metric_data(self, message: Dict[str, Any]) -> List[Dict[str, Any]]:
        query = message['query']
        if '{orgId}' not in query:
            raise ValueError("{orgId} placeholder is not in the query. Metric data must be filtered by organization.")
        else:
            query = query.replace("{orgId}", str(message['orgId']))
        if(message['lastRunDate'] != "" and message['lastRunDate'] != None):
            utc_time = datetime.fromisoformat(message['lastRunDate'].replace('Z', '+00:00'))
        else:
            utc_time = datetime.utcnow() - timedelta(days=1)
        formatted_date = utc_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        query = query.replace("{lastRunDate}", formatted_date)
        query = query.replace("{database}", os.environ["ATHENA_DB"])
        for key, value in message['variables'].items():
            placeholder = f"{{{key}}}"
            query = query.replace(placeholder, str(value))
        
        print(f"athenaQuery 👉 {query}")
        column_names = list(message['dataStructure'].keys())
        
        if 'ATHENA_OUTPUT_BUCKET' not in os.environ:
            raise ValueError("ATHENA_OUTPUT_BUCKET environment variable is not set")
        
        athena_result = athena_helper.run_athena_query_v2(
            query,
            os.environ['ATHENA_OUTPUT_BUCKET'],
            0,
            AthenaWorkGroup.METRIC,
            message['catalog']
        )
        print(f"athenaResult 👉 {athena_result}")
        
        check_table_id = athena_result['QueryExecutionId']
        check_table_query_result = athena_helper.wait_for_query_execution(check_table_id)
        
        if not check_table_query_result:
            raise ValueError("Athena query failed to execute")
        else:
            query_execution_id = check_table_id
            query_result_data = athena_helper.get_query_results(query_execution_id)
            query_data = []
            
            if query_result_data['ResultSet'] and query_result_data['ResultSet']['Rows']:
                print(f"Query Results: {json.dumps(query_result_data['ResultSet']['Rows'])}")
                data = query_result_data['ResultSet']['Rows']
                
                for i in range(1, len(data)):
                    row_data = data[i]['Data']
                    if not row_data:
                        continue
                    
                    row = {}
                    for index, column in enumerate(row_data):
                        column_name = column_names[index]
                        row[column_name] = 0

                        if 'VarCharValue' not in column:
                            continue

                        row[column_name] = column['VarCharValue']

                    query_data.append(row)

            return query_data

import base64
import boto3
import time
import json
from typing import List, Dict, Any
import os
from datetime import datetime, timedelta
import pandas as pd
import requests
import athena_helper
from raleon_helper import query_database

class AthenaWorkGroup:
    METRIC = 'metric'
    ADHOC = 'adhoc'

class CustomerIo:
    def __init__(self):
        self.s3_client = boto3.client('s3')

    def getData(self, message):
        self.message = message
        if message["query"]:
            return self.get_metric_data(self.message)
        else:
            raise ValueError("Query is not set")
        
    def processData(self, data):
        return data
    
    def writeData(self, data):
        if not data:
            print("No data to write")
            return
        if self.message["dataStructure"]:
            metrics_data_url = self.write_metrics_to_customerio(data, self.message)
            print(f"metricsDataUrl 👉 {metrics_data_url}")

    def write_metrics_to_customerio(self, metrics_data, message):
        email = self.get_email_from_org(message['orgId'])
        if email is None:
            print(f"No email found for org: {message['orgId']}")
            return
        currencyPrefix = self.get_currency_prefix(message['orgId'])
        if currencyPrefix is None:
            currencyPrefix = '$'
        df = pd.DataFrame(metrics_data)
        for column, details in message['dataStructure'].items():
            print(f"column: {column}, details: {details}")
            prefix = details.get('prefix', '')
            
            if details['type'] == 'float64':
                df[column] = df[column].astype('float64')
            elif details['type'] == 'DOUBLE':
                df[column] = df[column].astype('double')
            elif details['type'] == 'INT64':
                df[column] = df[column].astype('int64')
            elif details['type'] == 'datetime':
                df[column] = pd.to_datetime(df[column])
            
            if prefix == '$':
                df[column] = currencyPrefix + df[column].astype(str)
            else:
                df[column] = df[column].astype(str)

        metrics_dict = df.to_dict(orient='records')[0]
        self.send_to_customerio(email, metrics_dict)


    def send_to_customerio(self, email, attributes):
        print(f"Sending to Customer.io: {attributes}")
        print(f"Email: {email}")
        encoded_email = base64.urlsafe_b64encode(email.encode()).decode()
        session = boto3.session.Session()
        client = session.client(
            service_name='secretsmanager',
            region_name='us-east-1',
            endpoint_url='https://vpce-060670840daa313c8-mcqyvy4b.secretsmanager.us-east-1.vpce.amazonaws.com'
        )
        response = client.get_secret_value(SecretId=os.environ['CUSTOMERIO_SECRET_ARN'])
        secret = json.loads(response['SecretString'])

        url = f"https://track.customer.io/api/v1/customers/{email}"
        
        site_id = secret['siteid']
        api_key = secret['apikey']
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Basic {base64.b64encode(f'{site_id}:{api_key}'.encode()).decode()}"
        }
        attributes['email'] = email
        response = requests.put(url, headers=headers, json=attributes)
        if response.status_code == 200:
            print(f"Successfully updated customer attributes in Customer.io: {response}")
            eventurl = f"https://track.customer.io/api/v1/customers/{email}/events"
            event_data = {
                "name": "metrics_data_updated",
                "data": attributes
            }
            response = requests.post(eventurl, headers=headers, json=event_data)
            if response.status_code == 200:
                print(f"Successfully sent event to Customer.io for {email}")
            else:
                print(f"Failed to send event to Customer.io for {email}: {response.text}")
        else:
            print(f"Failed to update customer attributes in Customer.io: {response.text}")

    def get_email_from_org(self, orgId):
        rows = query_database("SELECT email FROM public.user WHERE organizationid = %s", (orgId,))
        if rows:
            return rows[0][0]
        else:
            print(f'No information found for org: {orgId}')
            return None
        
    def get_currency_prefix(self, orgId):
        rows = query_database("select prefix from supportedcurrencies s join currency c on s.id = c.supportedcurrenciesid where c.organizationid = %s", (orgId,))
        if rows:
            return rows[0][0]
        else:
            print(f'No currency information found for org: {orgId}')
            return None
        
    def get_metric_data(self, message: Dict[str, Any]) -> List[Dict[str, Any]]:
        query = message['query']
        if '{orgId}' not in query:
            raise ValueError("{orgId} placeholder is not in the query. Metric data must be filtered by organization.")
        else:
            query = query.replace("{orgId}", str(message['orgId']))
        if(message['lastRunDate'] != "" and message['lastRunDate'] != None):
            utc_time = datetime.fromisoformat(message['lastRunDate'].replace('Z', '+00:00'))
        else:
            utc_time = datetime.utcnow() - timedelta(days=1)
        formatted_date = utc_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        query = query.replace("{lastRunDate}", formatted_date)
        query = query.replace("{database}", os.environ["ATHENA_DB"])
        for key, value in message['variables'].items():
            placeholder = f"{{{key}}}"
            query = query.replace(placeholder, str(value))
        
        print(f"athenaQuery 👉 {query}")
        column_names = list(message['dataStructure'].keys())
        
        if 'ATHENA_OUTPUT_BUCKET' not in os.environ:
            raise ValueError("ATHENA_OUTPUT_BUCKET environment variable is not set")
        
        athena_result = athena_helper.run_athena_query_v2(
            query,
            os.environ['ATHENA_OUTPUT_BUCKET'],
            0,
            AthenaWorkGroup.METRIC,
            message['catalog']
        )
        print(f"athenaResult 👉 {athena_result}")
        
        check_table_id = athena_result['QueryExecutionId']
        check_table_query_result = athena_helper.wait_for_query_execution(check_table_id)
        
        if not check_table_query_result:
            raise ValueError("Athena query failed to execute")
        else:
            query_execution_id = check_table_id
            query_result_data = athena_helper.get_query_results(query_execution_id)
            query_data = []
            batch_timestamp = self.generate_timestamp()
            
            if query_result_data['ResultSet'] and query_result_data['ResultSet']['Rows']:
                print(f"Query Results: {json.dumps(query_result_data['ResultSet']['Rows'])}")
                data = query_result_data['ResultSet']['Rows']
                
                for i in range(1, len(data)):
                    row_data = data[i]['Data']
                    if not row_data:
                        continue
                    
                    row = {}
                    for index, column in enumerate(row_data):
                        column_name = column_names[index]
                        row[column_name] = 0

                        if 'VarCharValue' not in column:
                            continue

                        value = column['VarCharValue']

                        try:
                            date_value = int(time.mktime(time.strptime(value, "%Y-%m-%d %H:%M:%S")))
                            row[column_name] = date_value
                            continue
                        except ValueError:
                            pass 
                        
                        try:
                            float_value = float(value)
                            row[column_name] = float_value
                        except ValueError:
                            row[column_name] = value

                    query_data.append(row)

            return query_data

    def generate_timestamp(self):
        date = datetime.now()
        return date.strftime("%Y%m%d_%H%M%S")

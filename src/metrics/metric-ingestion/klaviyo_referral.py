import asyncio
import aiohttp
import base64
import boto3
import time
import json
from typing import List, Dict, Any
import os
from datetime import datetime, timedelta
import pandas as pd
import requests
import athena_helper
import re
from shopify_helper import get_api_access_token, get_shop_info_by_org_id, get_shopify_customers_by_ids
from raleon_helper import decrypt, get_klaviyo_integration_by_org_id, update_klaviyo_error, remove_klaviyo_error

class AthenaWorkGroup:
    METRIC = 'metric'
    ADHOC = 'adhoc'

class KlaviyoReferral:
    def __init__(self):
        self.s3_client = boto3.client('s3')

    def getData(self, message):
        self.message = message
        if message["query"]:
            return self.get_metric_data(self.message)
        else:
            raise ValueError("Query is not set")
        
    def processData(self, data):
        if not data:  # Check if data is empty
            print("No data to write to Klaviyo")
            return
        processed_data = []
        integration = get_klaviyo_integration_by_org_id(self.message['orgId'])
        if integration is None:
            raise ValueError("Klaviyo integration not found for organization ID: {}".format(self.message['orgId']))
        print(f"Integration: {integration}")
        field_mappings = json.loads(integration['fieldmappings'])
        for row in data:
            processed_row = {}
            for db_field, integration_field in field_mappings.items():
                if db_field in row:
                    processed_row[integration_field] = row[db_field]
            processed_data.append(processed_row)
        return processed_data
    
    def writeData(self, data):
        if not data:  # Check if data is empty
            print("No data to write to Klaviyo")
            return
        print("Writing data to Klaviyo")
        if self.message["dataStructure"]:
            df = pd.DataFrame(data)
            self.shop_info = get_shop_info_by_org_id(self.message['orgId'])
            self.sessionToken = get_api_access_token(self.shop_info['shopDomain'])
            asyncio.run(self.update_klaviyo(df))

    async def update_klaviyo(self, final_rfm):
        print("Updating Klaviyo Referral Code")
        klaviyo_info = get_klaviyo_integration_by_org_id(self.message['orgId'])
        klaviyo_api_key = decrypt(klaviyo_info['value'])
        customer_emails_with_properties = await self.get_customer_emails(final_rfm)

        klaviyo_profile_data = []
        for email, properties in customer_emails_with_properties.items():
            klaviyo_profile_data.append({
                "type": "profile",
                "attributes": {
                    "email": email,
                    "properties": properties
                }
            })

        chunk_size = 2000
        url = 'https://a.klaviyo.com/api/profile-bulk-import-jobs/'
        chunked_klaviyo_profile_data = [
            klaviyo_profile_data[i:i + chunk_size] 
            for i in range(0, len(klaviyo_profile_data), chunk_size)
        ]

        headers = {
            'Content-Type': 'application/json',
            'Authorization': f"Klaviyo-API-Key {klaviyo_api_key.strip()}",
            'revision': '2024-02-15'
        }

        async with aiohttp.ClientSession() as session:
            for i, chunk in enumerate(chunked_klaviyo_profile_data):
                print(f"Processing chunk {i+1}/{len(chunked_klaviyo_profile_data)} ({len(chunk)} profiles)")
                
                body = {
                    "data": {
                        "type": "profile-bulk-import-job",
                        "attributes": {
                            "profiles": {
                                "data": chunk
                            }
                        }
                    }
                }

                # Add retry logic
                max_retries = 3
                retry_delay = 5  # seconds
                
                for attempt in range(max_retries):
                    try:
                        response = await session.request('POST', url, headers=headers, json=body)
                        
                        if response.status in (401, 403):  # Auth errors
                            error_text = await response.text()
                            print(f"Klaviyo Authentication Error (Status {response.status}):")
                            print(f"Response: {error_text}")
                            
                            # Log auth error to database and continue
                            error_message = f"Klaviyo Authentication Error (Status {response.status}): {error_text}"
                            update_klaviyo_error(int(self.message['orgId']), error_message)
                            return  # Exit without throwing exception
                            
                        elif response.status == 413:  # Too large
                            error_text = await response.text()
                            print(f"Klaviyo API Error (Status {response.status}):")
                            print(f"Response: {error_text}")
                            
                            if response.status == 413:
                                print(f"Chunk {i+1} too large, reducing size and retrying")
                                mid = len(chunk) // 2
                                await self.process_klaviyo_chunk(session, chunk[:mid], url, headers)
                                await self.process_klaviyo_chunk(session, chunk[mid:], url, headers)
                                break

                        response.raise_for_status()
                        response_data = await response.json()
                        print(f"Successfully processed chunk {i+1}")
                        
                        # Remove error record on success
                        remove_klaviyo_error(int(self.message['orgId']))
                        await asyncio.sleep(2)
                        break
                    except Exception as e:
                        print(f"Error processing chunk {i+1}: {str(e)}")
                        if attempt == max_retries - 1:
                            print(f"Failed to process chunk after {max_retries} attempts")
                            #print(f"Chunk data: {chunk}")
                            raise RuntimeError("Failed to process chunk after {max_retries} attempts") from e
                        else:
                            await asyncio.sleep(retry_delay * (2 ** attempt))

    async def process_klaviyo_chunk(self, session, chunk, url, headers):
        """Process a single chunk of Klaviyo profiles"""
        body = {
            "data": {
                "type": "profile-bulk-import-job",
                "attributes": {
                    "profiles": {
                        "data": chunk
                    }
                }
            }
        }
        
        response = await session.request('POST', url, headers=headers, json=body)
        response_data = await response.json()
        print(f"Chunk processed, response: {response_data}")

    async def get_customer_emails(self, df):
        customer_ids = df['customer'].astype(str).str.strip().tolist()
        customer_records = await get_shopify_customers_by_ids(self.shop_info['shopDomain'], self.sessionToken, customer_ids)
        customer_id_to_email = {str(customer.get('id')).strip(): customer['email'] for customer in customer_records if 'email' in customer}

        customers_emails = {}
        invalid_emails = 0
        for index, row in df.iterrows():
            customer_id = str(row['customer']).strip()
            if customer_id in customer_id_to_email:
                email = customer_id_to_email[customer_id]
                if self.is_valid_email(email): 
                    properties = row.drop('customer').to_dict()
                    customers_emails[email] = properties
                else:
                    invalid_emails += 1
        print(f"Invalid emails: {invalid_emails}")
        return customers_emails
        
    def get_metric_data(self, message: Dict[str, Any]) -> List[Dict[str, Any]]:
        query = message['query']
        if '{orgId}' not in query:
            raise ValueError("{orgId} placeholder is not in the query. Metric data must be filtered by organization.")
        else:
            query = query.replace("{orgId}", str(message['orgId']))
        if(message['lastRunDate'] != "" and message['lastRunDate'] != None):
            utc_time = datetime.fromisoformat(message['lastRunDate'].replace('Z', '+00:00'))
        else:
            utc_time = datetime.utcnow() - timedelta(days=365)
        formatted_date = utc_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        query = query.replace("{lastRunDate}", formatted_date)
        query = query.replace("{database}", os.environ["ATHENA_DB"])
        for key, value in message['variables'].items():
            placeholder = f"{{{key}}}"
            query = query.replace(placeholder, str(value))
        
        print(f"athenaQuery = {query}")
        column_names = list(message['dataStructure'].keys())
        
        if 'ATHENA_OUTPUT_BUCKET' not in os.environ:
            raise ValueError("ATHENA_OUTPUT_BUCKET environment variable is not set")
        
        athena_result = athena_helper.run_athena_query_v2(
            query,
            os.environ['ATHENA_OUTPUT_BUCKET'],
            0,
            AthenaWorkGroup.METRIC,
            message['catalog']
        ) 
        
        check_table_id = athena_result['QueryExecutionId']
        check_table_query_result = athena_helper.wait_for_query_execution(check_table_id)
        
        if not check_table_query_result:
            raise ValueError("Athena query failed to execute")
        else:
            query_execution_id = check_table_id
            query_result_data = athena_helper.get_query_results(query_execution_id)
            query_data = []
            
            if query_result_data['ResultSet'] and query_result_data['ResultSet']['Rows']:
                data = query_result_data['ResultSet']['Rows']
                
                for i in range(1, len(data)):
                    row_data = data[i]['Data']
                    if not row_data:
                        continue
                    
                    row = {}
                    for index, column in enumerate(row_data):
                        column_name = column_names[index]
                        row[column_name] = ""

                        if 'VarCharValue' in column:
                            row[column_name] = column['VarCharValue']

                    query_data.append(row)

            return query_data
        
    def is_valid_email(self, email):
        # Check if email is a string and not empty
        if not isinstance(email, str) or not email.strip():
            print(f"Rejected email (empty or not string): {email}")
            return False
            
        # Basic string cleaning
        email = email.strip().lower()
        
        # Check length constraints
        if len(email) > 254:  # RFC 5321
            print(f"Rejected email (too long): {email}")
            return False
            
        # Split the local part and domain part
        local_part, domain_part = email.split('@')
        
        # Add a length check for the local part (should not exceed 64 characters per RFC 5321)
        if len(local_part) > 64:
            print(f"Rejected email (local part too long): {email}")
            return False
            
        # Comprehensive regex for email validation
        email_regex = r'^[a-zA-Z0-9](?:[a-zA-Z0-9._%+-]*[a-zA-Z0-9])?@(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}$'
        
        # Additional checks
        if (
            '..' in email or                    # No consecutive dots
            email.startswith('.') or            # Can't start with dot
            email.endswith('.') or              # Can't end with dot
            '@.' in email or                    # @ can't be followed by dot
            '.@' in email or                    # Dot can't be before @
            email.count('@') != 1 or            # Exactly one @
            ' ' in email                        # No spaces allowed
        ):
            print(f"Rejected email (failed additional checks): {email}")
            return False
            
        is_valid = bool(re.match(email_regex, email))
        if not is_valid:
            print(f"Rejected email (failed regex): {email}")
        return is_valid

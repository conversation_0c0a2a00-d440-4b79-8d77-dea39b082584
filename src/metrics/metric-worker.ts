import {
	APIGatewayProxyEvent,
	APIGatewayProxyResult,
	ScheduledEvent,
} from "aws-lambda";
import {
	runAthenaQueryV2,
	waitForQueryExecution,
	getQueryResults,
} from "../utils/athena-helpers";
import { queryDatabase } from "../utils/raleon-helper";
import * as AWS from 'aws-sdk';

// AWS SDK v2 client
const batch = new AWS.Batch();

// Environment
const BATCH_JOB_QUEUE = process.env.BATCH_JOB_QUEUE!;           // e.g. metric-ingestion-job-queue
const BATCH_JOB_DEFINITION = process.env.BATCH_JOB_DEFINITION!; // e.g. metric-ingestion-jobdef
const PRIORITY_BATCH_JOB_QUEUE = process.env.PRIORITY_BATCH_JOB_QUEUE!; // e.g. metric-ingestion-priority-job-queue

export const handler = async (
	event: APIGatewayProxyEvent | ScheduledEvent
  ): Promise<APIGatewayProxyResult | void> => {
	try {
	  if ((event as any).httpMethod) {
		return handleApiEvent(event as APIGatewayProxyEvent);
	  } else if ((event as any).source === "aws.events") {
		return handleScheduledEvent(event as ScheduledEvent);
	  }
	} catch (error) {
	  return {
		statusCode: 400,
		body: JSON.stringify({ 
		  error: error instanceof Error ? error.message : 'Unknown error occurred'
		}),
		headers: {
		  "Content-Type": "application/json",
		},
	  };
	}
  };

const handleApiEvent = async (
	event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
	console.log("Received API Gateway event:", event);
	const orgId = event.queryStringParameters?.orgId || "";
	const campaignId = event.queryStringParameters?.campaignId || "";
	const name = event.queryStringParameters?.name || "";
	const calculation = event.queryStringParameters?.["calculation"]!;
	const startDate = event.queryStringParameters?.["start-date"];
	const endDate = event.queryStringParameters?.["end-date"];
	const groupBy = event.queryStringParameters?.["group-by"] || "week";
	let groupByField = "rundate";
	let aggColumns = "";

	const metricInfo = await getMetricInformation(name, orgId);
	if (!metricInfo.lastrundate) {
		throw new Error("Metric has not ran");
	}
	if (metricInfo.variables && metricInfo.variables.groupByField) {
		groupByField = metricInfo.variables.groupByField;
	}
	if (calculation === "trend" || calculation === "sum") {
		aggColumns = createAggColumnsFromDataStructure(
			metricInfo.datastructure,
			`${name}metric`,
			calculation,
			groupByField
		);
	} else {
		aggColumns = createColumnsFromDataStructure(
			metricInfo.datastructure,
			`${name}metric`,
			calculation,
			groupByField
		);
	}

	let whereClause = `WHERE organization = '${orgId}'`;
	let groupByClause = "";

	if (startDate === "latest") {
		if (metricInfo.lastrundate && metricInfo.lastrundate instanceof Date) {
			const latestDate = metricInfo.lastrundate.toISOString().split('T')[0];
			whereClause += ` AND DATE(rundate) = date '${latestDate}'`;
		}
	} else {
		if (startDate && endDate) {
			whereClause += ` AND rundate BETWEEN date '${startDate}' AND date '${endDate}'`;
		} else if (startDate) {
			whereClause += ` AND rundate >= date '${startDate}'`;
		} else if (endDate) {
			whereClause += ` AND rundate <= date '${endDate}'`;
		}
	}

	if (campaignId) {
		whereClause += ` AND campaign_id = ${campaignId}`;
		groupByClause = `GROUP BY campaign_id`;
	}

	if (calculation === "trend" || calculation === "sum") {
		aggColumns = `DATE_TRUNC('${groupBy}', ${groupByField}) as group_date,` + aggColumns;
		groupByClause += (groupByClause ? ", " : "GROUP BY ") + `DATE_TRUNC('${groupBy}', ${groupByField})`;
	}

	const database = process.env.ATHENA_DB || 'ecommerce';
	const orderByClause = calculation === "trend" || calculation === "sum" ? "ORDER BY group_date" : "";

	const athenaQuery = `
		SELECT ${aggColumns}
		FROM ${database}.${name}
		${whereClause}
		${groupByClause}
		${orderByClause}
	`;

	console.log(`athenaQuery 👉`, athenaQuery);


	const queryExecution = await runAthenaQueryV2(
		athenaQuery,
		process.env.ATHENA_OUTPUT_BUCKET!
	);

	const queryId = queryExecution.QueryExecutionId;
	if (!queryId) {
		throw new Error("Failed to start Athena query");
	}

	const succeeded = await waitForQueryExecution(queryId);
	if (!succeeded) {
		throw new Error("Athena query did not succeed");
	}

	const queryResults = await getQueryResults(queryId);

	if (queryResults.ResultSet != null && queryResults.ResultSet.Rows != null) {
		const formattedResults = formatResults(
			queryResults.ResultSet.Rows,
			groupBy,
			startDate || "",
			endDate || "",
			calculation || "",
			metricInfo
		);
		return {
			statusCode: 200,
			body: JSON.stringify(formattedResults),
			headers: {
				"Content-Type": "application/json",
			},
		};
	} else {
		throw new Error("Athena query returned no results");
	}
};

async function getMetricInformation(metricName: string, orgId: string): Promise<any> {
	const result = await queryDatabase(
		`SELECT datastructure, lastrundate, runfrequency, variables FROM metric JOIN organizationmetric ON metric.id = organizationmetric.metricid WHERE name = $1 and orgid = $2 LIMIT 1`,
		[metricName, orgId]
	);
	if (result && result.length > 0) {
		const metricInfo = result[0];
		if (typeof metricInfo.datastructure === 'string') {
			metricInfo.datastructure = JSON.parse(metricInfo.datastructure);
		}
		if (metricInfo.variables && typeof metricInfo.variables === 'string') {
			metricInfo.variables = JSON.parse(metricInfo.variables);
		}
		return metricInfo;
	} else {
		throw new Error(`Metric with name ${metricName} not found`);
	}
}


function createAggColumnsFromDataStructure(
	dataStructure: any,
	tableName: string,
	calculation: string,
	groupByField?: string
): string {
	const columns: string[] = [];
	for (let [key, value] of Object.entries(dataStructure)) {
		if(key === "campaign_id") {
			columns.push(key);
			continue;
		}
		if(key == groupByField) {
			continue;
		}
		if ((value as any).type === "DOUBLE" || (value as any).type === "INT64") {
			if (calculation === "trend") {
				columns.push(`ROUND(AVG(${key}),2) as ${key}`);
			} else if (calculation === "sum"){
				columns.push(`ROUND(SUM(${key}),2) as ${key}`);
			} else {
				columns.push(`ROUND((${key}),2) as ${key}`);
			}
		} else {
			columns.push(key);
		}
	}
	return columns.join(", ");
}

function createColumnsFromDataStructure(
	dataStructure: any,
	tableName: string,
	calculation: string,
	groupByField?: string
): string {
	if (calculation !== "trend" && calculation !== "sum") {
		return "*";
	}
	const columns: string[] = [];
	for (let [key, value] of Object.entries(dataStructure)) {
		if(key == groupByField) {
			continue;
		}
		columns.push(key);
	}
	return columns.join(", ");
}

function formatResults(
	results: any[],
	groupBy: string,
	startDate: string,
	endDate: string,
	calculation: string,
	metricInfo: any
): any {
	const headerRow = results[0].Data.map((cell: any) => cell.VarCharValue);
	return {
		date_processed: new Date().toLocaleDateString(),
		calculation: calculation,
		start_date: startDate,
		end_date: endDate,
		group_by: groupBy,
		last_run_date: metricInfo.lastrundate,
		run_frequency: metricInfo.runfrequency,
		data: results.slice(1).reduce((acc, row) => {
			let group_label = row.Data[0].VarCharValue;
			if (/^\d{4}-\d{2}-\d{2}(T|\s)?/.test(group_label)) {
				group_label = group_label.split(' ')[0];
			}
			const metrics = row.Data.slice(1).reduce((metricAcc: any, cell: any, index: any) => {
				const columnName = headerRow[index + 1];
				const structure = metricInfo && metricInfo.datastructure ? metricInfo.datastructure[columnName] : null;
				const label = structure?.label || columnName;
				const value = /^[0-9]*\.?[0-9]+$/.test(cell.VarCharValue) ? parseFloat(cell.VarCharValue) : cell.VarCharValue;
				const prefix = structure?.prefix || '';
				const suffix = structure?.suffix || '';
				metricAcc[columnName] = { value, prefix, suffix, label };
				return metricAcc;
			}, {});
			acc.push({ group_label, metrics });
			return acc;
		}, []),
	};
}

export const handleScheduledEvent = async (event: ScheduledEvent): Promise<void> => {
	console.log('Received CloudWatch Scheduled event:', JSON.stringify(event));
	const orgIdFromEvent = (event.detail as any)?.orgId;
  
	let sqlQuery = `
	  SELECT DISTINCT ON (om.orgId)
		m.*, om.orgId, om.metricId, om.variableOverride         AS om_variableOverride,
		om.queryOverride       AS om_queryOverride,
		om.lastRunDate, om.runFrequency, ms.name                AS ms_name,
		ms.query              AS ms_query, ms.variables         AS ms_variables,
		m.priority, lp.activationdate
	  FROM Metric m
	  LEFT JOIN OrganizationMetric om ON m.id = om.metricId
	  LEFT JOIN MetricSegment ms      ON m.metricsegmentId = ms.id
	  LEFT JOIN loyaltyprogram lp     ON om.orgId = lp.orgid
	  LEFT JOIN organization o        ON om.orgId = o.id
	  WHERE CASE
		  WHEN om.lastRunDate IS NULL THEN TRUE
		  WHEN om.runFrequency = 'day'    AND DATE(om.lastRunDate) < CURRENT_DATE THEN TRUE
		  WHEN om.runFrequency = 'week'   AND DATE(om.lastRunDate) < CURRENT_DATE - INTERVAL '6 day' THEN TRUE
		  WHEN om.runFrequency = 'monday' AND EXTRACT(DOW FROM CURRENT_DATE) = 1 AND DATE(om.lastRunDate) < CURRENT_DATE THEN TRUE
		  WHEN om.runFrequency = 'month'  AND (EXTRACT(MONTH FROM CURRENT_DATE) <> EXTRACT(MONTH FROM om.lastRunDate) OR EXTRACT(YEAR FROM CURRENT_DATE) <> EXTRACT(YEAR FROM om.lastRunDate)) THEN TRUE
		  WHEN om.runFrequency = 'year'   AND EXTRACT(YEAR FROM CURRENT_DATE) <> EXTRACT(YEAR FROM om.lastRunDate) THEN TRUE
		  ELSE FALSE
		END
		[ANDCLAUSE]
	  ORDER BY om.orgId, m.priority ASC, m.id ASC;`;
  
	sqlQuery = sqlQuery.replace('[ANDCLAUSE]', orgIdFromEvent ? `AND om.orgId = ${orgIdFromEvent}` : 'AND (o.dev IS NULL OR o.dev = false)');
  
	const rows = await queryDatabase(sqlQuery, []);
	const unique = new Set<string>();
  
	const messages = rows
	  .map((row: any) => {
		const msg = {
		  orgId: row.orgid,
		  metricId: row.id,
		  name: row.name,
		} as const;
  
		const id = `${msg.orgId}-${row.query}-${row.variables}`;
		if (unique.has(id)) return null;
		unique.add(id);
		return msg;
	  })
	  .filter(Boolean) as Array<Record<string, any>>;
    if(orgIdFromEvent) {
		await submitToPriorityBatch(messages);
	}
	else {
		await submitToBatch(messages);
	}
	console.log(`Queued ${messages.length} metrics to AWS Batch`);
  };
  
  async function submitToBatch(messages: Record<string, any>[]): Promise<void> {
	for (const message of messages) {
	  message.queue = BATCH_JOB_QUEUE;
	  await batch
		.submitJob({
		  jobName: `metric-${message.name}-${message.orgId}`.slice(0, 128), // jobName max length 128
		  jobQueue: BATCH_JOB_QUEUE,
		  jobDefinition: BATCH_JOB_DEFINITION,
		  containerOverrides: {
			environment: [
			  { name: 'METRIC_MESSAGE', value: JSON.stringify(message) }
			],
		  },
		})
		.promise();
	}
  }

async function submitToPriorityBatch(messages: Record<string, any>[]): Promise<void> {
	for (const message of messages) {
		console.log(`Submitting priority job for metric: ${message.name}, orgId: ${message.orgId}`);
		message.queue = PRIORITY_BATCH_JOB_QUEUE;
		await batch
			.submitJob({
				jobName: `priority-metric-${message.name}-${message.orgId}`.slice(0, 128),
				jobQueue: PRIORITY_BATCH_JOB_QUEUE,
				jobDefinition: BATCH_JOB_DEFINITION,
				containerOverrides: {
					environment: [
						{ name: 'METRIC_MESSAGE', value: JSON.stringify(message) }
					],
				}
			})
			.promise();
	}
}



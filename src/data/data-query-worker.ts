import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { runAthenaQueryV2, waitForQueryExecution, getQueryResults, AthenaWorkGroup } from '../utils/athena-helpers';

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
	try {
		const body = event.body ? JSON.parse(event.body) : {};
		const query = body.query || event.queryStringParameters?.query;

		if (!query) {
			return {
				statusCode: 400,
				body: JSON.stringify({ error: 'Query not provided' }),
				headers: {
					'Content-Type': 'application/json',
				},
			};
		}

		const workgroup = process.env.ATHENA_WORKGROUP || AthenaWorkGroup.ADHOC;
		const catalog = process.env.ATHENA_CATALOG || 'AWSDataCatalog';
		const database = process.env.ATHENA_DB;
		const outputBucket = process.env.ATHENA_OUTPUT_BUCKET!;

		const exec = await runAthenaQueryV2(query, outputBucket, 0, workgroup, catalog, database);

		const queryId = exec.QueryExecutionId;
		if (!queryId) {
			throw new Error('Failed to start Athena query');
		}

		const succeeded = await waitForQueryExecution(queryId);
		if (!succeeded) {
			throw new Error('Athena query did not succeed');
		}

		const results = await getQueryResults(queryId);
		const rows = results.ResultSet?.Rows ?? [];

		if (rows.length < 2) {
			return {
				statusCode: 200,
				body: JSON.stringify([]),
				headers: {
					'Content-Type': 'application/json',
				},
			};
		}

		const headers = rows[0].Data?.map(cell => cell.VarCharValue || '') || [];
		const data = rows.slice(1).map(row => {
			const values = row.Data?.map(cell => cell.VarCharValue || '') || [];
			return Object.fromEntries(headers.map((h, i) => [h, values[i]]));
		});

		return {
			statusCode: 200,
			body: JSON.stringify(data),
			headers: {
				'Content-Type': 'application/json',
			},
		};
	} catch (error) {
		return {
			statusCode: 400,
			body: JSON.stringify({ error: error instanceof Error ? error.message : 'Unknown error' }),
			headers: {
				'Content-Type': 'application/json',
			},
		};
	}
};

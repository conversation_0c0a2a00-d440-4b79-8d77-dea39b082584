import { GetSecretValueCommand, SecretsManagerClient } from '@aws-sdk/client-secrets-manager';
import { createApi } from 'unsplash-js';
import * as JSON5 from 'json5';

export const handler = async (
	event: any = {},
	context: any = {}
): Promise<any> => {
	console.log(JSON.stringify(event));

	const json = event?.json?.Payload;

	if (!json) {
		throw new Error(`No data in the json container`);
	}

	const brandingData = JSON5.parse(json);
	if (!brandingData.guest?.heroImageSearchText) {
		json.guest.heroImageSearchText = 'loyalty program';
	}

	const unsplashAccessKey = await getUnsplashApiKey();

	const unsplash = createApi({
		accessKey: unsplashAccessKey,
	});

	const image = await unsplash.search.getPhotos({
		query: brandingData.guest.heroImageSearchText,
		page: 1,
		perPage: 1,
		orientation: 'landscape'
	});

	brandingData.guest.heroImageUrl = image?.response?.results[0].urls.small;

	unsplash.photos.trackDownload(
		{ downloadLocation: image?.response?.results[0].links.download_location! }
	).catch(e => console.error(`Error tracking download: ${e}`));

	return JSON.stringify(brandingData);
}

const getUnsplashApiKey = async () => {
	const client = new SecretsManagerClient({
		region: "us-east-1",
	});
	const command = new GetSecretValueCommand({
		SecretId: process.env.UNSPLASH_SECRET_ARN,
	});
	const response = await client.send(command);
	const secret = JSON.parse(response.SecretString!);
	return secret.access_key;
}

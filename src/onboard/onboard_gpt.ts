import OpenAI from "openai";
const openai = new OpenAI({ apiKey: '***************************************************' });

export const handler = async (
  event: any = {},
  context: any = {}
): Promise<any> => {
  console.log(event.prompt);
  const output = await openai.chat.completions.create(event.prompt);

  if (event.parseGptJson) {
    return JSON.parse(output.choices[0].message.content!);
  } else if (event.sanitizeJson) {
    return output.choices[0].message.content!.replace('```json', '').replace(/```/g, '').trim();
  }

  return {
    output
  };
} 
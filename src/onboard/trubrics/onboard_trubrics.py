
import os
from trubrics import Trubrics

trubrics = Trubrics(
    project="default",
    email=os.environ["TRUBRICS_EMAIL"],
    password=os.environ["TRUBRICS_PASSWORD"],
)

def handler(event, context):
    prompt = trubrics.log_prompt(
        config_model={
            "model": event["model"],
        },
        prompt=event["prompt"],
        generation=event["generation"],
        session_id=event["session_id"],
        user_id=event["user_id"],
    )

    return ''
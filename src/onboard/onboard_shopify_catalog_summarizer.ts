import * as invoker from './onboard_shopify_api_invoker';

export const handler = async (
    event: any = {},
    context: any = {}
): Promise<any> => {
    const shopifyCatalog = await invoker.handler({
        url: event.url,
        method: "POST",
        token: event.token.Payload,
        path: "/product-catalog",
        body: {
            query: `query {products(first: 100, reverse: true, sortKey: UPDATED_AT) {  edges {node {  id  createdAt  updatedAt  title  handle  productType  priceRangeV2 {maxVariantPrice {  amount  currencyCode}minVariantPrice {  amount  currencyCode}  }  description  totalInventory  status}  }}  }`,
            variables: {}
        }
    });

    console.log(shopifyCatalog?.data?.products?.edges);

    return summarize(shopifyCatalog);
} 

function summarize(catalogData: { data: { products: { edges: any[] } } }) {
    return {
        data: {
            products: {
                edges: catalogData.data.products.edges.map((item: any) => ({
                    id: item.node.id?.split('/')?.reverse()[0],
                    createdAt: item.node?.createdAt,
                    updatedAt: item.node?.updatedAt,
                    title: item.node?.title?.slice(0, 255),
                    // handle: item.node?.handle,
                    productType: item.node?.productType,
                    // description: item.node?.description?.slice(0, 255),
                    totalInventory: item.node?.totalInventory,
                    status: item.node?.status,
                    priceRange: item.node.priceRangeV2?.minVariantPrice?.amount == item.node.priceRangeV2?.maxVariantPrice?.amount 
                        ? `${item.node.priceRangeV2?.minVariantPrice?.amount} ${item.node.priceRangeV2?.minVariantPrice?.currencyCode}`
                        :  `${item.node.priceRangeV2?.minVariantPrice?.amount} ${item.node.priceRangeV2?.minVariantPrice?.currencyCode} - ${item.node.priceRangeV2?.maxVariantPrice?.amount} ${item.node.priceRangeV2?.maxVariantPrice?.currencyCode}`,
                }))
            }
        }
    };
}

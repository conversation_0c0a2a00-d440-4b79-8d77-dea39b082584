import * as J<PERSON>N<PERSON> from 'json5';

let RECOMMENDATION_STATES = {
    APPROVED_RECOMMENDATION: 'APPROVED_RECOMMENDATION',
    RECOMMENDED: 'RECOMMENDED'
}

export const handler = async (
    event: any = {},
    context: any = {}
): Promise<any> => {
    if (!event.json) {
        event.json = event?.gptResult2?.Payload?.output?.choices?.[0]?.message?.content;
    }

    const json = typeof event.json === 'string' ? parsePlus(event.json) : event.json; 
    let value = event.jsonKey 
            ? typeof json[event.jsonKey] === 'string' ? parsePlus(json[event.jsonKey]) : json[event.jsonKey]
            : json;

    if (!Array.isArray(value)) {
        value = Object.values(value).find((x: any) => Array.isArray(x));
    }

    if (!Array.isArray(value)) {
        throw new Error('No array found in JSON');
    }

    const clone: any = event.campaignId 
        ? getCampaignTemplate(event) 
        : getProgramTemplate(event);

    console.log('CLONE', clone);

    if (!event.campaignId) {
        console.log('setting org id to ' + event.organization_id);
        (clone as any).orgId = event.organization_id;
    }
    const loyaltyCampaign = (clone as any).loyaltyCampaign || (clone as any);
    console.log('LOYALTY CAMPAIGN', loyaltyCampaign);

    const gptWaysToEarn = value.filter((x: any) => x && x.type?.startsWith('way') && x.type?.includes(':')).map((x: any) => ({ ...x, type: x.type.split(':')[1] }));
    const gptRewards = value.filter((x: any) => x && x.type?.startsWith('rew') && x.type?.includes(':')).map((x: any) => ({ ...x, type: x.type.split(':')[1] }));

    const gptSuggestion = {
        loyaltyEarn: gptWaysToEarn.map((x: any) => ({ type: x.type })),
        loyaltyRedemptionShopItem: gptRewards.map((x: any) => x.type)
    };
    console.log(gptSuggestion);
    const structureTemplate = getStructureTemplate(event, gptSuggestion);
    console.log(structureTemplate);



    // loyaltyCampaign.loyaltyEarn.forEach((loyaltyEarn: any) => {
    //     if (loyaltyEarn.name === 'Spend and Earn' && value['way-to-earn:each-dollar-spent']) {
    //         loyaltyEarn.name = value['way-to-earn:each-dollar-spent'].name;
    //         // loyaltyEarn.earnEffect[0].name = value['way-to-earn:each-dollar-spent'].name;
    //         loyaltyEarn.earnEffect[0].pointsPerDollar = value['way-to-earn:each-dollar-spent'].pointsPerDollar;
    //     } else if (loyaltyEarn.name === 'First Purchase' && value['way-to-earn:first-purchase']) {
    //         loyaltyEarn.name = value['way-to-earn:first-purchase'].name;
    //         // loyaltyEarn.earnEffect[0].name = value['way-to-earn:first-purchase'].name;
    //         loyaltyEarn.earnEffect[0].points = value['way-to-earn:first-purchase'].pointsRewarded;
    //     } else if (loyaltyEarn.name === 'Strike Gold on the Fifth' && value['way-to-earn:fifth-purchase']) {
    //         loyaltyEarn.name = value['way-to-earn:fifth-purchase'].name;
    //         // loyaltyEarn.earnEffect[0].name = value['way-to-earn:fifth-purchase'].name;
            
    //         loyaltyEarn.earnEffect[0].points = value['way-to-earn:fifth-purchase'].pointsRewarded;
    //     }
    // });

    const wayToEarnValues = gptWaysToEarn.map((x: any) => ({
        ...x
    }));
    console.log(wayToEarnValues);

    for (const wayToEarn of structureTemplate.loyaltyEarn) {
        const templateContainer = getWayToEarnTemplate(event, wayToEarn.type, wayToEarn.matchKey);
        console.log('TEMPLATE CONTAINER', templateContainer)

        if (!templateContainer) {
            console.warn('GPT suggested non-templatized way to earn type: ' + wayToEarn.type);
            continue;
        }

        loyaltyCampaign.loyaltyEarn.push(templateContainer?.template);

        const value = wayToEarnValues.find((x: any) => x.type === wayToEarn.type && (!templateContainer?.matchKey || x.matchKey == templateContainer?.matchKey || x.purchaseCount == templateContainer?.matchKey));
        if (!value) {
            console.warn('GPT omitted required way to earn type: ' + wayToEarn.type);
            continue;
        }

        wayToEarnValues.splice(wayToEarnValues.indexOf(value as any), 1);

        const template: any = templateContainer?.template!;
        template.name = value.name;
        if (template.earnEffect?.[0]?.pointsPerDollar && value.pointsPerDollar) template.earnEffect[0].pointsPerDollar = value.pointsPerDollar;
        if (template.earnEffect?.[0]?.points && value.pointsRewarded) template.earnEffect[0].points = value.pointsRewarded;

        console.log('TEMPLATE line 101', template)


        template.earnCondition.forEach((earnCondition: any) => {
            
            if (earnCondition.type === 'nth-purchase' && value.purchaseCount) earnCondition.amount = value.purchaseCount;
            if (earnCondition.type === 'timed-purchase' && value.purchaseCount) earnCondition.amount = value.purchaseCount;
            if (earnCondition.variable == 'instagramHandle' && value.instagramHandle) earnCondition.textValue = value.instagramHandle;
            if (earnCondition.variable == 'tikTokHandle' && value.tikTokHandle) earnCondition.textValue = value.tikTokHandle;
            if (earnCondition.variable == 'facebookHandle' && value.facebookUsername) earnCondition.textValue = value.facebookUsername;
            if (earnCondition.variable == 'productId' && value.productGid) earnCondition.textValue = value.productGid;

        });
    }

    
    // loyaltyCampaign.loyaltyRedemptionShopItem.forEach((loyaltyRedemptionShopItem: any) => {
    //     if (loyaltyRedemptionShopItem.name === '$10pppp Off Coupon' && value['reward:dollars-off']) {
    //         loyaltyRedemptionShopItem.name = value['reward:dollars-off'].name;
    //         loyaltyRedemptionShopItem._121_loyaltyRewardDefinition._121_rewardCoupon.name = value['reward:dollars-off'].name;
    //         loyaltyRedemptionShopItem._121_loyaltyRewardDefinition._121_rewardCoupon.amount = value['reward:dollars-off'].dollarDiscount;
    //         loyaltyRedemptionShopItem._121_loyaltyRewardDefinition._121_rewardCoupon.expiresInDays = value['reward:dollars-off'].expiresindays;
    //         loyaltyRedemptionShopItem._121_loyaltyRewardDefinition._121_rewardCoupon.minimumordertotal = value['reward:dollars-off'].minimumordertotal;
    //     } else if (loyaltyRedemptionShopItem.name === '15% Discount Coupon' && value['reward:percent-off']) {
    //         loyaltyRedemptionShopItem.name = value['reward:percent-off'].name;
    //         loyaltyRedemptionShopItem._121_loyaltyRewardDefinition._121_rewardCoupon.name = value['reward:percent-off'].name;
    //         loyaltyRedemptionShopItem._121_loyaltyRewardDefinition._121_rewardCoupon.amount = value['reward:percent-off'].percentDiscount;
    //         loyaltyRedemptionShopItem._121_loyaltyRewardDefinition._121_rewardCoupon.expiresInDays = value['reward:percent-off'].expiresindays;
    //     }
    // });

    const rewardValues = gptRewards.map((x: any) => ({
        ...x
    }));
    console.log(rewardValues)



    for (const reward of structureTemplate.loyaltyRedemptionShopItem) {
        const templateContainer = getRewardTemplate(event, reward);

        if (!templateContainer) {
            console.warn('GPT suggested non-templatized reward type: ' + reward);
            continue;
        }


        loyaltyCampaign.loyaltyRedemptionShopItem.push(templateContainer?.template);

        const value = rewardValues.find((x: any) => x.type === reward);
        if (!value) {
            console.warn('GPT omitted required reward type: ' + reward);
            continue;
        }

        rewardValues.splice(rewardValues.indexOf(value as any), 1);
        
        const template: any = templateContainer?.template!;
        template.name = value.name;
        if (template.price && value.pointsPrice) template.price = value.pointsPrice;
        if (template._121_loyaltyRewardDefinition?.price && value.pointsPrice)  template._121_loyaltyRewardDefinition.price = value.pointsPrice;
        if (template._121_loyaltyRewardDefinition?.maxUserGrants && value.maxUserGrants) template._121_loyaltyRewardDefinition.maxUserGrants = value.maxUserGrants;
        if (template._121_loyaltyRewardDefinition?.maxUserRedemptions && value.maxUserRedemptions) template._121_loyaltyRewardDefinition.maxUserRedemptions = value.maxUserRedemptions;
        if (template._121_loyaltyRewardDefinition?._121_rewardCoupon) template._121_loyaltyRewardDefinition._121_rewardCoupon.name = value.name;
        if (template._121_loyaltyRewardDefinition?._121_rewardCoupon && (value.dollarDiscount || value.percentDiscount)) {
            template._121_loyaltyRewardDefinition._121_rewardCoupon.amount = value.dollarDiscount || value.percentDiscount;
        }
        // if (template._121_loyaltyRewardDefinition?._121_rewardCoupon && (value.productId)) {
        //     template._121_loyaltyRewardDefinition._121_rewardCoupon.textValue = value.productId;
        // }
        if (template._121_loyaltyRewardDefinition?._121_rewardCoupon?.expiresInDays && value.expiresInDays) template._121_loyaltyRewardDefinition._121_rewardCoupon.expiresInDays = value.expiresInDays;
        if (template._121_loyaltyRewardDefinition?._121_rewardCoupon?.minimumOrderTotal && value.minimumOrderTotal) template._121_loyaltyRewardDefinition._121_rewardCoupon.minimumOrderTotal = value.minimumOrderTotal;
        if (template._121_loyaltyRewardDefinition?._121_rewardCoupon?.maximumDiscount && value.maximumDiscount) template._121_loyaltyRewardDefinition._121_rewardCoupon.maximumDiscount = value.maximumDiscount;
    }



    if (event.includeCampaignSummary == 'true') {
        clone.summaryHtml = value.find((x: any) => x.type === 'summaryHtml')?.value || '';
    }

    console.log('CLONE', clone)


    if (event.exclude121Prefix == 'true') {
        return removePrefix(clone);   
    }


    return clone;
} 

function getProgramTemplate(event: any) {
    return {
        name: "Copilot Recommended Program",
        orgId: 1042069,
        active: false,
        loyaltyCurrency: {
            name: "points",
            conversiontousd: 100
        },
        loyaltyCampaign: getCampaignTemplate(event)
    };
}

function getCampaignTemplate(event: any) {
    return {
        name: "Foundational Loyalty & Rewards from Copilot",
        active: true,
        evergreen: true,
        loyaltySegment: "Everyone",
        loyaltyEarn: [],
        loyaltyRedemptionShopItem: []
    };
}

function getStructureTemplate(event: any, gptSuggestion: any) {
    if (event.structureTemplate) {
        return event.structureTemplate;
    }

    if (event.allowGptTemplateSuggestion) {
        return gptSuggestion;
    }

    return {
        loyaltyEarn: [{ type: 'dollar-spent'}, { type: 'welcome-bonus' }, { type: 'birthday-bonus' }, { type: 'nth-purchase', matchKey: 2 }, { type: 'nth-purchase', matchKey: 5 }],
        loyaltyRedemptionShopItem: ['dollars-off-coupon', 'percent-discount']
    }
}

function getWayToEarnTemplate(event: any, type: string, matchKey?: string | number) {
    //NOTE dollar spent we always need one, so if this is onboarding go ahead and approve it
    const templates = [
        { 
            type: "dollar-spent",
            template: {
                name: "Spend and Earn",
                loyaltyCampaignId: event.campaignId || undefined,
                active: true,
                recommendedByAI: true,
                description: "Earn points for every dollar spent",
                imageURL: "https://raleoncdn.s3.us-east-1.amazonaws.com/WTERewardDefault.jpg",
                recommendationState: event.campaignId ? RECOMMENDATION_STATES.RECOMMENDED : RECOMMENDATION_STATES.APPROVED_RECOMMENDATION,
                earnCondition: [
                {
                    type: "dollar-spent",
                    variable: "purchaseTotalIncrease",
                    operator: ">=",
                    amount: 1,
                    triggeredEvent: "orders/create"
                }
                ],
                earnEffect: [
                {
                    pointsPerDollar: 1,
                    type: "points-per-dollar",
                    description: "Earn points for every dollar spent",
                    imageURL: "https://raleoncdn.s3.us-east-1.amazonaws.com/WTERewardDefault.jpg",
                    name: "Spend and Earn"
                }
                ]
            },
        },
        {
            type: "nth-purchase",
            matchKey: '1',
            template: {
                name: "First Purchase",
                loyaltyCampaignId: event.campaignId || undefined,
                active: true,
                recommendedByAI: true,
                recommendationState: RECOMMENDATION_STATES.RECOMMENDED,
                earnCondition: [
                {
                    type: "nth-purchase",
                    variable: "purchaseCount",
                    operator: "==",
                    amount: 1,
                    triggeredEvent: "orders/create"
                }
                ],
                earnEffect: [
                {
                    points: 500,
                    type: "points"
                }
                ]
            }
        },
        {
            type: "nth-purchase",
            matchKey: '2',
            template: {
                name: "Second Purchase",
                loyaltyCampaignId: event.campaignId || undefined,
                active: true,
                recommendedByAI: true,
                recommendationState: RECOMMENDATION_STATES.RECOMMENDED,
                earnCondition: [
                {
                    type: "nth-purchase",
                    variable: "purchaseCount",
                    operator: "==",
                    amount: 2,
                    triggeredEvent: "orders/create"
                }
                ],
                earnEffect: [
                {
                    points: 500,
                    type: "points"
                }
                ]
            }
        },
        {
            type: "nth-purchase",
            matchKey: '5',
            template: {
                name: "Strike Gold on the Fifth",
                loyaltyCampaignId: event.campaignId || undefined,
                active: true,
                recommendedByAI: true,
                recommendationState: RECOMMENDATION_STATES.RECOMMENDED,
                earnCondition: [
                {
                    type: "nth-purchase",
                    variable: "purchaseCount",
                    operator: "==",
                    amount: 5,
                    triggeredEvent: "orders/create"
                }
                ],
                earnEffect: [
                {
                    points: 500,
                    type: "points"
                }
                ]
            }
        },
        {
            type: "nth-purchase",
            template: {
                name: "Nth Purchase",
                loyaltyCampaignId: event.campaignId || undefined,
                active: true,
                recommendedByAI: true,
                recommendationState: RECOMMENDATION_STATES.RECOMMENDED,
                earnCondition: [
                {
                    type: "nth-purchase",
                    variable: "purchaseCount",
                    operator: "==",
                    amount: 3,
                    triggeredEvent: "orders/create"
                }
                ],
                earnEffect: [
                {
                    points: 500,
                    type: "points"
                }
                ]
            }
        },
        
    // ${ enabledTypes?.includes('follow-on-instagram') ? `
    //     - Points for following the store on Instagram:
    //     { "type": "way-to-earn:follow-on-instagram", "instagramHandle": $$$, "name": $$$, "pointsRewarded": $$$ }` : '' }

        {
            type: "follow-on-instagram",
            template: {
                name: "Follow On Instagram",
                loyaltyCampaignId: event.campaignId || undefined,
                active: true,
                recommendedByAI: true,
                recommendationState: RECOMMENDATION_STATES.RECOMMENDED,
                earnCondition: [
                {
                    type: "follow-on-instagram",
                    variable: "instagramHandle",
                    operator: "==",
                    textValue: ''
                }
                ],
                earnEffect: [
                {
                    points: 500,
                    type: "points"
                }
                ]
            }
        },

    // ${ enabledTypes?.includes('product-review') ? `
    //     - Points for leaving a review of a product purchased from the store:
    //     { "type": "way-to-earn:product-review", "name": $$$, "pointsRewarded": $$$ }` : '' }
        {
            type: "product-review",
            template: {
                name: "Follow On Instagram",
                loyaltyCampaignId: event.campaignId || undefined,
                active: true,
                recommendedByAI: true,
                recommendationState: RECOMMENDATION_STATES.RECOMMENDED,
                earnCondition: [
                {
                    type: "product-review",
                    variable: "maxReviews",
                    operator: "==",
                    amount: 3
                }
                ],
                earnEffect: [
                {
                    points: 500,
                    type: "points"
                }
                ]
            }
        },


    // ${ enabledTypes?.includes('timed-purchase') ? `
    //     - Points for making a certain number of purchases within a time period:
    //     { "type": "way-to-earn:timed-purchase", "purchaseCount": $$$, "name": $$$, "pointsRewarded": $$$ }` : '' }


        {
            type: "timed-purchase",
            template: {
                name: "Timed Purchase",
                loyaltyCampaignId: event.campaignId || undefined,
                active: true,
                recommendedByAI: true,
                recommendationState: RECOMMENDATION_STATES.RECOMMENDED,
                earnCondition: [
                {
                    type: "timed-purchase",
                    variable: "purchaseCount",
                    operator: "==",
                    amount: 3
                }
                ],
                earnEffect: [
                {
                    points: 500,
                    type: "points"
                }
                ]
            }
        },
    // ${ enabledTypes?.includes('follow-on-tiktok') ? `
    //     - Points for following the store on TikTok:
    //     { "type": "way-to-earn:follow-on-tiktok", "tikTokHandle": $$$, "name": $$$, "pointsRewarded": $$$ }` : '' }


        {
            type: "follow-on-tiktok",
            template: {
                name: "Follow On TikTok",
                loyaltyCampaignId: event.campaignId || undefined,
                active: true,
                recommendedByAI: true,
                recommendationState: RECOMMENDATION_STATES.RECOMMENDED,
                earnCondition: [
                {
                    type: "follow-on-tiktok",
                    variable: "tikTokHandle",
                    operator: "==",
                    textValue: ''
                }
                ],
                earnEffect: [
                {
                    points: 500,
                    type: "points"
                }
                ]
            }
        },

    // ${ enabledTypes?.includes('follow-on-facebook') ? `
    //     - Points for liking the store on Facebook:
    //     { "type": "way-to-earn:follow-on-facebook", "facebookUsername": $$$, "name": $$$, "pointsRewarded": $$$ }` : '' }

        {
            type: "follow-on-facebook",
            template: {
                name: "Follow On facebook",
                loyaltyCampaignId: event.campaignId || undefined,
                active: true,
                recommendedByAI: true,
                recommendationState: RECOMMENDATION_STATES.RECOMMENDED,
                earnCondition: [
                {
                    type: "follow-on-facebook",
                    variable: "facebookHandle",
                    operator: "==",
                    textValue: ''
                }
                ],
                earnEffect: [
                {
                    points: 500,
                    type: "points"
                }
                ]
            }
        },


    // ${ enabledTypes?.includes('specific-product-purchase') ? `
    //     - Reward customers for purchasing a specific product:
    //     { "type": "way-to-earn:specific-product-purchase", "productGid": $$$, "name": $$$, "pointsRewarded": $$$ }` : '' }


        {
            type: "specific-product-purchase",
            template: {
                name: "Customer Buys Product",
                loyaltyCampaignId: event.campaignId || undefined,
                active: true,
                recommendedByAI: true,
                recommendationState: RECOMMENDATION_STATES.RECOMMENDED,
                earnCondition: [
                {
                    type: "specific-product-purchase",
                    variable: "productId",
                    operator: "==",
                    textValue: '',
                    triggeredEvent: "orders/create"
                }
                ],
                earnEffect: [
                {
                    points: 500,
                    type: "points"
                }
                ]
            }
        },

    // ${ enabledTypes?.includes('specific-collection-purchase') ? `
    //     - Reward customers for purchasing a specific collection:
    //     { "type": "way-to-earn:specific-collection-purchase", "collectionGid": $$$, "name": $$$, "pointsRewarded": $$$ }` : '' }
    // `;


        {
            type: "specific-collection-purchase",
            template: {
                name: "Customer Buys From Collection",
                loyaltyCampaignId: event.campaignId || undefined,
                active: true,
                recommendedByAI: true,
                recommendationState: RECOMMENDATION_STATES.RECOMMENDED,
                earnCondition: [
                {
                    type: "specific-collection-purchase",
                    variable: "collectionId",
                    operator: "==",
                    textValue: '',
                    triggeredEvent: "orders/create"
                }
                ],
                earnEffect: [
                {
                    points: 500,
                    type: "points"
                }
                ]
            }
        },
    
    // ${ enabledTypes?.includes('welcome-bonus') ? `
    // - Reward customers for joining the rewards program:
    //   { "type": "way-to-earn:welcome-bonus", "name": $$$, "pointsRewarded": $$$ }` : '' }


        {
            type: "welcome-bonus",
            template: {
                name: "Customer Joins Rewards Program",
                loyaltyCampaignId: event.campaignId || undefined,
                active: true,
                recommendedByAI: true,
                recommendationState: RECOMMENDATION_STATES.RECOMMENDED,
                earnCondition: [
                {
                    type: "welcome-bonus",
                    variable: "joins",
                    operator: "==",
                    amount: 1
                }
                ],
                earnEffect: [
                {
                    points: 500,
                    type: "points"
                }
                ]
            }
        },


        {
            type: "birthday-bonus",
            template: {
                name: "Birthday Bonus",
                loyaltyCampaignId: event.campaignId || undefined,
                active: true,
                recommendedByAI: true,
                recommendationState: RECOMMENDATION_STATES.RECOMMENDED,
                earnCondition: [
                {
                    type: "birthday-bonus",
                    variable: "birthday",
                    operator: "==",
                    amount: 1
                }
                ],
                earnEffect: [
                {
                    points: 500,
                    type: "points"
                }
                ]
            }
        },
    
    ];

    const matchKeyMatch = templates.find((x: any) => x.type === type && x.matchKey === matchKey);
    if (matchKeyMatch) {
        return matchKeyMatch;
    }
    
    const noMatchKeyMatch = templates.find((x: any) => x.type === type && (x.matchKey === null || x.matchKey === undefined || x.matchKey === ''));
    if ((matchKey === null || matchKey === undefined || matchKey === '') && noMatchKeyMatch) {
        return noMatchKeyMatch;
    }
    
    return templates.find((x: any) => x.type === type);
}

function getRewardTemplate(event: any, type: string) { 
    const templates = [
        {
            type: "dollars-off-coupon",
            template: {
                name: "$10 Off Coupon",
                loyaltyCampaignId: event.campaignId || undefined,
                active: true,
                price: 1000,
                recommendedByAI: true,
                recommendationState: RECOMMENDATION_STATES.RECOMMENDED,
                _121_loyaltyRewardDefinition: {
                    loyaltyCampaignId: event.campaignId || "@loyaltyCampaign",
                    redeemable: true,
                    daysToRedeem: 365,
                    price: 1000,
                    maxUserRedemptions: 1,
                    _121_rewardCoupon: {
                        name: "$10 Off Next Purchase",
                        amount: 10,
                        amountType: "dollars-off-coupon",
                        expiresInDays: 30,
                        minimumOrderTotal: 0
                    }
                }
            }
        },
        {
            type: "percent-discount",
            template: {
                name: "15% Discount Coupon",
                loyaltyCampaignId: event.campaignId || undefined,
                active: true,
                price: 1500,
                recommendedByAI: true,
                recommendationState: RECOMMENDATION_STATES.RECOMMENDED,
                _121_loyaltyRewardDefinition: {
                    loyaltyCampaignId: event.campaignId ||  "@loyaltyCampaign",
                    redeemable: true,
                    price: 1500,
                    daysToRedeem: 365,
                    maxUserRedemptions: 1,
                    _121_rewardCoupon: {
                        name: "15% Off Next Purchase",
                        amount: 15,
                        amountType: "percent-discount",
                        expiresInDays: 30,
                        minimumOrderTotal: 0
                    }
                }
            }
        },


    // ${ enabledTypes?.includes('free-shipping') ? `
    //     - Percent off coupon:
    //     { "type": "reward:free-shipping", "pointsPrice": $$$, "name": $$$, "expiresInDays": $$$, "minimumOrderTotal": $$$ }` : '' }
    
        {
            type: "free-shipping",
            template: {
                name: "Free Shipping",
                loyaltyCampaignId: event.campaignId || undefined,
                active: true,
                price: 1500,
                _121_loyaltyRewardDefinition: {
                    loyaltyCampaignId: event.campaignId ||  "@loyaltyCampaign",
                    redeemable: true,
                    price: 1500,
                    daysToRedeem: 365,
                    maxUserRedemptions: 1,
                    _121_rewardCoupon: {
                        name: "Free Shipping",
                        amount: 1,
                        amountType: "free-shipping",
                        expiresInDays: 30,
                        minimumOrderTotal: 0
                    }
                }
            }
        },



    // ${ enabledTypes?.includes('free-product') ? `
    //     - Percent off coupon:
    //     { "type": "reward:free-product", "pointsPrice": $$$, "name": $$$, "productGid": $$$, "expiresInDays": $$$, "minimumOrderTotal": $$$ }` : '' }
    
        {
            type: "free-product",
            template: {
                name: "Free Product",
                loyaltyCampaignId: event.campaignId || undefined,
                active: true,
                price: 1500,
                _121_loyaltyRewardDefinition: {
                    loyaltyCampaignId: event.campaignId ||  "@loyaltyCampaign",
                    redeemable: true,
                    price: 1500,
                    daysToRedeem: 365,
                    maxUserRedemptions: 1,
                    _121_rewardCoupon: {
                        name: "Free Product",
                        // textValue: '',
                        amountType: "free-product",
                        expiresInDays: 30,
                        minimumOrderTotal: 0
                    }
                }
            }
        }


    ];

    return templates.find((x: any) => x.type === type);
}

function parsePlus(value: string) {
    try {
        return JSON5.parse(value);
    } catch (e) {
        return JSON5.parse(value + '}');
    }
}

function removePrefix(obj: any, prefix: string = '_121_'): any {
    if (typeof obj !== 'object' || obj === null) {
        return obj;
    }

    let result = Array.isArray(obj) ? [] : {};

    for (let key in obj) {
        let newKey = key?.startsWith(prefix) ? key.slice(prefix.length) : key;
        (result as any)[newKey] = removePrefix(obj[key], prefix);
    }

    return result;
}

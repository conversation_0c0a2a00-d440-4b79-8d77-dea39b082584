import { APIGatewayProxyResultV2, SQSEvent } from 'aws-lambda';
import { StepFunctions } from 'aws-sdk';

const stepfunctions = new StepFunctions();

export async function main(event: SQSEvent): Promise<APIGatewayProxyResultV2> {
    const messages = event.Records.map(record => {
        console.log('record.body 👉', JSON.stringify(record.body, null, 2));
        const messageParsed = JSON.parse(record.body);
        console.log('messageParsed 👉', JSON.stringify(messageParsed, null, 2));
        const message = JSON.parse(messageParsed.Message);
        console.log('message 👉', JSON.stringify(message, null, 2));
        return message; 
        
        // {
		// 	org_id: message.org_id,
        //     shop_domain: message.shop_domain,
		// };
    });

    console.log('messages 👉', JSON.stringify(messages, null, 2));
    //Should only be one message, but in case we'll wrap it with a for loop
    for (const message of messages) {
        //Kick off a step function to start the onboarding process
        const params = {
            stateMachineArn: process.env.ONBOARD_STEP_FUNCTION_ARN!,
            input: JSON.stringify({
                
                // defaults
                skipShopify: 'false',
                skipProgram: 'false',
                ignoreExistingProgram: 'false',
                skipPersist: 'false',
                s3Bucket: '',
                s3ResultKey: '',
                allowGptTemplateSuggestion: 'false',
                programSetupPrompt: '',
                programResponsePrompt: '',
                programUserPrompt: '',
                includeCampaignSummary: 'false',
                campaignId: null,
                topLevelTableName: 'loyaltyprogram',

                skipBranding: 'false',
                storePassword: "raychu",
                overwriteBranding: 'false',
                skipInsights: 'false',
                ignoreExistingInsights: 'false',

                // input
                ...message,

                // overrides
                url: message.url.startsWith('http') ? message.url : `https://${message.url}`,
                organization_id_string: message.organization_id.toString(),
                programResultS3Bucket: process.env.PROGRAM_RESULT_S3_BUCKET_NAME!,
                scrapeResultS3Bucket: process.env.SCRAPE_RESULT_S3_BUCKET_NAME!,
                catalogResultS3Bucket: process.env.CATALOG_RESULT_S3_BUCKET_NAME!,
            }),
        };

        try {
            console.log("Starting step function with params:", JSON.stringify(params, null, 2));
            const response = await stepfunctions.startExecution(params).promise();
            console.log('Step function started with execution ARN:', response.executionArn);
        } catch (error) {
            console.log('error 👉', error);
        }
    }

    return {
        statusCode: 200,
        body: JSON.stringify({
            message: 'Onboard Request Received',
            input: event,
        })
    }
}


async function waitForExecution(executionArn: any) {
    while (true) {
        const executionStatus = await stepfunctions.describeExecution({ executionArn }).promise();
        const status = executionStatus.status;
        
        if (status === 'SUCCEEDED') {
            // The execution has succeeded, you can retrieve the output here
            console.log('Execution succeeded.');
            console.log('Output:', executionStatus.output);
            break;
        } else if (status === 'FAILED' || status === 'TIMED_OUT' || status === 'ABORTED') {
            // The execution has failed, you can handle the error here
            console.error('Execution failed:', executionStatus.error);
            break;
        } else {
            // The execution is still in progress, wait and check again
            await new Promise(resolve => setTimeout(resolve, 5000)); // Wait for 5 seconds before checking again
        }
    }
}
import fetch from 'node-fetch';
import { SHOPIFY_API_VERSION } from '../utils/shopify-helper';

export const handler = async (
  event: any = {},
  context: any = {}
): Promise<any> => {
  console.log('test');
  const apiToken = event.token;
  
  const response = await fetch(`${event.url}/admin/api/${SHOPIFY_API_VERSION}/graphql.json`, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				"X-Shopify-Access-Token": apiToken,
			},
			body: JSON.stringify(event.body)
		});
		
		const jsonResponse: any = await response.json();

    return jsonResponse;
} 

// import { WEBAPP_API_URL } from '../events/webhook-worker';
// async function fetchShopify(
//   authToken: string,
//   url: string,
//   method: string,
//   body?: string
// ): Promise<any> {
//   let response;
// 		const options: any = {
// 			method,
// 			headers: {
// 				"Content-Type": "application/json",
// 				Authorization: `Bearer ${authToken}`,
// 				'ngrok-skip-browser-warning': true,
// 			},
// 		};
// 		if (body) options.body = body;
//     console.log('test3');
// 		try {
// 			response = await fetch(url, options);
// 		} catch (e) {
// 			console.log("error 👉", e);
// 		}

// 		return await response!.json();

//   // let response;
//   // const options: any = {
//   //   url,
//   //   method,
//   //   headers: {
//   //     "Content-Type": "application/json",
//   //     Authorization: `Bearer ${authToken}`,
//   //     'ngrok-skip-browser-warning': true,
//   //   },
//   // };
//   // if (body) options.body = body;
//   // try {
    
//   //   response = await new Promise((r, reject) => {
//   //     const req = https.request(options, (res: any) => {
//   //         let result = '';
//   //         console.log('Status:', res.statusCode);
//   //         console.log('Headers:', JSON.stringify(res.headers));
//   //         res.setEncoding('utf8');
//   //         res.on('data', (chunk: any) => result += chunk);
//   //         res.on('end', () => {
//   //             console.log('Successfully processed HTTPS response');
//   //             // If we know it's JSON, parse it
//   //             if (res.headers['content-type'] === 'application/json') {
//   //                 result = JSON.parse(result);
//   //             }
//   //             r(result);
//   //         });
//   //     });
//   //     req.on('error', reject);
//   //     req.write(JSON.stringify(body));
//   //     req.end();

//   //   });

//   // } catch (e) {
//   //   console.log("error 👉", e);
//   // }

//   // return response; // await response!.json();
// }
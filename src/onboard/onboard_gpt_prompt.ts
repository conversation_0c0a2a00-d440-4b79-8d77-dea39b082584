export const handler = async (
  event: any = {},
  context: any = {}
): Promise<any> => {

  const messages = [
    {
      role: 'system',
      content: `This conversation will be about an e-commerce store with the following URL ${event.url}`
    },
    
    {
      "role": "system",
      "content": "The store is associated with the following org id:"
    },
    {
      "role": "system",
      "content": JSON.stringify(event.organization_id)
    }
  ];

  const scrapeMessages = [];
  if (event.scrapeResults?.web?.text) {
    scrapeMessages.push({
        "role": "system",
        "content": "The following text is text scraped from the website:"
      },
      {
        "role": "system",
        "content": event.scrapeResults.web.text.slice(0, 10000)
    });
  }

  if (event.scrapeResults?.web?.links) {
    scrapeMessages.push({
        "role": "system",
        "content": "The following links are links scraped from the website:"
      },
      {
        "role": "system",
        "content": event.scrapeResults.web.links.join(', ').slice(0, 5000)
    }); 
  }

  if (event.scrapeResults?.shopify?.data) {
    scrapeMessages.push({
        "role": "system",
        "content": "The following is the shopify catalog data from the website:"
      },
      {
        "role": "system",
        "content": JSON.stringify(event.scrapeResults.shopify.data).slice(0, 100000)
    });
  }

  const prompt = {
    messages: [
      ...messages,
      {
        role: 'system',
        content: event.programSetupPrompt || `
        
          You are going to be asked to create a loyalty program campaign for the e-commerce store from the website.
          - Loyalty Program Objectives:
            - Customers can earn points and other rewards for certain actions
              - The action portion of the "way to earn" will be represented by conditions
                - For example, "Each $ Spent" is a condition that represents the action of a customer spending money
              - NTH Purchase, would represent a customer making their NTH purchase since being a patron of the store
              - etc
            - points are worth 100 points per dollar ($0.01 per point)
            - Customers can redeem points for specific rewards directly
            - The objective of the points and rewards is to incentivize customers to return for more purchases in the future, building brand loyalty in the process.
              - However, the points system and rewards must be appropriately awarded so that the store doesn't give away too much or too little.
              - In your recommendations, attempt to find the optimal balance in the rewards such that they are:
              - enticing and relevant for a customer of this store
                - ie. rewards should be valuable and attainable
              - profitable to the store
                - ie. ensure that the store isn't giving away too much gross profit wtih excessive points, discounting, rewards, etc that are too easily achieved.

        `
      },
      {
          role: 'system',
          //
          // - Array items can be repeated as many or as little times (including 0) as makes sense. 
          // - For example, way-to-earn:nth-purchase can be used once to represent "First Purchase" and again to represent "Fifth Purchase" etc.
          content: event.programResponsePrompt || (generateStandardPrompt(event))
        },      
        {
          role: "user",
          content: 
          event.programUserPrompt || `
            Please create an example loyalty program campaign for the e-commerce store from the website
          `
        },
    ], 
    model: event.model || "gpt-4o-2024-08-06",
    response_format: { "type": "json_object" },
    max_tokens: event.max_tokens || 4096,
    
    // function_call: { name: 'createLoyaltyProgram' },
    // functions: [
    //   {
    //       name: "createLoyaltyProgram",
    //       description: "Create a loyalty program complete ways to earn and redeemable rewards.",
    //       parameters: {
    //         type: "object",
    //         properties: {
    //             "name": { type: "string" },
    //             "orgId": { type: "number" },
    //             "active": { type: "boolean" },
    //             "loyaltycurrency": {
    //               "type": "object",
    //               "properties": {
    //                 "name": { type: "string" },
    //                 "conversiontousd": { type: "number" }
    //               },
    //               required: ["name", "conversiontousd"],
    //               additionalProperties: false
    //             },
    //             "loyaltycampaign": {
    //               "type": "object",
    //               "properties": {
    //                 "name": { type: "string" },
    //                 "active": { type: "boolean" },
    //                 "evergreen": { type: "boolean" },
    //                 "loyaltyearn": {
    //                   "type": "array",
    //                   "items": {
    //                     "type": "object",
    //                     "properties": {
    //                       "name": { type: "string" },
    //                       "earncondition": {
    //                         "type": "array",
    //                         "items": {
    //                           "type": "object",
    //                           "properties": {
    //                             "type": { type: "string", enum: event.scrapeResults.wte.map((x: any) => x.type) },
    //                             "variable": { type: "string", enum: event.scrapeResults.wteConditions.map((x: any) => x.variable) },
    //                             "operator": { type: "string", enum: event.scrapeResults.wteConditions.map((x: any) => x.operatorcomparison) },
    //                             "amount": { type: "number" },
    //                             "triggeredevent": { type:  "string", enum: event.scrapeResults.wte.map((x: any) => x.triggeredevent) }
    //                           },
    //                           required: ["type", "variable", "operator"],
    //                           additionalProperties: false
    //                         }
    //                       },
    //                       "earneffect": {
    //                         "type": "array",
    //                         "items": {
    //                           "type": "object",
    //                           "properties": {
    //                             "points": { type: "number" },
    //                             "pointsPerDollar": { type: "number" },
    //                             "type": { type: "string", enum: event.scrapeResults.wteRewards.map((x: any) => x.type) },
    //                             "_121_loyaltyrewarddefinition": {
    //                               "type": "object",
    //                               "properties": {
    //                                 "grantable": { type: "boolean" },
    //                                 "daystoredeem": { type: "number" },
    //                                 "maxusergrants": { type: "number" },
    //                                 "_121_rewardcoupon": {
    //                                   "type": "object",
    //                                   "properties": {
    //                                     "name": { type: "string" },
    //                                     "amount": { type: "number" },
    //                                     "amounttype": { type: "string", enum: event.scrapeResults.wteRewards.map((x: any) => x.type) },
    //                                     "expiresindays": { type: "number" },
    //                                     "maximumdiscount": { type: "number" },
    //                                     "minimumordertotal": { type: "number" }
    //                                   },
    //                                   required: ["name", "amount", "amounttype"]
    //                                 }
    //                               },
    //                               required: ["grantable", "_121_rewardcoupon"]
    //                             }
    //                           },
    //                           required: ["type"],
    //                           additionalProperties: false
    //                         }
    //                       }
    //                     },
    //                     required: ["name", "earncondition", "earneffect"],
    //                     additionalProperties: false
    //                   }
    //                 },
    //                 "loyaltyredemptionshopitem": {
    //                   "type": "array",
    //                   "items": {
    //                     "type": "object",
    //                     "properties": {
    //                       "name": { type: "string" },
    //                       "price": { type: "number" },
    //                       "_121_loyaltyrewarddefinition": {
    //                         "type": "object",
    //                         "properties": {
    //                           "redeemable": { type: "boolean" },
    //                           "daystoredeem": { type: "number" },
    //                           "maxUserRedemptions": { type: "number" },
    //                           "_121_rewardcoupon": {
    //                             "type": "object",
    //                             "properties": {
    //                               "name": { type: "string" },
    //                               "amount": { type: "number" },
    //                               "amounttype": { type: "string", enum: event.scrapeResults.rewards.map((x: any) => x.type) },
    //                               "expiresindays": { type: "number" },
    //                               "maximumdiscount": { type: "number" },
    //                               "minimumordertotal": { type: "number" }
    //                             },
    //                             required: ["name", "amount", "amounttype"]
    //                           }
    //                         },
    //                         required: ["redeemable", "price", "_121_rewardcoupon"],
    //                       }
    //                     },
    //                     required: ["name", "price", "_121_loyaltyrewarddefinition"],
    //                     additionalProperties: false
    //                   }
    //                 }
    //               },
    //               required: ["name", "active", "evergreen", "loyaltyearn", "loyaltyredemptionshopitem"],
    //               additionalProperties: false
    //             }
    //           },
    //           required: ["name", "orgId", "active", "loyaltycurrency", "loyaltycampaign"],
    //           additionalProperties: false
    //         },
    //     }
    // ]
  };

  return {
    url: event.url,
    organization_id: event.organization_id,
    organization_id_string: event.organization_id_string,
    storePassword: event.storePassword,
    campaignId: event.campaignId,
    skipPersist: event.skipPersist,
    programResultS3Bucket: event.programResultS3Bucket,
    catalogResultS3Bucket: event.catalogResultS3Bucket,
    scrapeResultS3Bucket: event.scrapeResultS3Bucket,
    s3ResultKey: event.s3ResultKey,
    topLevelTableName: event.campaignId ? 'loyaltycampaign' : 'loyaltyprogram',
    allowGptTemplateSuggestion: event.allowGptTemplateSuggestion,
    includeCampaignSummary: event.includeCampaignSummary,
    skipShopify: event.skipShopify,
    skipBranding: event.skipBranding,
    overwriteBranding: event.overwriteBranding,
    skipProgram: event.skipProgram,
    ignoreExistingProgram: event.ignoreExistingProgram,
    skipInsights: event.skipInsights,
    ignoreExistingInsights: event.ignoreExistingInsights,
    programSetupPrompt: event.programSetupPrompt,
    programResponsePrompt: event.programResponsePrompt,
    programUserPrompt: event.programUserPrompt,
      
    scrapeResults: {
      dbFields: [], // event.scrapeResults?.dbFields,
      wte: event.scrapeResults?.wte,
      wteRewards: event.scrapeResults?.wteRewards,
      rewards: event.scrapeResults?.rewards,
    },
    prompt,
  }

} 


function generateStandardPrompt(event: any) {
  if (event.allowGptTemplateSuggestion != 'true') {
    return `

      - The response should be this exact JSON array below with details marked with $$$ replaced with relevant values pertaining to the question below
      - Any time the "name" property string (and only the "name" property) refers to a currency value, the placeholder, {$}, should be used in place of the actual numerical value/currency symbol/etc
        - Other property values, such as "dollarDiscount", "minimumOrderTotal", etc should just use raw numeric values

      {
        "campaign": [
          { "type": "way-to-earn:dollar-spent", "name": $$$, "pointsPerDollar": $$$ },
          { "type": "way-to-earn:nth-purchase", "purchaseCount": 1, "name": $$$, "pointsRewarded": $$$ },
          { "type": "way-to-earn:nth-purchase", "purchaseCount": 5, "name": $$$, "pointsRewarded": $$$ },
          { "type": "reward:dollars-off-coupon", "pointsPrice": $$$, "name": $$$, "dollarDiscount": $$$, "expiresInDays": $$$, "minimumOrderTotal": $$$ },
          { "type": "reward:percent-discount", "pointsPrice": $$$, "name": $$$, "percentDiscount": $$$, "expiresInDays": $$$, },
        ]
      }
    `;

  }

  return `

  - This campaign is being created for a store that has already set up loyalty campaign(s) in the past
  - Include a formatted text summary of the campaign in an HTML string in the summaryHtml field.
    - The audience for this summary is the loyalty campaign manager / marketing team.
    - Don't mention the specific ways to earn, rewards, or amounts in the summaryHtml, just the stylistic summary of the campaign.

  - There can be as many or as few ways to earn and rewards as you want. 
      - Generally, there should be at least 1 way to earn and at least 1 reward, but there can be zero in some cases
          - For example: An exclusive limited-time reward campaign, may have zero ways to earn, and just a single reward
      - Sometimes a campaign may have multiple instances of the same way to earn or reward, but with different values
          - For example, a campaign may have 2 nth-purchase ways to earn, one for the customer's first purchase, and one for a milestone purchase, like 5 or 10 purchases
      - Additionally, a temporary "booster" campaign may only have ways to earn, and no rewards.
          - For example, a holiday booster campaign that gives bonus rewards on top of the normal rewards from the store's existing campaigns, for a limited time
      - The user may ask for a specific number and/or specific types of ways to earn and rewards. 
          - If they do mention specific quantities, ways to earn, and/or rewards, be sure to follow their request as closely as possible
  - Try to use as many of the ways to earn and reward types that are relevant to the store as possible
  - Any time the "name" property string (and only the "name" property) refers to a currency value, the placeholder, {$}, should be used in place of the actual numerical value/currency symbol/etc
    - Other property values, such as "dollarDiscount", "minimumOrderTotal", etc should just use raw numeric values

  - The response JSON object should look like this, with the campaign array containing the ways to earn, rewards, etc:

  {
      "campaign": [
          ...
      ]
  }


  - All array items must match one of the following schemas EXACTLY, with the $$$ replaced with relevant values:
      - Ways to Earn:
          ${ getWayToEarnTemplates(event) }
      - Rewards:
          ${ getRewardTemplates(event) }
      - Summary HTML:       
          { "type": "summaryHtml", "value": $$$ }

  `;
}

function getWayToEarnTemplates(event: any) {
  const isAiCampaigns = event.campaignId;
  const isOnboarding = !isAiCampaigns;
  const enabledTypes = event
    .scrapeResults
    ?.wte
    ?.filter((x: any) => x.includeinai && isOnboarding || x.includeinaicampaigns && isAiCampaigns)
    ?.map((x: any) => x.type);

  return `
    ${ enabledTypes?.includes('dollar-spent') ? `
      - Points for each dollar spent:
        { "type": "way-to-earn:dollar-spent", "name": $$$, "pointsPerDollar": $$$ }` : '' }
    ${ enabledTypes?.includes('nth-purchase') ? `
      - Points for reaching a specific purchase count:
        { "type": "way-to-earn:nth-purchase", "purchaseCount": $$$, "name": $$$, "pointsRewarded": $$$ }` : '' }
    ${ enabledTypes?.includes('follow-on-instagram') ? `
      - Points for following the store on Instagram:
        { "type": "way-to-earn:follow-on-instagram", "instagramHandle": $$$, "name": $$$, "pointsRewarded": $$$ }` : '' }
    ${ enabledTypes?.includes('product-review') ? `
      - Points for leaving a review of a product purchased from the store:
        { "type": "way-to-earn:product-review", "name": $$$, "pointsRewarded": $$$ }` : '' }
    ${ enabledTypes?.includes('timed-purchase') ? `
      - Points for making a certain number of purchases within a time period:
        { "type": "way-to-earn:timed-purchase", "purchaseCount": $$$, "name": $$$, "pointsRewarded": $$$ }` : '' }
    ${ enabledTypes?.includes('follow-on-tiktok') ? `
      - Points for following the store on TikTok:
      { "type": "way-to-earn:follow-on-tiktok", "tikTokHandle": $$$, "name": $$$, "pointsRewarded": $$$ }` : '' }
    ${ enabledTypes?.includes('follow-on-facebook') ? `
      - Points for liking the store on Facebook:
        { "type": "way-to-earn:follow-on-facebook", "facebookUsername": $$$, "name": $$$, "pointsRewarded": $$$ }` : '' }
    ${ enabledTypes?.includes('specific-product-purchase') ? `
      - Reward customers for purchasing a specific product:
        { "type": "way-to-earn:specific-product-purchase", "productGid": $$$, "name": $$$, "pointsRewarded": $$$ }` : '' }
    ${ enabledTypes?.includes('specific-collection-purchase') ? `
      - Reward customers for purchasing a specific collection:
        { "type": "way-to-earn:specific-collection-purchase", "collectionGid": $$$, "name": $$$, "pointsRewarded": $$$ }` : '' }
    ${ enabledTypes?.includes('welcome-bonus') ? `
      - Reward customers for joining the rewards program:
        { "type": "way-to-earn:welcome-bonus", "name": $$$, "pointsRewarded": $$$ }` : '' }
    ${ enabledTypes?.includes('birthday-bonus') ? `
      - Reward customers on their birthday:
        { "type": "way-to-earn:birthday-bonus", "name": $$$, "pointsRewarded": $$$ }` : '' }
  `;
}

function getRewardTemplates(event: any) {
  const isAiCampaigns = event.campaignId;
  const isOnboarding = !isAiCampaigns;
  const enabledTypes = event
    .scrapeResults
    ?.rewards
    ?.filter((x: any) => x.includeinai && isOnboarding || x.includeinaicampaigns && isAiCampaigns)
    ?.map((x: any) => x.type);

  return `
    ${ enabledTypes?.includes('dollars-off-coupon') ? `
      - Dollars off coupon:
        { "type": "reward:dollars-off-coupon", "pointsPrice": $$$, "name": $$$, "dollarDiscount": $$$, "expiresInDays": $$$, "minimumOrderTotal": $$$ }` : '' }
    ${ enabledTypes?.includes('percent-discount') ? `
      - Percent off coupon:
        { "type": "reward:percent-discount", "pointsPrice": $$$, "name": $$$, "percentDiscount": $$$, "expiresInDays": $$$, }` : '' }
    ${ enabledTypes?.includes('free-shipping') ? `
      - Percent off coupon:
        { "type": "reward:free-shipping", "pointsPrice": $$$, "name": $$$, "expiresInDays": $$$, "minimumOrderTotal": $$$ }` : '' }
    ${ enabledTypes?.includes('free-product') ? `
      - Percent off coupon:
        { "type": "reward:free-product", "pointsPrice": $$$, "name": $$$, "productGid": $$$, "expiresInDays": $$$, "minimumOrderTotal": $$$ }` : '' }
  `;
}


import { Context } from 'aws-lambda';
import StepFunctions = require('aws-sdk/clients/stepfunctions');

export async function main(event: any, context: Context): Promise<any> {
    const params = {
        stateMachineArn: process.env.STATE_MACHINE_ARN!,
        input: JSON.stringify(event),
    };

    try {
        const stepfunctions = new StepFunctions();
        const data = await new Promise((r, rej) => stepfunctions.startExecution(params, (err, data) => err ? rej(err) : r(data)));
        console.log('Step Function executed:', data);
        return { statusCode: 200, body: JSON.stringify(data) };
    } catch (error) {
        console.error('Error executing Step Function:', error);
        return { statusCode: 500, body: JSON.stringify(error) };
    }
}

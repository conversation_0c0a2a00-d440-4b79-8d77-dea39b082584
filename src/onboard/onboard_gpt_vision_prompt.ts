export const handler = async (
  event: any = {},
  context: any = {}
): Promise<any> => {
  const prompt = {
    messages: [
      // {
			// 	role: "system",
			// 	content: `The messages following the questions are data scraped from an ecommerce website, ${event.url}. Please determine and answer the following questions:
			// 	  - which industry does the store most closely fit into out of the following: (Health, Household Care, Beauty, Toys, Hobby, DIY, Fashion - Non-Luxury, Fashion - Luxury, Food, Electronics, Cooking)?
			// 	  - what are the social media URLs for this ecommerce website?
			// 	    - Make sure to include all discovered links from sites including but not limited to:
			// 		  - Facebook
			// 		  - Twitter
			// 		  - Instagram
			// 		  - Pintrest
			// 		  - LinkedIn
			// 		  - Youtube
			// 		  - TikTok
			// 		  - Snapchat
			// 		  - Twitch
			// 		  - Reddit
			// 		  - Tumblr

			// 	  Format the answer in its entirety as valid JSON (no leading or trailing text around the JSON, no backtick delimiters/etc around the JSON, no trailing commas after the last array item or object property, no comments, correctly escaped string values, etc), with the following schema:

				  // {
					// "industry": "Fashion - Non-Luxury",
					// "socialMedia": [
					// 	{ "type": "facebook", "url":  "https://www.facebook.com/vervecoffee" },
					// 	{ "type": "instagram", "url":  "https://www.instagram.com/vervecoffee/" },
					// ],
					// "branding": {
					// 	"colors": {
					// 		"backgroundColor": "#000000",
					// 		"textColor": "#ffffff",
					// 		"buttonTextColor": "#000000",
					// 		"buttonBackgroundColor": "#ffffff",
					// 		"linkColor": "#aaaaaa",
					// 		"accentColor": {
					// 			"from": "#f0abfc",
					// 			"to": "#f87171",
					// 		},
					// 		"secondaryColor": "#BBF7D0",
					// 		"warningColor": "#EFC030"
					// 	},
					// 	"launcher": {
					// 		"callToAction": "JOIN NIKE'S LOYALTY PROGRAM",
					// 		"styling": {
					// 			"textColor": "#ffffff",
					// 			"backgroundColor": "#000000",
					// 		}
					// 	},
					// 	"guest": {
					// 		"content": {
					// 			"title": "JOIN COMMUNITY",
					// 			"subtitle": "You receive rewards by completing ways to earn and making purchases. Collect XP to move across levels or spend it on extra rewards!",
					// 			"benefitsTitle": "Member Benefits",
					// 		}
					// 	},
					// 	"member": {
					// 		"content": {
					// 			"rewardsTitle": "Your Rewards",
					// 			"wteTitle": "Ways to Earn",
					// 			"rewardShopTitle": "Available Rewards",
					// 		}
					// 	}
					// },
          //   "dollarValuePerPoint": 0.01,
          //   "approximateCostBenefitToStore": "summary of cost/benefit impact to revenue / margins / etc goes here, use specific or approximate figures for the impacts to revenue/margins/etc",
          //   "waysToEarn": [{
          //     "conditions": [{
          //       "type":"dollar-spent",
          //       "label":"Each $ Spent",
          //       "subtitle":"Converts all purchases to points.",
          //       "variable": "purchaseTotalIncrease",
          //       "operator": "=",
          //       "amount": 1
          //     }],
          //     "rewards":[{
          //       "loyaltyRewardDefinition": {
          //         "grantable": true,
          //         "redeemable": false,
          //         "rewardCoupon": {
          //           "name": "Points",
          //           "amount": 100,
          //           "amountType": "points"
          //         }
          //       }
          //     }]
          //   },{
          //     "conditions": [{
          //       "type":"nth-purchase",
          //       "label":"NTH Purchase",
          //       "subtitle":"Customer Makes their [X] Purchase Within the last Y days/months",
          //       "variable": "purchaseCount",
          //       "operator": ">=",
          //       "amount": 10
          //     }, {
          //       "type":"nth-purchase",
          //       "label":"NTH Purchase",
          //       "subtitle":"Customer Makes their [X] Purchase Within the last Y days/months",
          //       "variable": "purchaseDateAgo",
          //       "operator": "<=",
          //       "amount": 30
          //     }],
          //     "rewards":[{
          //       "loyaltyRewardDefinition": {
          //         "grantable": true,
          //         "redeemable": false,
          //         "rewardCoupon": {
          //           "name": "30% off coupon",
          //           "amount": 30,
          //           "amountType": "percent-discount",
          //           "expiresInDays": 60,
          //           "maximumDiscount": 100
          //         },
          //         "maxUserGrants": 2
          //       }
          //     } ,{
          //       "points": 2000,
          //       "loyaltyRewardDefinition": {
          //         "grantable": true,
          //         "redeemable": false,
          //         "rewardCoupon": {
          //           "name": "$10 off coupon",
          //           "amount": 10,
          //           "amountType": "dollars-off-coupon",
          //           "minimumOrderTotal": 20,
          //           "expiresInDays": 90
          //         },
          //         "maxUserGrants": 2
          //       }
          //     }]
          //   }],
          //   "redeemableRewards": [
          //     {
          //       "points": 500,
          //       "loyaltyRewardDefinition": {
          //         "redeemable": true,
          //         "rewardCoupon": {
          //           "name": "5% off coupon",
          //           "amount": 5,
          //           "amountType": "percent-discount",
          //           "minimumOrderTotal": 10,
          //           "maximumDiscount": 50
          //         }
          //       }
          //     },
          //     {
          //       "points": 2000,
          //       "loyaltyRewardDefinition": {
          //         "redeemable": true,
          //         "rewardCoupon": {
          //           "name": "$10 off coupon",
          //           "amount": 10,
          //           "amountType": "dollars-off-coupon",
          //           "minimumOrderTotal": 50
          //         }
          //       }
          //     },
          //     {
          //       "points": 20000,
          //       "loyaltyRewardDefinition": {
          //         "redeemable": true,
          //         "rewardCoupon": {
          //           "name": "Free Coffee Variety pack with Mug",
          //           "amount": 54020340,
          //           "amountType": "free-product"
          //         }
          //       }
          //     }
  
          //   ]
          //   }
  

			// 	`
			// },
      {
        role: "system",
        content: `
          The messages following the questions are data scraped from an ecommerce website, ${event.url}. Please determine and answer the following questions:
          - If you were to create a fake loyalty program for this store, what branding text and colors should be used for this loyalty program?
            - The loyalty program experience will be displayed as a full-height overlay along the left or right side of the website, with a launcher button that opens the overlay.
            - The colors will be used within the overlay, and the launcher button.
              - Make sure the background color of the overlay contrasts with the webpage behind it
            - None of the colors used within the overlay should blend in with each other
              - For example the secondary color, accent colors, etc should not blend in with the chosen overlay background color
              - The button text color should not blend in with the button background color
              - etc
            - The launcher colors should stand out from the rest of the site, to draw attention to the loyalty program.
			- The guest heroImageSearchText should be a search term, no more than 3 words, that would return an image relevant to the brand or industry of the brand.
            - The warning color:
              - should usually be a yellowish, redish, or orangeish color
              - ideally should be a color that is used elsewhere on the site, but if not just choose the best yellow, red, or orange color to use against the branding background
          
				  Format the answer in its entirety as valid JSON (no leading or trailing text around the JSON, no triple backtick delimiters/etc around the JSON, no trailing commas after any last array items or object properties, no comments, correctly escaped string values, etc), with the following schema:

          {
            "logoUrl": "",
            "colors": {
              "backgroundColor": "#000000",
              "useBackgroundColor": true,
              "textColor": "#ffffff",
              "buttonTextColor": "#000000",
              "buttonBackgroundColor": "#ffffff",
              "linkColor": "#aaaaaa",
              "accentColor": {
                "from": "#f0abfc",
                "to": "#f87171",
              },
              "secondaryColor": "#BBF7D0",
              "warningColor": "#EFC030"
            },
            "launcher": {
              "callToAction": "JOIN NIKE'S LOYALTY PROGRAM",
              "styling": {
                "textColor": "#ffffff",
                "backgroundColor": "#000000",
              }
            },
            "guest": {
              "content": {
                "title": "JOIN COMMUNITY",
                "subtitle": "You receive rewards by completing ways to earn and making purchases. Collect XP to move across levels or spend it on extra rewards!",
                "benefitsTitle": "Member Benefits",
              },
			  "heroImageSearchText": "nike logo"
            },
            "member": {
              "content": {
                "rewardsTitle": "Your Rewards",
                "wteTitle": "Ways to Earn",
                "rewardShopTitle": "Available Rewards",
              }
            }
          }
        `
      },
      event.scrapeResults.web.faviconUrl 
        ? { "role": "user", "content": `The path to the website's logo is: ${event.scrapeResults.web.faviconUrl}` } 
        : { "role": "user", "content": "The website does not have a logo." },
      {
        "role": "user",
        "content": [
          {
            "type": "text",
            "text": "here is a screenshot of the website:"
          },
          {
            "type": "image_url",
            "image_url": {
              url: event.scrapeResults.web.screenshotUrl
            }
          }
        ]
      }
    ],
    model: event.model || "gpt-4o-2024-08-06",
    max_tokens: event.max_tokens || 4096,
    // function_call: { name: 'createBranding' },
    // functions: [
    //   {
    //     name: 'createBranding',
    //     properties: {
    //       json: { type: 'string' },
    //     },
    //     required: ['json'],
    //     additionalProperties: false
    //   }
    // ],n
    // functions: [
    //   {
    //     name: "createLoyaltyProgram",
    //     description: "Create a loyalty program complete with branding and ways to earn and redeem rewards.",
    //     parameters: {
    //       type: "object",
    //       properties: {
    //         // branding: {
    //         //   type: 'object',
    //         //   properties: {
    //         //     colors: {
    //         //       type: 'object',
    //         //       properties: {
    //         //         backgroundColor: { type: 'string' },
    //         //         textColor: { type: 'string' },
    //         //         buttonTextColor: { type: 'string' },
    //         //         buttonBackgroundColor: { type: 'string' },
    //         //         linkColor: { type: 'string' },
    //         //         accentColor: {
    //         //           type: 'object',
    //         //           properties: {
    //         //             from: { type: 'string' },
    //         //             to: { type: 'string' },
    //         //           }
    //         //         },
    //         //         secondaryColor: { type: 'string' },
    //         //         warningColor: { type: 'string' }
    //         //       }
    //         //     },
    //         //     launcher: {
    //         //       properties: {
    //         //         callToAction: { type: 'string' },
    //         //         styling: {
    //         //           type: 'object',
    //         //           properties: {
    //         //             textColor: { type: 'string' },
    //         //             backgroundColor: { type: 'string' },
    //         //           }
    //         //         }
    //         //       }
    //         //     },
    //         //     guest: {
    //         //       type: 'object',
    //         //       properties: {
    //         //         content: {
    //         //           type: 'object',
    //         //           properties: {
    //         //             title: { type: 'string' },
    //         //             subtitle: { type: 'string' },
    //         //             benefitsTitle: { type: 'string' },
    //         //           }
    //         //         }
    //         //       }
    //         //     },
    //         //     member: {
    //         //       type: 'object',
    //         //       properties: {
    //         //         content: {
    //         //           type: 'object',
    //         //           properties: {
    //         //             rewardsTitle: { type: 'string' },
    //         //             wteTitle: { type: 'string' },
    //         //             rewardShopTitle: { type: 'string' },
    //         //           }
    //         //         }
    //         //       }
    //         //     }
    //         //   }
    //         // },
    //         waysToEarn: {
    //           type: "array",
    //           items: {
    //             type: "object",
    //             properties: {
    //               conditions: {
    //                 type: "array",
    //                 items: {
    //                   type: "object",
    //                   properties: {
    //                   type: { type: "string", enum: ["nth-purchase"] },
    //                   label: { type: "string" },
    //                   subtitle: { type: "string" },
    //                   variable: { type: "string", enum: ["purchaseCount", "purchaseDateAgo"] },
    //                   operator: { type: "string", enum: [">=", "<="] },
    //                   amount: { type: "integer" }
    //                   },
    //                   required: ["type", "label", "subtitle", "variable", "operator", "amount"],
    //                   additionalProperties: false
    //                 }
    //               },
    //               rewards: {
    //                 type: "array",
    //                 items: {
    //                   type: "object",
    //                   properties: {
    //                   points: { type: "integer", minimum: 0 },
    //                   loyaltyRewardDefinition: {
    //                     type: "object",
    //                     properties: {
    //                     grantable: { type: "boolean" },
    //                     redeemable: { type: "boolean" },
    //                     rewardCoupon: {
    //                       type: "object",
    //                       properties: {
    //                       name: { type: "string" },
    //                       amount: { type: "number" },
    //                       amountType: { type: "string", enum: ["percent-discount", "dollars-off-coupon"] },
    //                       expiresInDays: { type: "integer" },
    //                       maximumDiscount: { type: "number", minimum: 0 },
    //                       minimumOrderTotal: { type: "number", minimum: 0 }
    //                       },
    //                       required: ["name", "amount", "amountType", "expiresInDays"],
    //                       additionalProperties: false
    //                     },
    //                     maxUserGrants: { type: "integer" }
    //                     },
    //                     required: ["grantable", "redeemable", "rewardCoupon", "maxUserGrants"],
    //                     additionalProperties: false
    //                   }
    //                   },
    //                   additionalProperties: false
    //                 }
    //               }
    //             }
    //           }
    //         }
    //       },
    //       required: [
    //         // "branding", 
    //         "waysToEarn"
    //       ],
    //       additionalProperties: false

    //     }

      // }
    // ]
  };

  return {
    ...event,
    prompt
  } 
} 
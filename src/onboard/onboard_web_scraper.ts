import puppeteer from "puppeteer-core";
const chromium = require("@sparticuz/chromium");
import { S3, waitForObjectExists } from '@aws-sdk/client-s3';
import { randomUUID } from "crypto";
import fetch from 'node-fetch';

export const handler = async (
  event: any = {},
  context: any = {}
): Promise<any> => {
  try {
    return await scrape(event, context);
  } catch (e: any) {
    console.error(e);
    console.error('Retrying with alternate waiting strategy');

    return await scrape(event, context, true);
  }
}

const scrape = async function(event: any, context: any, usePuppeteerWaits = false) {

  const storePassword = event.storePassword;

  const browser = await puppeteer.launch({
    executablePath: await chromium.executablePath(),
    headless: chromium.headless,
    ignoreHTTPSErrors: true,
    defaultViewport: chromium.defaultViewport,
    args: [...chromium.args, "--hide-scrollbars", "--disable-web-security"],
  });
  const page = await browser.newPage();

  // Set a custom user-agent
  await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
  // Set additional 
  await page.setExtraHTTPHeaders({
    'Accept-Language': 'en-US,en;q=0.9',
    'Referer': 'https://www.google.com/'
  });

  const defaultUrl = "https://developers.google.com/web/";

  let status;
  try {
    status = await page.goto(event.url || defaultUrl, { waitUntil: event.waitUntil || 'domcontentloaded', timeout: 0 });
    const code = status?.status() || 0;
    if (code > 399 || code < 200) {
      throw new Error(`HTTP Error: ${code}`);
    }
  } catch (error: any) {
    console.error(`Failed to load the page: ${error.message}`);
    console.error('Navigating to the default page instead');
    status = await page.goto(defaultUrl, { waitUntil: 'domcontentloaded', timeout: 0 });
  }

  console.log('Page went to');

  if (!usePuppeteerWaits) {
    const passwordPrompt = await page.$('form[action="/password"] input.form-input#password[type="password"][autocomplete="nope"]');
    if (passwordPrompt && storePassword) {
      await page.type('#password', storePassword);
      await page.click('button[type=submit]');

      await page.waitForNavigation({ waitUntil: event.waitUntil || 'domcontentloaded', timeout: 0 }); 
    }
    console.log('Page password checked');

    await waitForLoad(page);    
    
    await new Promise(r => setTimeout(r, event.extraTimeout || 10 * 1000)),

    console.log('Page loaded (via document events)');
  } else {
    console.log('Skipping password check and load event');

    await new Promise(r => setTimeout(r, event.extraTimeout || 15 * 1000)),

    console.log('Waiting for Puppeteer nav/idle');

    await Promise.race([
      new Promise(r => setTimeout(r, event.extraTimeout || 15 * 1000)),
      page.waitForNetworkIdle().catch(),
      page.waitForNavigation().catch()
    ]);

    console.log('Page loaded/idle (via Puppeteer awaits)');
  }

  // const self = globalThis;

  // await page.evaluate(function () {
  //     document.querySelectorAll("img,video").forEach((x: any) => x.remove());
  //     document.querySelectorAll("*").forEach((x: any) => x.style.backgroundImage = "none" );
  // });

  console.log('Scraping text/screenshot');
  
  const [text, html, links, screenshot, faviconUrl] = await Promise.all([
    page.evaluate(function () {
        return document.body.innerText.slice(0, 200000);
    }),
    page.evaluate(function () {
      return document.body.innerHTML;
    }),
    page.evaluate(function () {
      return Array.from(document.querySelectorAll('a[href^=http]')).map((x: any) => x.href);
    }),
    page.screenshot({ fullPage: true }),
    findBestFaviconURL(page, event.url)
  ]);

  console.log('scraping complete');
  
  await browser.close();

  console.log('saving to S3');

  const s3 = new S3();
  const bucket = process.env.OUTPUT_BUCKET;
  const key = `${event.organization_id}`;
  const params = { Bucket: bucket, Key: key, Body: screenshot };
  await s3.putObject(params);

  const truncatedLinks = [];
  let totalLength = 0;

  for (let link of links) {
    let linkLength = link.length;
    if (totalLength + linkLength < 50000) {
      truncatedLinks.push(link);
      totalLength += linkLength;
    } else {
      break;
    }
  }

  return {
    text,
    // html,
    links: truncatedLinks,
    screenshotUrl: `https://${bucket}.s3.amazonaws.com/${key}`,
    faviconUrl
  };
}

const findBestFaviconURL = async function(page: any, pageUrl: any) {
  try {
    const rootUrl = (new URL(pageUrl)).protocol + "//" + (new URL(pageUrl)).host; 
    const selectorsToTry = [
        `link[rel="icon"]`, 
        `link[rel="shortcut icon"]`
    ];

    let faviconUrlFromDocument = null;
    for(let i=0; i<selectorsToTry.length; i++) {
        const href = await getDOMElementHRef(page, selectorsToTry[i]);
        if(typeof href === 'undefined' || href === null || href.length === 0) {
            continue;
        }

        faviconUrlFromDocument = href;
        break;
    }

    if(faviconUrlFromDocument === null) {
        // No favicon link found in document, best URL is likley favicon.ico at root
        const url = rootUrl + "/favicon.ico";

        try {
          const response = await fetch(url, {method: 'HEAD'});
          if (response.ok) {
              return url;
          }
        } catch (e) {
          console.warn('Favicon request failed', e);
        }

        return 'https://d3q4ufbgs1i4ak.cloudfront.net/raleon_icon.png';
    }

    if(faviconUrlFromDocument.substr(0, 4) === "http" || faviconUrlFromDocument.substr(0, 2) === "//") {
        // absolute url
        return faviconUrlFromDocument;
    } else if(faviconUrlFromDocument.substr(0, 1) === '/') {
        // favicon relative to root
        return (rootUrl + faviconUrlFromDocument);
    } else {
        // favicon relative to current (pageUrl) URL
        return (pageUrl + "/" + faviconUrlFromDocument);
    }
  } catch (e) {
    console.warn('Error finding favicon', e);
    return 'https://d3q4ufbgs1i4ak.cloudfront.net/raleon_icon.png';
  }
};

const getDOMElementHRef = async function(page: any, query: any) {
  return await page.evaluate((q) => {
      const elem = document.querySelector(q);
      if(elem) {
          return (elem.getAttribute('href') || '');
      } else {
          return "";
      }
  }, query);
};


async function waitForWindowEvent(page: any, eventName: string, timeout: any = 10*1000) {

  // use race to implement a timeout
  return Promise.race([

      // add event listener and wait for event to fire before returning
      page.evaluate(function(eventName: string) {
          return new Promise<void>(function(resolve, reject) {
              window.addEventListener(eventName, function(e) {
                  resolve(); // resolves when the event fires
              });
          });
      }, eventName),

      // if the event does not fire within n seconds, exit
      page.waitForTimeout(timeout)
  ]);
}

async function waitForDocumentEvent(page: any, eventName: string, timeout: any = 10*1000) {

  // use race to implement a timeout
  return Promise.race([

      // add event listener and wait for event to fire before returning
      page.evaluate(function(eventName: string) {
          return new Promise<void>(function(resolve, reject) {
              document.addEventListener(eventName, function(e) {
                  resolve(); // resolves when the event fires
              });
          });
      }, eventName),

      // if the event does not fire within n seconds, exit
      page.waitForTimeout(timeout)
  ]);
}

async function waitForLoad(page: any, timeout = 10*1000) {
  const readyState = await page.evaluate(function() { return document.readyState });
  if (readyState === 'complete') {
    return;
  }

  return waitForWindowEvent(page, 'load', timeout);
}
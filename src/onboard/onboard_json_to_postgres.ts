type PostgresDataType =
  | 'bigint'
  | 'bigserial'
  | 'bit'
  | 'bit varying'
  | 'boolean'
  | 'box'
  | 'bpchar'
  | 'bytea'
  | 'char'
  | 'character'
  | 'character varying'
  | 'cidr'
  | 'circle'
  | 'date'
  | 'decimal'
  | 'double precision'
  | 'inet'
  | 'integer'
  | 'interval'
  | 'json'
  | 'jsonb'
  | 'line'
  | 'lseg'
  | 'macaddr'
  | 'money'
  | 'numeric'
  | 'path'
  | 'point'
  | 'polygon'
  | 'real'
  | 'smallint'
  | 'smallserial'
  | 'serial'
  | 'text'
  | 'time'
  | 'time without time zone'
  | 'time with time zone'
  | 'timestamp'
  | 'timestamp without time zone'
  | 'timestamp with time zone'
  | 'tsquery'
  | 'tsvector'
  | 'txid_snapshot'
  | 'varchar';

interface PostgresColumnSchema {
  table_name: string,
  column_name: string,
  data_type: PostgresDataType,
  column_default: any,
  is_nullable: 'YES' | 'NO';
}

export const handler = async (
  event: any = {},
  context: any = {}
): Promise<any> => {
  const json = typeof event.json === 'string' ? JSON.parse(event.json) : event.json;
  const topLevelTableName = event.topLevelTableName || 'loyaltycampaign';
  let value = event.jsonKey
    ? typeof json[event.jsonKey] === 'string' ? JSON.parse(json[event.jsonKey]) : json[event.jsonKey]
    : json;

  const dbFields = event.dbFields || [];

  console.log(event, value);

  keysToLowerCase(event.jsonOverrides, true);
  keysToLowerCase(value, true);

  if (event.jsonOverrides) {
    value = applyJsonOverrides(value, event.jsonOverrides);
  }

  return generateInsertQuery(
    value,
    topLevelTableName,
    dbFields?.Payload ? dbFields.Payload : dbFields
  );
}

type JsonValue = string | number | boolean | JsonStructure | JsonStructure[];
interface JsonStructure {
  [key: string]: JsonValue;
}

function generateInsertQuery(data: any, topLevelTable: string, dbFields: Array<PostgresColumnSchema>): string {
  let index = 0;
  let ids: Map<string, string> = new Map();

  const generateOneToOneInsert = (data: JsonStructure, tableName: string): string => {
    let columns: string[] = [];
    let values: string[] = [];
    let preClauses = [];
    const relevantFields = dbFields.filter(x => x.table_name.toLowerCase().trim() == tableName.toLowerCase().trim());

    for (const [key, value] of Object.entries(data || {})) {
      if (key.trim().includes(' ')) {
        continue;
      }

      if (value && typeof value === 'object' && !Array.isArray(value)) {
        if (!Object.keys(value).length) {
          continue;
        }

        if (key.startsWith('_121_')) {
          // One-to-one relation, insert key value first
          const keyTable = key.substring(5);
          const reference = `${keyTable}id`;
          if (dbFields.length && !relevantFields.some(x => x.column_name.toLowerCase().trim() == reference.toLowerCase().trim())) {
            continue;
          }

          preClauses.push(generateOneToOneInsert(value, keyTable));
          columns.push(reference);
          values.push(`(SELECT id FROM ${keyTable}_${index})`);
        } else {
          throw new Error('Nested relationships on one-to-ones must be one-to-ones');
        }
      } else if (Array.isArray(value)) {
        throw new Error('Nested relationships on one-to-ones must be one-to-ones');
      } else if (value || value === 0 || value === false || value === '') {
        // It's a primitive value, treat as a column
        if (key.startsWith('_121_')) {
          continue;
        }

        if (dbFields.length && !relevantFields.some(x => x.column_name.toLowerCase().trim() == key.toLowerCase().trim())) {
          continue;
        }
        if (typeof value == 'string' && value.startsWith('@')) {
          const valueTable = value.substring(1);
          if (!ids.has(valueTable.toLowerCase())) {
            console.log(ids);
            throw new Error(`Table ${valueTable} has not been inserted yet`);
          }
          values.push(`(SELECT id FROM ${ids.get(valueTable.toLowerCase())})`);
          columns.push(key);
          continue;
        }

        columns.push(key);
        switch (typeof value) {
          case 'string':
            values.push(`'${value.replace(/'/g, "''")}'`);
            break;
          case 'number':
            values.push(`${value}`);
            break;
          case 'boolean':
            values.push(`${value}`);
            break;
          default:
            throw new Error(`Unknown primitive type ${typeof value}`);
        }
      }
    }

    backfillRequiredFields(tableName, columns, values, dbFields);
    ids.set(tableName.toLowerCase(), `${tableName}_${index}`);
    preClauses.push(`${tableName}_${++index} AS (INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${values.join(', ')}) RETURNING id)`);
    return preClauses.join(',');// `${tableName}_${++index} AS (INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${values.join(', ')} RETURNING id)`;

    // return `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${values.join(', ')})`;
  }

  const generateWithClause = (data: JsonStructure, tableName: string, parentId: string | null): string => {
    let withClauses: string[] = [];
    let columns: string[] = [];
    let values: string[] = [];

    const selfid = `${tableName}_${index++}`;
    ids.set(tableName.toLowerCase(), selfid);

    let preClauses = [];

    const relevantFields = dbFields.filter(x => x.table_name.toLowerCase().trim() == tableName.toLowerCase().trim());
    for (const [key, value] of Object.entries(data || {})) {
      if (value && typeof value === 'object' && !Array.isArray(value)) {
        if (!Object.keys(value).length) {
          continue;
        }

        if (key.startsWith('_121_')) {
          // One-to-one relation, insert key value first
          const keyTable = key.substring(5);
          const reference = `${keyTable}id`;

          if (dbFields.length && !relevantFields.some(x => x.column_name.toLowerCase().trim() == reference.toLowerCase().trim())) {
            continue;
          }

          // preClauses.push(generateWithClause(value as JsonStructure, key.substring(1), selfid));
          // preClauses.push(`${keyTable}_${++index} AS (${generateOneToOneInsert(value, keyTable)} RETURNING id)`);
          preClauses.push(generateOneToOneInsert(value, keyTable));
          columns.push(reference);
          values.push(`(SELECT id FROM ${keyTable}_${index})`);
        } else {
          // It's a nested object, treat as a child table
          withClauses.push(generateWithClause(value as JsonStructure, key, selfid));
        }
      } else if (Array.isArray(value)) {
        // It's an array of nested objects, treat each as a row in a child table
        value.forEach((childData) => {
          withClauses.push(generateWithClause(childData as JsonStructure, key, selfid));
        });
      } else if (value || value === 0 || value === false || value === '') {
        // It's a primitive value, treat as a column
        if (key.startsWith('_121_')) {
          continue;
        }

        if (dbFields.length && !relevantFields.some(x => x.column_name.toLowerCase().trim() == key.toLowerCase().trim())) {
          continue;
        }

        if (typeof value == 'string' && value.startsWith('@')) {
          const valueTable = value.substring(1);
          if (!ids.has(valueTable.toLowerCase())) {
            throw new Error('Table has not been inserted yet');
          }
          values.push(`(SELECT id FROM ${ids.get(valueTable.toLowerCase())})`);
          columns.push(key);
          continue;
        }

        columns.push(key);
        switch (typeof value) {
          case 'string':
            values.push(`'${value.replace(/'/g, "''")}'`);
            break;
          case 'number':
            values.push(`${value}`);
            break;
          case 'boolean':
            values.push(`${value}`);
            break;
          default:
            throw new Error(`Unknown primitive type ${typeof value}`);
        }
      }
    }

    if (parentId) {
      columns.push(`${parentId.split('_')[0]}id`);
      values.push(`(SELECT id FROM ${parentId})`);
    }

    backfillRequiredFields(tableName, columns, values, dbFields);

    let withClause = `${selfid} AS (INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${values.join(', ')}) RETURNING id)`;
    withClauses.unshift(withClause);
    withClauses.unshift(...preClauses);


    // debugger;

    return withClauses.join(', ');
  };

  if (Array.isArray(data)) {
    // If data is an array, generate insert queries for each item
    return data.map(singleData => {
      const startIndex = index;
      return `WITH ${generateWithClause(singleData, topLevelTable, null)} SELECT * FROM ${topLevelTable}_${startIndex} `
    }).join('; ');
  } else {
    // Handle the single object case
    return `WITH ${generateWithClause(data, topLevelTable, null)} SELECT * FROM ${topLevelTable}_0 `;
  }
}


function applyJsonOverrides(data: any, overrides: any): any {
  if (typeof data !== 'object' || data === null || overrides === null || typeof overrides !== 'object') {
    return data;
  }

  for (const property in data) {
    if (overrides.hasOwnProperty(property)) {
      if (Array.isArray(data[property] && Array.isArray(overrides[property]))) {
        for (let i = 0; i < data[property].length; i++) {
          const override = overrides[property].length === 1
            ? overrides[property][0]
            : overrides[property][i];

          if (!override) {
            continue;
          }

          if (typeof data[property][i] === 'object' && typeof override === 'object') {
            data[property][i] = applyJsonOverrides(data[property][i], override);
          } else {
            data[property][i] = override;
          }
        }
      } else if (typeof data[property] === 'object' && typeof overrides[property] === 'object') {
        data[property] = applyJsonOverrides(data[property], overrides[property]);
      } else {
        data[property] = overrides[property];
      }
    }
  }

  for (const property in overrides) {
    if (!data.hasOwnProperty(property)) {
      if (typeof data[property] === 'object' && typeof overrides[property] === 'object') {
        data[property] = applyJsonOverrides({}, overrides[property]);
      } else {
        data[property] = overrides[property];
      }
    }
  }

  return data;
}

function backfillRequiredFields(tableName: string, columns: string[], values: any[], dbFields: Array<PostgresColumnSchema>) {
  if (!dbFields.length) {
    return;
  }

  const requiredFields = dbFields.filter(x => x.table_name === tableName.trim().toLowerCase() && !x.column_default && x.is_nullable !== "YES");
  for (const requiredField of requiredFields) {
    if (!columns.find(x => x.toLowerCase().trim() == requiredField.column_name.toLowerCase().trim())) {
      columns.push(requiredField.column_name);
      switch (requiredField.data_type) {
        case 'text':
        case 'bpchar':
        case 'varchar':
        case 'char':
        case 'character':
        case 'character varying':
          values.push(`''`);
          break;
        case 'numeric':
        case 'smallint':
        case 'integer':
        case 'bigint':
          values.push('0');
          break;
        case 'decimal':
        case 'real':
        case 'double precision':
          values.push('0.0');
          break;
        case 'boolean':
          console.warn(`Encountred required boolean column ${tableName}.${requiredField.column_name} with no value, defaulting to false`);
          values.push('false');
          break;
        case 'date':
          values.push("'1970-01-01'");
          break;
        case 'time':
        case 'time with time zone':
        case 'time without time zone':
          values.push("'00:00:00+00:00'");
          break;
        case 'timestamp':
        case 'timestamp without time zone':
        case 'timestamp with time zone':
          values.push("'1970-01-01 00:00:00 +0:00'")
          break;
        default:
          console.warn(`Encountred required column ${tableName}.${requiredField.column_name} with unhandled data type: ${requiredField.data_type}, defaulting to ''`);
          values.push(`''`);
      }
    }

  }
}

function keysToLowerCase(obj: any, inPlace: boolean = false): any {
  if (typeof obj !== 'object' || obj === null) {
    return obj;
  }

  let result = inPlace ? obj : Array.isArray(obj) ? [] : {};

  if (Array.isArray(obj)) {
    for (let i = 0; i < obj.length; i++) {
      result[i] = keysToLowerCase(obj[i], inPlace);
    }
  } else {
    const keys = Object.keys(obj);
    for (const key of keys) {
      const lowerKey = key.toLowerCase();
      if (key !== lowerKey) {
        result[lowerKey] = keysToLowerCase(obj[key], inPlace);
        if (inPlace) {
          delete obj[key];
        }
      } else {
        result[key] = keysToLowerCase(obj[key], inPlace);
      }
    }
  }

  return result;
}
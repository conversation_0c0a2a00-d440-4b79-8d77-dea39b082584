const OpenAI = require('openai');
const openai = new OpenAI({ apiKey: '***************************************************' });

const json = {
  "Comment": "A description of my state machine",
  "StartAt": "Pass (1)",
  "States": {
    "Pass (1)": {
      "Type": "Pass",
      "Next": "Onboarding - Parallel Processes",
      "Parameters": {
        "url.$": "$.url",
        "organization_id.$": "$.organization_id",
        "metrics": [],
        "useGptVision": true,
        "skipShopify.$": "$.skipShopify",
        "skipProgram.$": "$.skipProgram"
      }
    },
    "Onboarding - Parallel Processes": {
      "Type": "Parallel",
      "Branches": [
        {
          "StartAt": "Branding - Program Dependency",
          "States": {
            "Branding - Program Dependency": {
              "Type": "Parallel",
              "Branches": [
                {
                  "StartAt": "Branding - Status - Mark Start",
                  "States": {
                    "Branding - Status - Mark Start": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::lambda:invoke",
                      "Parameters": {
                        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-livemac05:$LATEST",
                        "Payload": {
                          "query": "INSERT INTO onboardingstate(state,timestamp,orgid,taskid) VALUES ($1,$2,$3,$4) ON CONFLICT ON CONSTRAINT unique_orgid_taskid DO UPDATE SET state=$1,timestamp=$2",
                          "values": {
                            "0": "\"Started\"",
                            "1": "NOW()",
                            "3": "1",
                            "2.$": "$.organization_id"
                          }
                        }
                      },
                      "Retry": [
                        {
                          "ErrorEquals": [
                            "Lambda.ServiceException",
                            "Lambda.AWSLambdaException",
                            "Lambda.SdkClientException",
                            "Lambda.TooManyRequestsException"
                          ],
                          "IntervalSeconds": 1,
                          "MaxAttempts": 3,
                          "BackoffRate": 2
                        }
                      ],
                      "Next": "Branding - Web Scrape (text, screenshot)",
                      "ResultPath": null
                    },
                    "Branding - Web Scrape (text, screenshot)": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::lambda:invoke",
                      "Parameters": {
                        "Payload.$": "$",
                        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-web-scraper-livemac05:$LATEST"
                      },
                      "Retry": [
                        {
                          "ErrorEquals": [
                            "Lambda.ServiceException",
                            "Lambda.AWSLambdaException",
                            "Lambda.SdkClientException",
                            "Lambda.TooManyRequestsException"
                          ],
                          "IntervalSeconds": 1,
                          "MaxAttempts": 3,
                          "BackoffRate": 2
                        }
                      ],
                      "ResultPath": "$.scrape",
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "Choice",
                  "States": {
                    "Choice": {
                      "Type": "Choice",
                      "Choices": [
                        {
                          "Variable": "$.skipShopify",
                          "StringEquals": "true",
                          "Next": "Pass"
                        }
                      ],
                      "Default": "Program - Status - Mark Start"
                    },
                    "Pass": {
                      "Type": "Pass",
                      "End": true,
                      "Result": {
                        "data": {
                          "products": {
                            "edges": [
                              {
                                "node": {
                                  "id": "gid://shopify/Product/8438965600546",
                                  "createdAt": "2023-08-01T14:02:25Z",
                                  "updatedAt": "2023-08-25T13:35:28Z",
                                  "title": "The Multi-location Snowboard",
                                  "handle": "the-multi-location-snowboard",
                                  "productType": "",
                                  "priceRangeV2": {
                                    "maxVariantPrice": {
                                      "amount": "729.95",
                                      "currencyCode": "USD"
                                    },
                                    "minVariantPrice": {
                                      "amount": "729.95",
                                      "currencyCode": "USD"
                                    }
                                  },
                                  "description": "",
                                  "totalInventory": 95,
                                  "status": "ACTIVE"
                                }
                              },
                              {
                                "node": {
                                  "id": "gid://shopify/Product/8438965797154",
                                  "createdAt": "2023-08-01T14:02:28Z",
                                  "updatedAt": "2023-08-23T20:49:20Z",
                                  "title": "The Collection Snowboard: Liquid",
                                  "handle": "the-collection-snowboard-liquid",
                                  "productType": "",
                                  "priceRangeV2": {
                                    "maxVariantPrice": {
                                      "amount": "749.95",
                                      "currencyCode": "USD"
                                    },
                                    "minVariantPrice": {
                                      "amount": "749.95",
                                      "currencyCode": "USD"
                                    }
                                  },
                                  "description": "",
                                  "totalInventory": 49,
                                  "status": "ACTIVE"
                                }
                              },
                              {
                                "node": {
                                  "id": "gid://shopify/Product/8438965305634",
                                  "createdAt": "2023-08-01T14:02:24Z",
                                  "updatedAt": "2023-08-23T20:49:20Z",
                                  "title": "The Complete Snowboard",
                                  "handle": "the-complete-snowboard",
                                  "productType": "snowboard",
                                  "priceRangeV2": {
                                    "maxVariantPrice": {
                                      "amount": "699.95",
                                      "currencyCode": "USD"
                                    },
                                    "minVariantPrice": {
                                      "amount": "699.95",
                                      "currencyCode": "USD"
                                    }
                                  },
                                  "description": "This PREMIUM snowboard is so SUPERDUPER awesome!",
                                  "totalInventory": 34,
                                  "status": "ACTIVE"
                                }
                              },
                              {
                                "node": {
                                  "id": "gid://shopify/Product/8438965731618",
                                  "createdAt": "2023-08-01T14:02:27Z",
                                  "updatedAt": "2023-08-23T20:49:16Z",
                                  "title": "Selling Plans Ski Wax",
                                  "handle": "selling-plans-ski-wax",
                                  "productType": "",
                                  "priceRangeV2": {
                                    "maxVariantPrice": {
                                      "amount": "49.95",
                                      "currencyCode": "USD"
                                    },
                                    "minVariantPrice": {
                                      "amount": "9.95",
                                      "currencyCode": "USD"
                                    }
                                  },
                                  "description": "",
                                  "totalInventory": 29,
                                  "status": "ACTIVE"
                                }
                              },
                              {
                                "node": {
                                  "id": "gid://shopify/Product/8438965666082",
                                  "createdAt": "2023-08-01T14:02:25Z",
                                  "updatedAt": "2023-08-01T14:05:19Z",
                                  "title": "The 3p Fulfilled Snowboard",
                                  "handle": "the-3p-fulfilled-snowboard",
                                  "productType": "",
                                  "priceRangeV2": {
                                    "maxVariantPrice": {
                                      "amount": "2629.95",
                                      "currencyCode": "USD"
                                    },
                                    "minVariantPrice": {
                                      "amount": "2629.95",
                                      "currencyCode": "USD"
                                    }
                                  },
                                  "description": "",
                                  "totalInventory": 19,
                                  "status": "ACTIVE"
                                }
                              },
                              {
                                "node": {
                                  "id": "gid://shopify/Product/8438965207330",
                                  "createdAt": "2023-08-01T14:02:23Z",
                                  "updatedAt": "2023-08-01T14:05:19Z",
                                  "title": "The Minimal Snowboard",
                                  "handle": "the-minimal-snowboard",
                                  "productType": "",
                                  "priceRangeV2": {
                                    "maxVariantPrice": {
                                      "amount": "885.95",
                                      "currencyCode": "USD"
                                    },
                                    "minVariantPrice": {
                                      "amount": "885.95",
                                      "currencyCode": "USD"
                                    }
                                  },
                                  "description": "",
                                  "totalInventory": 39,
                                  "status": "ACTIVE"
                                }
                              },
                              {
                                "node": {
                                  "id": "gid://shopify/Product/8438965633314",
                                  "createdAt": "2023-08-01T14:02:25Z",
                                  "updatedAt": "2023-08-01T14:02:48Z",
                                  "title": "The Multi-managed Snowboard",
                                  "handle": "the-multi-managed-snowboard",
                                  "productType": "",
                                  "priceRangeV2": {
                                    "maxVariantPrice": {
                                      "amount": "629.95",
                                      "currencyCode": "USD"
                                    },
                                    "minVariantPrice": {
                                      "amount": "629.95",
                                      "currencyCode": "USD"
                                    }
                                  },
                                  "description": "",
                                  "totalInventory": 100,
                                  "status": "ACTIVE"
                                }
                              },
                              {
                                "node": {
                                  "id": "gid://shopify/Product/8438965698850",
                                  "createdAt": "2023-08-01T14:02:26Z",
                                  "updatedAt": "2023-08-01T14:02:44Z",
                                  "title": "The Collection Snowboard: Oxygen",
                                  "handle": "the-collection-snowboard-oxygen",
                                  "productType": "",
                                  "priceRangeV2": {
                                    "maxVariantPrice": {
                                      "amount": "1025.0",
                                      "currencyCode": "USD"
                                    },
                                    "minVariantPrice": {
                                      "amount": "1025.0",
                                      "currencyCode": "USD"
                                    }
                                  },
                                  "description": "",
                                  "totalInventory": 50,
                                  "status": "ACTIVE"
                                }
                              },
                              {
                                "node": {
                                  "id": "gid://shopify/Product/8438965436706",
                                  "createdAt": "2023-08-01T14:02:24Z",
                                  "updatedAt": "2023-08-01T14:02:35Z",
                                  "title": "The Archived Snowboard",
                                  "handle": "the-archived-snowboard",
                                  "productType": "",
                                  "priceRangeV2": {
                                    "maxVariantPrice": {
                                      "amount": "629.95",
                                      "currencyCode": "USD"
                                    },
                                    "minVariantPrice": {
                                      "amount": "629.95",
                                      "currencyCode": "USD"
                                    }
                                  },
                                  "description": "",
                                  "totalInventory": 50,
                                  "status": "ARCHIVED"
                                }
                              },
                              {
                                "node": {
                                  "id": "gid://shopify/Product/8438965272866",
                                  "createdAt": "2023-08-01T14:02:24Z",
                                  "updatedAt": "2023-08-01T14:02:35Z",
                                  "title": "The Hidden Snowboard",
                                  "handle": "the-hidden-snowboard",
                                  "productType": "",
                                  "priceRangeV2": {
                                    "maxVariantPrice": {
                                      "amount": "749.95",
                                      "currencyCode": "USD"
                                    },
                                    "minVariantPrice": {
                                      "amount": "749.95",
                                      "currencyCode": "USD"
                                    }
                                  },
                                  "description": "",
                                  "totalInventory": 50,
                                  "status": "ACTIVE"
                                }
                              },
                              {
                                "node": {
                                  "id": "gid://shopify/Product/8438965469474",
                                  "createdAt": "2023-08-01T14:02:24Z",
                                  "updatedAt": "2023-08-01T14:02:32Z",
                                  "title": "The Collection Snowboard: Hydrogen",
                                  "handle": "the-collection-snowboard-hydrogen",
                                  "productType": "",
                                  "priceRangeV2": {
                                    "maxVariantPrice": {
                                      "amount": "600.0",
                                      "currencyCode": "USD"
                                    },
                                    "minVariantPrice": {
                                      "amount": "600.0",
                                      "currencyCode": "USD"
                                    }
                                  },
                                  "description": "",
                                  "totalInventory": 50,
                                  "status": "ACTIVE"
                                }
                              },
                              {
                                "node": {
                                  "id": "gid://shopify/Product/8438965567778",
                                  "createdAt": "2023-08-01T14:02:24Z",
                                  "updatedAt": "2023-08-01T14:02:27Z",
                                  "title": "The Compare at Price Snowboard",
                                  "handle": "the-compare-at-price-snowboard",
                                  "productType": "",
                                  "priceRangeV2": {
                                    "maxVariantPrice": {
                                      "amount": "785.95",
                                      "currencyCode": "USD"
                                    },
                                    "minVariantPrice": {
                                      "amount": "785.95",
                                      "currencyCode": "USD"
                                    }
                                  },
                                  "description": "",
                                  "totalInventory": 10,
                                  "status": "ACTIVE"
                                }
                              },
                              {
                                "node": {
                                  "id": "gid://shopify/Product/8438965403938",
                                  "createdAt": "2023-08-01T14:02:24Z",
                                  "updatedAt": "2023-08-01T14:02:27Z",
                                  "title": "The Draft Snowboard",
                                  "handle": "the-draft-snowboard",
                                  "productType": "",
                                  "priceRangeV2": {
                                    "maxVariantPrice": {
                                      "amount": "2629.95",
                                      "currencyCode": "USD"
                                    },
                                    "minVariantPrice": {
                                      "amount": "2629.95",
                                      "currencyCode": "USD"
                                    }
                                  },
                                  "description": "",
                                  "totalInventory": 20,
                                  "status": "DRAFT"
                                }
                              },
                              {
                                "node": {
                                  "id": "gid://shopify/Product/8438965535010",
                                  "createdAt": "2023-08-01T14:02:24Z",
                                  "updatedAt": "2023-08-01T14:02:26Z",
                                  "title": "Gift Card",
                                  "handle": "gift-card",
                                  "productType": "",
                                  "priceRangeV2": {
                                    "maxVariantPrice": {
                                      "amount": "100.0",
                                      "currencyCode": "USD"
                                    },
                                    "minVariantPrice": {
                                      "amount": "10.0",
                                      "currencyCode": "USD"
                                    }
                                  },
                                  "description": "This is a gift card for the store",
                                  "totalInventory": 0,
                                  "status": "ACTIVE"
                                }
                              },
                              {
                                "node": {
                                  "id": "gid://shopify/Product/8438965502242",
                                  "createdAt": "2023-08-01T14:02:24Z",
                                  "updatedAt": "2023-08-01T14:02:26Z",
                                  "title": "The Inventory Not Tracked Snowboard",
                                  "handle": "the-inventory-not-tracked-snowboard",
                                  "productType": "",
                                  "priceRangeV2": {
                                    "maxVariantPrice": {
                                      "amount": "949.95",
                                      "currencyCode": "USD"
                                    },
                                    "minVariantPrice": {
                                      "amount": "949.95",
                                      "currencyCode": "USD"
                                    }
                                  },
                                  "description": "",
                                  "totalInventory": 0,
                                  "status": "ACTIVE"
                                }
                              },
                              {
                                "node": {
                                  "id": "gid://shopify/Product/8438965240098",
                                  "createdAt": "2023-08-01T14:02:23Z",
                                  "updatedAt": "2023-08-01T14:02:26Z",
                                  "title": "The Videographer Snowboard",
                                  "handle": "the-videographer-snowboard",
                                  "productType": "",
                                  "priceRangeV2": {
                                    "maxVariantPrice": {
                                      "amount": "885.95",
                                      "currencyCode": "USD"
                                    },
                                    "minVariantPrice": {
                                      "amount": "885.95",
                                      "currencyCode": "USD"
                                    }
                                  },
                                  "description": "",
                                  "totalInventory": 50,
                                  "status": "ACTIVE"
                                }
                              },
                              {
                                "node": {
                                  "id": "gid://shopify/Product/8438965371170",
                                  "createdAt": "2023-08-01T14:02:24Z",
                                  "updatedAt": "2023-08-01T14:02:25Z",
                                  "title": "The Out of Stock Snowboard",
                                  "handle": "the-out-of-stock-snowboard",
                                  "productType": "",
                                  "priceRangeV2": {
                                    "maxVariantPrice": {
                                      "amount": "885.95",
                                      "currencyCode": "USD"
                                    },
                                    "minVariantPrice": {
                                      "amount": "885.95",
                                      "currencyCode": "USD"
                                    }
                                  },
                                  "description": "",
                                  "totalInventory": 0,
                                  "status": "ACTIVE"
                                }
                              }
                            ]
                          }
                        },
                        "extensions": {
                          "cost": {
                            "requestedQueryCost": 502,
                            "actualQueryCost": 36,
                            "throttleStatus": {
                              "maximumAvailable": 1000,
                              "currentlyAvailable": 964,
                              "restoreRate": 50
                            }
                          }
                        }
                      }
                    },
                    "Program - Status - Mark Start": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::lambda:invoke",
                      "Parameters": {
                        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-livemac05:$LATEST",
                        "Payload": {
                          "query": "INSERT INTO onboardingstate(state,timestamp,orgid,taskid) VALUES ($1,$2,$3,$4) ON CONFLICT ON CONSTRAINT unique_orgid_taskid DO UPDATE SET state=$1,timestamp=$2",
                          "values": {
                            "0": "\"Started\"",
                            "1": "NOW()",
                            "3": "3",
                            "2.$": "$.organization_id"
                          }
                        }
                      },
                      "Retry": [
                        {
                          "ErrorEquals": [
                            "Lambda.ServiceException",
                            "Lambda.AWSLambdaException",
                            "Lambda.SdkClientException",
                            "Lambda.TooManyRequestsException"
                          ],
                          "IntervalSeconds": 1,
                          "MaxAttempts": 3,
                          "BackoffRate": 2
                        }
                      ],
                      "Next": "Program - Get Shopify Token",
                      "ResultPath": null
                    },
                    "Program - Get Shopify Token": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::lambda:invoke",
                      "Parameters": {
                        "Payload.$": "$",
                        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-shopify-token-livemac05:$LATEST"
                      },
                      "Retry": [
                        {
                          "ErrorEquals": [
                            "Lambda.ServiceException",
                            "Lambda.AWSLambdaException",
                            "Lambda.SdkClientException",
                            "Lambda.TooManyRequestsException"
                          ],
                          "IntervalSeconds": 1,
                          "MaxAttempts": 3,
                          "BackoffRate": 2
                        }
                      ],
                      "Next": "Program - Get Shopify Catalog",
                      "ResultPath": "$.token"
                    },
                    "Program - Get Shopify Catalog": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::lambda:invoke",
                      "OutputPath": "$.Payload",
                      "Parameters": {
                        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-shopify-api-invoker-livemac05:$LATEST",
                        "Payload": {
                          "url.$": "$.url",
                          "token.$": "$.token.Payload",
                          "method": "POST",
                          "path": "/product-catalog",
                          "body": {
                            "query": "query {products(first: 250, reverse: true, sortKey: UPDATED_AT) {  edges {node {  id  createdAt  updatedAt  title  handle  productType  priceRangeV2 {maxVariantPrice {  amount  currencyCode}minVariantPrice {  amount  currencyCode}  }  description  totalInventory  status}  }}  }",
                            "variables": {}
                          }
                        }
                      },
                      "Retry": [
                        {
                          "ErrorEquals": [
                            "Lambda.ServiceException",
                            "Lambda.AWSLambdaException",
                            "Lambda.SdkClientException",
                            "Lambda.TooManyRequestsException"
                          ],
                          "IntervalSeconds": 1,
                          "MaxAttempts": 3,
                          "BackoffRate": 2
                        }
                      ],
                      "End": true
                    }
                  }
                }
              ],
              "Next": "Parallel",
              "ResultSelector": {
                "web.$": "$[0]",
                "shopify.$": "$[1]"
              },
              "ResultPath": "$.scrapeResults"
            },
            "Parallel": {
              "Type": "Parallel",
              "Branches": [
                {
                  "StartAt": "Branding - Detect Existing (1)",
                  "States": {
                    "Branding - Detect Existing (1)": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::lambda:invoke",
                      "Parameters": {
                        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-livemac05:$LATEST",
                        "Payload": {
                          "query": "SELECT COUNT(branding) FROM organization WHERE id=$1 AND branding IS NOT NULL",
                          "values": {
                            "0.$": "$.organization_id"
                          }
                        }
                      },
                      "Retry": [
                        {
                          "ErrorEquals": [
                            "Lambda.ServiceException",
                            "Lambda.AWSLambdaException",
                            "Lambda.SdkClientException",
                            "Lambda.TooManyRequestsException"
                          ],
                          "IntervalSeconds": 1,
                          "MaxAttempts": 3,
                          "BackoffRate": 2
                        }
                      ],
                      "Next": "Choice (2)",
                      "ResultPath": "$.existingBranding"
                    },
                    "Choice (2)": {
                      "Type": "Choice",
                      "Choices": [
                        {
                          "Not": {
                            "Variable": "$.existingBranding.Payload[0].count",
                            "StringEquals": "0"
                          },
                          "Next": "Branding - Status - Mark Complete (1)"
                        }
                      ],
                      "Default": "Branding - Generate GPT Prompt"
                    },
                    "Branding - Status - Mark Complete (1)": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::lambda:invoke",
                      "Parameters": {
                        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-livemac05:$LATEST",
                        "Payload": {
                          "query": "INSERT INTO onboardingstate(state,timestamp,orgid,taskid) VALUES ($1,$2,$3,$4) ON CONFLICT ON CONSTRAINT unique_orgid_taskid DO UPDATE SET state=$1,timestamp=$2",
                          "values": {
                            "0": "\"Complete\"",
                            "1": "NOW()",
                            "3": "1",
                            "2.$": "$.organization_id"
                          }
                        }
                      },
                      "Retry": [
                        {
                          "ErrorEquals": [
                            "Lambda.ServiceException",
                            "Lambda.AWSLambdaException",
                            "Lambda.SdkClientException",
                            "Lambda.TooManyRequestsException"
                          ],
                          "IntervalSeconds": 1,
                          "MaxAttempts": 3,
                          "BackoffRate": 2
                        }
                      ],
                      "ResultPath": null,
                      "End": true
                    },
                    "Branding - Generate GPT Prompt": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::lambda:invoke",
                      "OutputPath": "$.Payload",
                      "Parameters": {
                        "Payload.$": "$",
                        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-gpt-vision-prompt-generator-livemac05:$LATEST"
                      },
                      "Retry": [
                        {
                          "ErrorEquals": [
                            "Lambda.ServiceException",
                            "Lambda.AWSLambdaException",
                            "Lambda.SdkClientException",
                            "Lambda.TooManyRequestsException"
                          ],
                          "IntervalSeconds": 1,
                          "MaxAttempts": 3,
                          "BackoffRate": 2
                        }
                      ],
                      "Next": "Pass (3)"
                    },
                    "Pass (3)": {
                      "Type": "Pass",
                      "Next": "Branding - GPT Vision",
                      "Parameters": {
                        "url.$": "$.url",
                        "organization_id.$": "$.organization_id",
                        "metrics": [],
                        "useGptVision": true,
                        "skipShopify.$": "$.skipShopify",
                        "prompt.$": "$.prompt",
                        "sanitizeJson": true
                      }
                    },
                    "Branding - GPT Vision": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::lambda:invoke",
                      "Parameters": {
                        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-gpt-livemac05:$LATEST",
                        "Payload.$": "$"
                      },
                      "Retry": [
                        {
                          "ErrorEquals": [
                            "Lambda.ServiceException",
                            "Lambda.AWSLambdaException",
                            "Lambda.SdkClientException",
                            "Lambda.TooManyRequestsException"
                          ],
                          "IntervalSeconds": 1,
                          "MaxAttempts": 3,
                          "BackoffRate": 2
                        },
                        {
                          "ErrorEquals": [
                            "States.TaskFailed"
                          ],
                          "BackoffRate": 2,
                          "IntervalSeconds": 10,
                          "MaxAttempts": 12,
                          "Comment": "GPT Failure"
                        }
                      ],
                      "ResultPath": "$.gpt",
                      "Next": "Branding - Detect Existing"
                    },
                    "Branding - Detect Existing": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::lambda:invoke",
                      "Parameters": {
                        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-livemac05:$LATEST",
                        "Payload": {
                          "query": "SELECT COUNT(branding) FROM organization WHERE id=$1 AND branding IS NOT NULL",
                          "values": {
                            "0.$": "$.organization_id"
                          }
                        }
                      },
                      "Retry": [
                        {
                          "ErrorEquals": [
                            "Lambda.ServiceException",
                            "Lambda.AWSLambdaException",
                            "Lambda.SdkClientException",
                            "Lambda.TooManyRequestsException"
                          ],
                          "IntervalSeconds": 1,
                          "MaxAttempts": 3,
                          "BackoffRate": 2
                        }
                      ],
                      "Next": "Choice (1)",
                      "ResultPath": "$.existingBranding2"
                    },
                    "Choice (1)": {
                      "Type": "Choice",
                      "Choices": [
                        {
                          "Variable": "$.existingBranding2.Payload[0].count",
                          "StringEquals": "0",
                          "Next": "Branding - Status - Mark Complete (2)"
                        }
                      ],
                      "Default": "Pass (4)"
                    },
                    "Pass (4)": {
                      "Type": "Pass",
                      "Next": "Branding - Extract JSON",
                      "Parameters": {
                        "jsonContainer.$": "$.gpt.Payload",
                        "organization_id.$": "$.organization_id"
                      }
                    },
                    "Branding - Extract JSON": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::lambda:invoke",
                      "Parameters": {
                        "Payload.$": "$",
                        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-json-extractor-livemac05:$LATEST"
                      },
                      "Retry": [
                        {
                          "ErrorEquals": [
                            "Lambda.ServiceException",
                            "Lambda.AWSLambdaException",
                            "Lambda.SdkClientException",
                            "Lambda.TooManyRequestsException"
                          ],
                          "IntervalSeconds": 1,
                          "MaxAttempts": 3,
                          "BackoffRate": 2
                        }
                      ],
                      "Next": "Branding - Persist Results",
                      "ResultPath": "$.json"
                    },
                    "Branding - Status - Mark Complete (2)": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::lambda:invoke",
                      "Parameters": {
                        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-livemac05:$LATEST",
                        "Payload": {
                          "query": "INSERT INTO onboardingstate(state,timestamp,orgid,taskid) VALUES ($1,$2,$3,$4) ON CONFLICT ON CONSTRAINT unique_orgid_taskid DO UPDATE SET state=$1,timestamp=$2",
                          "values": {
                            "0": "\"Complete\"",
                            "1": "NOW()",
                            "3": "1",
                            "2.$": "$.organization_id"
                          }
                        }
                      },
                      "Retry": [
                        {
                          "ErrorEquals": [
                            "Lambda.ServiceException",
                            "Lambda.AWSLambdaException",
                            "Lambda.SdkClientException",
                            "Lambda.TooManyRequestsException"
                          ],
                          "IntervalSeconds": 1,
                          "MaxAttempts": 3,
                          "BackoffRate": 2
                        }
                      ],
                      "ResultPath": null,
                      "End": true
                    },
                    "Branding - Persist Results": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::lambda:invoke",
                      "Parameters": {
                        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-livemac05:$LATEST",
                        "Payload": {
                          "query": "UPDATE organization SET branding=$1 WHERE id=$2",
                          "values": {
                            "0.$": "$.json.Payload",
                            "1.$": "$.organization_id"
                          }
                        }
                      },
                      "Retry": [
                        {
                          "ErrorEquals": [
                            "Lambda.ServiceException",
                            "Lambda.AWSLambdaException",
                            "Lambda.SdkClientException",
                            "Lambda.TooManyRequestsException"
                          ],
                          "IntervalSeconds": 1,
                          "MaxAttempts": 3,
                          "BackoffRate": 2
                        }
                      ],
                      "ResultPath": null,
                      "Next": "Branding - Status - Mark Complete"
                    },
                    "Branding - Status - Mark Complete": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::lambda:invoke",
                      "Parameters": {
                        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-livemac05:$LATEST",
                        "Payload": {
                          "query": "INSERT INTO onboardingstate(state,timestamp,orgid,taskid) VALUES ($1,$2,$3,$4) ON CONFLICT ON CONSTRAINT unique_orgid_taskid DO UPDATE SET state=$1,timestamp=$2",
                          "values": {
                            "0": "\"Complete\"",
                            "1": "NOW()",
                            "3": "1",
                            "2.$": "$.organization_id"
                          }
                        }
                      },
                      "Retry": [
                        {
                          "ErrorEquals": [
                            "Lambda.ServiceException",
                            "Lambda.AWSLambdaException",
                            "Lambda.SdkClientException",
                            "Lambda.TooManyRequestsException"
                          ],
                          "IntervalSeconds": 1,
                          "MaxAttempts": 3,
                          "BackoffRate": 2
                        }
                      ],
                      "ResultPath": null,
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "Choice (3)",
                  "States": {
                    "Choice (3)": {
                      "Type": "Choice",
                      "Choices": [
                        {
                          "Variable": "$.skipProgram",
                          "StringEquals": "true",
                          "Next": "Pass (2)"
                        }
                      ],
                      "Default": "Program - Generate GPT Prompt"
                    },
                    "Pass (2)": {
                      "Type": "Pass",
                      "End": true
                    },
                    "Program - Generate GPT Prompt": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::lambda:invoke",
                      "OutputPath": "$.Payload",
                      "Parameters": {
                        "Payload.$": "$",
                        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-gpt-prompt-generator-livemac05:$LATEST"
                      },
                      "Retry": [
                        {
                          "ErrorEquals": [
                            "Lambda.ServiceException",
                            "Lambda.AWSLambdaException",
                            "Lambda.SdkClientException",
                            "Lambda.TooManyRequestsException"
                          ],
                          "IntervalSeconds": 1,
                          "MaxAttempts": 3,
                          "BackoffRate": 2
                        }
                      ],
                      "Next": "Program - GPT"
                    },
                    "Program - GPT": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::lambda:invoke",
                      "Parameters": {
                        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-gpt-livemac05:$LATEST",
                        "Payload.$": "$"
                      },
                      "Retry": [
                        {
                          "ErrorEquals": [
                            "Lambda.ServiceException",
                            "Lambda.AWSLambdaException",
                            "Lambda.SdkClientException",
                            "Lambda.TooManyRequestsException"
                          ],
                          "IntervalSeconds": 1,
                          "MaxAttempts": 3,
                          "BackoffRate": 2
                        },
                        {
                          "ErrorEquals": [
                            "States.TaskFailed"
                          ],
                          "BackoffRate": 2,
                          "Comment": "GPT Error",
                          "IntervalSeconds": 10,
                          "MaxAttempts": 12
                        }
                      ],
                      "ResultPath": "$.gptResult2",
                      "Next": "Program - Translate JSON to SQL"
                    },
                    "Program - Translate JSON to SQL": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::lambda:invoke",
                      "Parameters": {
                        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-json-to-postgres-livemac05:$LATEST",
                        "Payload": {
                          "url.$": "$.url",
                          "organization_id.$": "$.organization_id",
                          "json.$": "$.gptResult2.Payload.output.choices[0].message.function_call.arguments",
                          "jsonKey": "json",
                          "topLevelTableName": "loyaltyprogram"
                        }
                      },
                      "Retry": [
                        {
                          "ErrorEquals": [
                            "Lambda.ServiceException",
                            "Lambda.AWSLambdaException",
                            "Lambda.SdkClientException",
                            "Lambda.TooManyRequestsException"
                          ],
                          "IntervalSeconds": 1,
                          "MaxAttempts": 3,
                          "BackoffRate": 2
                        }
                      ],
                      "Next": "Program - Persist Program/Campaign",
                      "ResultPath": "$.query"
                    },
                    "Program - Persist Program/Campaign": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::lambda:invoke",
                      "Parameters": {
                        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-prod:$LATEST",
                        "Payload": {
                          "url.$": "$.url",
                          "organization_id.$": "$.organization_id",
                          "query.$": "$.query.Payload",
                          "values": []
                        }
                      },
                      "Retry": [
                        {
                          "ErrorEquals": [
                            "Lambda.ServiceException",
                            "Lambda.AWSLambdaException",
                            "Lambda.SdkClientException",
                            "Lambda.TooManyRequestsException"
                          ],
                          "IntervalSeconds": 1,
                          "MaxAttempts": 3,
                          "BackoffRate": 2
                        }
                      ],
                      "ResultPath": null,
                      "Next": "Program - Parallel Persistence"
                    },
                    "Program - Parallel Persistence": {
                      "Type": "Parallel",
                      "Branches": [
                        {
                          "StartAt": "GPT Results - WTE List",
                          "States": {
                            "GPT Results - WTE List": {
                              "Type": "Map",
                              "ItemProcessor": {
                                "ProcessorConfig": {
                                  "Mode": "INLINE"
                                },
                                "StartAt": "Program - Persist WTEs",
                                "States": {
                                  "Program - Persist WTEs": {
                                    "Type": "Task",
                                    "Resource": "arn:aws:states:::lambda:invoke",
                                    "OutputPath": "$.Payload",
                                    "Parameters": {
                                      "Payload.$": "$",
                                      "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboarding-test-branding:$LATEST"
                                    },
                                    "Retry": [
                                      {
                                        "ErrorEquals": [
                                          "Lambda.ServiceException",
                                          "Lambda.AWSLambdaException",
                                          "Lambda.SdkClientException",
                                          "Lambda.TooManyRequestsException"
                                        ],
                                        "IntervalSeconds": 1,
                                        "MaxAttempts": 3,
                                        "BackoffRate": 2
                                      }
                                    ],
                                    "End": true
                                  }
                                }
                              },
                              "End": true,
                              "ItemsPath": "$.metrics"
                            }
                          }
                        },
                        {
                          "StartAt": "GPT Results - Reward List",
                          "States": {
                            "GPT Results - Reward List": {
                              "Type": "Map",
                              "ItemProcessor": {
                                "ProcessorConfig": {
                                  "Mode": "INLINE"
                                },
                                "StartAt": "Program - Persist Shop Rewards",
                                "States": {
                                  "Program - Persist Shop Rewards": {
                                    "Type": "Task",
                                    "Resource": "arn:aws:states:::lambda:invoke",
                                    "OutputPath": "$.Payload",
                                    "Parameters": {
                                      "Payload.$": "$",
                                      "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboarding-test-branding:$LATEST"
                                    },
                                    "Retry": [
                                      {
                                        "ErrorEquals": [
                                          "Lambda.ServiceException",
                                          "Lambda.AWSLambdaException",
                                          "Lambda.SdkClientException",
                                          "Lambda.TooManyRequestsException"
                                        ],
                                        "IntervalSeconds": 1,
                                        "MaxAttempts": 3,
                                        "BackoffRate": 2
                                      }
                                    ],
                                    "End": true
                                  }
                                }
                              },
                              "End": true,
                              "ItemsPath": "$.metrics"
                            }
                          }
                        }
                      ],
                      "ResultPath": null,
                      "Next": "Program - Status - Mark Complete"
                    },
                    "Program - Status - Mark Complete": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::lambda:invoke",
                      "Parameters": {
                        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-livemac05:$LATEST",
                        "Payload": {
                          "query": "INSERT INTO onboardingstate(state,timestamp,orgid,taskid) VALUES ($1,$2,$3,$4) ON CONFLICT ON CONSTRAINT unique_orgid_taskid DO UPDATE SET state=$1,timestamp=$2",
                          "values": {
                            "0": "\"Complete\"",
                            "1": "NOW()",
                            "3": "3",
                            "2.$": "$.organization_id"
                          }
                        }
                      },
                      "Retry": [
                        {
                          "ErrorEquals": [
                            "Lambda.ServiceException",
                            "Lambda.AWSLambdaException",
                            "Lambda.SdkClientException",
                            "Lambda.TooManyRequestsException"
                          ],
                          "IntervalSeconds": 1,
                          "MaxAttempts": 3,
                          "BackoffRate": 2
                        }
                      ],
                      "ResultPath": null,
                      "End": true
                    }
                  }
                }
              ],
              "End": true
            }
          }
        },
        {
          "StartAt": "Member Insights - Status - Mark Start",
          "States": {
            "Member Insights - Status - Mark Start": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-livemac05:$LATEST",
                "Payload": {
                  "query": "INSERT INTO onboardingstate(state,timestamp,orgid,taskid) VALUES ($1,$2,$3,$4) ON CONFLICT ON CONSTRAINT unique_orgid_taskid DO UPDATE SET state=$1,timestamp=$2",
                  "values": {
                    "0": "\"Started\"",
                    "1": "NOW()",
                    "3": "2",
                    "2.$": "$.organization_id"
                  }
                }
              },
              "Retry": [
                {
                  "ErrorEquals": [
                    "Lambda.ServiceException",
                    "Lambda.AWSLambdaException",
                    "Lambda.SdkClientException",
                    "Lambda.TooManyRequestsException"
                  ],
                  "IntervalSeconds": 1,
                  "MaxAttempts": 3,
                  "BackoffRate": 2
                }
              ],
              "Next": "Member Insights - Data Ingestion",
              "ResultPath": null
            },
            "Member Insights - Data Ingestion": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "Payload.$": "$",
                "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboarding-test-branding:$LATEST"
              },
              "Retry": [
                {
                  "ErrorEquals": [
                    "Lambda.ServiceException",
                    "Lambda.AWSLambdaException",
                    "Lambda.SdkClientException",
                    "Lambda.TooManyRequestsException"
                  ],
                  "IntervalSeconds": 1,
                  "MaxAttempts": 3,
                  "BackoffRate": 2
                }
              ],
              "Next": "Member Insights - Parallel Processes",
              "ResultPath": "$.ingestionOutput"
            },
            "Member Insights - Parallel Processes": {
              "Type": "Parallel",
              "Next": "Member Insights - Status - Mark Complete",
              "Branches": [
                {
                  "StartAt": "Member Insights - Loyalty Score",
                  "States": {
                    "Member Insights - Loyalty Score": {
                      "Type": "Task",
                      "Resource": "arn:aws:states:::lambda:invoke",
                      "OutputPath": "$.Payload",
                      "Parameters": {
                        "Payload.$": "$",
                        "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboarding-test-branding:$LATEST"
                      },
                      "Retry": [
                        {
                          "ErrorEquals": [
                            "Lambda.ServiceException",
                            "Lambda.AWSLambdaException",
                            "Lambda.SdkClientException",
                            "Lambda.TooManyRequestsException"
                          ],
                          "IntervalSeconds": 1,
                          "MaxAttempts": 3,
                          "BackoffRate": 2
                        }
                      ],
                      "End": true
                    }
                  }
                },
                {
                  "StartAt": "Member Insights - Metrics List",
                  "States": {
                    "Member Insights - Metrics List": {
                      "Type": "Map",
                      "ItemProcessor": {
                        "ProcessorConfig": {
                          "Mode": "INLINE"
                        },
                        "StartAt": "Member Insights - Metric - Invoke",
                        "States": {
                          "Member Insights - Metric - Invoke": {
                            "Type": "Task",
                            "Resource": "arn:aws:states:::lambda:invoke",
                            "OutputPath": "$.Payload",
                            "Parameters": {
                              "Payload.$": "$",
                              "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboarding-test-branding:$LATEST"
                            },
                            "Retry": [
                              {
                                "ErrorEquals": [
                                  "Lambda.ServiceException",
                                  "Lambda.AWSLambdaException",
                                  "Lambda.SdkClientException",
                                  "Lambda.TooManyRequestsException"
                                ],
                                "IntervalSeconds": 1,
                                "MaxAttempts": 3,
                                "BackoffRate": 2
                              }
                            ],
                            "End": true
                          }
                        }
                      },
                      "End": true,
                      "ItemsPath": "$.metrics"
                    }
                  }
                }
              ],
              "ResultPath": null
            },
            "Member Insights - Status - Mark Complete": {
              "Type": "Task",
              "Resource": "arn:aws:states:::lambda:invoke",
              "Parameters": {
                "FunctionName": "arn:aws:lambda:us-east-1:831543322268:function:onboard-postgres-livemac05:$LATEST",
                "Payload": {
                  "query": "INSERT INTO onboardingstate(state,timestamp,orgid,taskid) VALUES ($1,$2,$3,$4) ON CONFLICT ON CONSTRAINT unique_orgid_taskid DO UPDATE SET state=$1,timestamp=$2",
                  "values": {
                    "0": "\"Complete\"",
                    "1": "NOW()",
                    "3": "2",
                    "2.$": "$.organization_id"
                  }
                }
              },
              "Retry": [
                {
                  "ErrorEquals": [
                    "Lambda.ServiceException",
                    "Lambda.AWSLambdaException",
                    "Lambda.SdkClientException",
                    "Lambda.TooManyRequestsException"
                  ],
                  "IntervalSeconds": 1,
                  "MaxAttempts": 3,
                  "BackoffRate": 2
                }
              ],
              "End": true,
              "ResultPath": null
            }
          }
        }
      ],
      "End": true
    }
  }
};

const detectArns = (json) => {
  const arns = [];
  const regex = /arn:aws:([a-z-]+):([a-z]+):([0-9]+):([a-z]+)\/([a-zA-Z0-9+=,.@_-]+)/g;
  const jsonString = JSON.stringify(json);
  let match;
  while ((match = regex.exec(jsonString)) !== null) {
    arns.push(match[0]);
  }
  return arns;
};

const createVariables = (arns) => {
  let variables = '';
  for (const arn of arns) {
    const parts = arn.split(':');
    const variableName = parts[parts.length - 2];
    const variableValue = arn;
    variables += `const ${variableName} = '${variableValue}';\n`;
  }
  return variables;
};

const splitJson = (json) => {
  // const chunks = [];
  // for (const [key, value] of Object.entries(json)) {
  //   if (typeof value === 'object' && value) {
  //     chunks.push({ [key]: splitJson(value) });
  //   } else {
  //     chunks.push({ [key]: value });
  //   }
  // }
  // return chunks;
  return json.match(/.{1,4096}/g);
};

const requestCosts = [];

const generateTemplates = async (chunks, variables) => {
  let templates = [];
  for (const jsonString of chunks) {
    // const jsonString = JSON.stringify(chunk);
    console.log('GPT request initializing');
    const messages = [{
      role: 'system',
      content: `
        The user will provide some JSON fragments from an AWS Step Function. 
        Replace any ARNs with TS template string variable references, like \${variableName}. 
        Your answer should be the exact raw JSON string passed in, with only the ARNs replaced. 
        The answer should not contain any additional characters, commentary, delimiters, etc.

        Variables available for replacing ARNs: ${variables}
      `
    },
    {
      role: 'user',
      content: jsonString
    }];
    const promptTokens = messages.reduce((a, b) => a + b.content.length, 0);
    const totalTokens = promptTokens + 4096;


    const requestsInLastMinute = requestCosts.filter(r => r.time > Date.now() - 60 * 1000);
    const totalCost = requestsInLastMinute.reduce((a, b) => a + b.cost, 0);
    if (totalCost + totalTokens > 10000) {
      // find the time when we can make the request
      const timeToWait = requestsInLastMinute[0].time + 60 * 1000 - Date.now();
      console.log(`GPT Request limit reached, waiting ${timeToWait / 1000}s`);
      await new Promise(r => setTimeout(r, timeToWait));
      console.log('Timer complete');
    }

    const response = await openai.chat.completions.create({
      model: 'gpt-4',
      messages,
      max_tokens: Math.min(4096, 10000 - promptTokens),
    });
    requestCosts.push({
      time: Date.now(),
      cost: response.usage.total_tokens
    });
    console.log('GPT request complete');
    const template = response.choices[0].message.content.trim();
    templates.push(template);
  }
  return templates;
};

const stitchJson = (chunks, templates) => {
  let json = {};
  for (let i = 0; i < chunks.length; i++) {
    const chunk = chunks[i];
    const template = templates[i];
    for (const [key, value] of Object.entries(chunk)) {
      if (typeof value === 'object') {
        json[key] = stitchJson(value, (template)[key]);
      } else {
        json[key] = template.replace(new RegExp(`\\$${key}`, 'g'), value);
      }
    }
  }
  return json;
};

(async () => {
  const arns = detectArns(json);
  const variables = createVariables(arns);
  const chunks = splitJson(JSON.stringify(json));
  const templates = await generateTemplates(chunks, variables);
  const template = templates.join('');
  console.log(template);
})();
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { WebsiteQAService } from '../utils/website-qa/website-qa.service';
import { LLMRouterService } from '../utils/llm-router/llm-router.service';
import { QAResult } from '../utils/website-qa/website-qa.service';

interface WebsiteQARequest {
  url: string;
  question: string;
  followUpQuestions?: string[];
  maxPagesToVisit?: number;
  useLLMForLinkSelection?: boolean;
  storePassword?: string;
  phase?: 'fast' | 'enhanced' | 'both';
}

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    console.log('Event body:', event.body);
    
    if (!event.body) {
      return {
        statusCode: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Credentials': true,
        },
        body: JSON.stringify({ 
          error: 'Missing request body' 
        }),
      };
    }

    const request: WebsiteQARequest = JSON.parse(event.body);
    
    // Validate required fields
    if (!request.question) {
      return {
        statusCode: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Credentials': true,
        },
        body: JSON.stringify({ 
          error: 'Missing required field: question' 
        }),
      };
    }

    if (!request.url) {
      return {
        statusCode: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Credentials': true,
        },
        body: JSON.stringify({ 
          error: 'Missing required field: url' 
        }),
      };
    }

    // Initialize services
    const llmRouter = new LLMRouterService();
    const websiteQaService = new WebsiteQAService(llmRouter);

    // Process the main question and any follow-up questions
    const results = await websiteQaService.processQuestionsAboutWebsite(
      request.url,
      request.question,
      {
        maxPagesToVisit: request.maxPagesToVisit || 10,
        useLLMForLinkSelection: request.useLLMForLinkSelection !== false,
        followUpQuestions: request.followUpQuestions || [],
        storePassword: request.storePassword,
        phase: request.phase || 'both'
      }
    );

    // Extract the main answer and follow-up answers
    const mainResult = results[0];
    const followUpResults = results.slice(1);

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Credentials': true,
      },
      body: JSON.stringify({
        url: request.url,
        question: mainResult.question,
        answer: mainResult.answer,
        followUpResults: followUpResults.length > 0 ? followUpResults : undefined,
      }),
    };
  } catch (error) {
    console.error('Error in website QA handler:', error);
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Credentials': true,
      },
      body: JSON.stringify({ 
        error: 'An internal error occurred', 
        details: error instanceof Error ? error.message : String(error) 
      }),
    };
  }
};
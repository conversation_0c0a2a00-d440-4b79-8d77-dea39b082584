const { Pool } = require('pg');

// Create a new pool instance to manage connections to the PostgreSQL server
const pool = new Pool({
  user: process.env.PGUSER,
  host: process.env.PGHOST,
  database: process.env.PGDATABASE,
  password: process.env.PGPASSWORD,
  port: process.env.PGPORT,
});

export const handler = async (
  event: any = {},
  context: any = {}
): Promise<any> => {
    const client = await pool.connect();

    try {
        const res = await client.query(
          event.query || JSON.parse(event?.json)?.query, 
          Object.values(event.values || JSON.parse(event?.json)?.values || {})
        );
        
        // Release the client back to the pool
        client.release();

        // Return the query results
        return res.rows;
    } catch (e) { 
        // If there's a query error, log it and re-throw
        console.error('Query failed', e);
        client.release();
        throw e;
    }
} 
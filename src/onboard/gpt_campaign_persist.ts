import * as transformer from './onboard_json_to_postgres';
import * as postgres from './onboard_postgres';

export const handler = async (event: any = {}, context: any = {}): Promise<any> => {

    if (event.body) {
        event = JSON.parse(event.body);
    }

    const dbFields = await postgres.handler({
        query: `SELECT table_name, column_name, data_type, column_default, is_nullable FROM information_schema.columns WHERE table_schema = 'public';`,
        values: []
    });
    console.log(dbFields);

    const [wteQuery, rewardQuery] = await Promise.all([
        transformer.handler({
            dbFields,
            jsonOverrides: {},
            json: event.wteJson,
            topLevelTableName: 'loyaltyearn'
        }, context),
        transformer.handler({
            dbFields,
            jsonOverrides: {},
            json: event.rewardJson,
            topLevelTableName: 'loyaltyredemptionshopitem'
        }, context),
    ]);
    console.log(wteQuery, rewardQuery);

    const [wteResult, rewardsResult] = await Promise.all([
        postgres.handler({
            query: wteQuery,
            values: []
        }, context),

        postgres.handler({
            query: rewardQuery,
            values: []
        }, context),
    ]);

    console.log(wteResult, rewardsResult);

    return {
        body: JSON.stringify([wteResult, rewardsResult]),
        statusCode: 200
    };
}
import { Context, SQSEvent } from "aws-lambda";
import { getQueryResults, runAthenaQueryV2, waitForQueryExecution } from "../utils/athena-helpers";
import { addTagsCustomer, getAPIaccessToken, getShopInfoByOrgId, removeTagsCustomer } from "../utils/shopify-helper";
import { SQS } from "aws-sdk";
import { FRIENDLY_SEGMENT_NAMES } from "./segment-name-mapping";
export class Handler {

	async main(event: SQSEvent, context: Context) {
		const messages: QueryData[] = event.Records.map((record: any) => {
			const message = JSON.parse(record.body);
			return {
				orgId: message.orgId,
				runDate: message.runDate,
				customerBookmark: message?.customerBookmark,
			}
		});

		for (let message of messages) {
			const customerData = await this.getCustomerSegments(message);
			if (customerData) {
				await this.updateCustomerSegments(
					customerData, 
					message.orgId,
					message.runDate,
					context
				);
			}
		}
	}

	private async getCustomerSegments(message: QueryData) {
		let runDate = message.runDate;
		if (runDate.indexOf('T') > -1) {
			runDate = runDate.replace('T', ' ');
		}
		let athenaQuery = `
			SELECT
				customer, 
				max(rundate) as run_date,
				max_by(segment, CAST('${runDate}' AS TIMESTAMP)) as segment,
				previoussegment
			FROM 
				"${process.env.ATHENA_DB}"."rfm_segments"
			WHERE
				organization = '${message.orgId}'
			AND
				rundate >= CAST('${runDate}' AS TIMESTAMP)
		`;

		if (message.customerBookmark) {
			athenaQuery += `\nAND CAST(customer as BIGINT) > CAST('${message.customerBookmark}' AS BIGINT)`;
		}

		athenaQuery += `\nGROUP BY customer, previoussegment`;
		athenaQuery += `\nORDER BY CAST(customer as BIGINT)`;

		console.log(`SEGMENT QUERY: ${athenaQuery}`);

		const queryExecution = await runAthenaQueryV2(
			athenaQuery,
			process.env.ATHENA_OUTPUT_BUCKET!
		);

		const queryId = queryExecution.QueryExecutionId;
		if (!queryId) {
			throw new Error("Failed to start Athena query");
		}

		const succeeded = await waitForQueryExecution(queryId);
		if (!succeeded) {
			throw new Error("Athena query did not succeed");
		}

		const queryResults = await getQueryResults(queryId);

		if (queryResults.ResultSet != null && queryResults.ResultSet.Rows != null) {
			const rows = queryResults.ResultSet.Rows;
			if (rows.length > 1) {
				const customerSegments = rows.slice(1).map((row) => {
					const data: any = row.Data;
					return {
						customer: data[0].VarCharValue,
						segment: data[2].VarCharValue,
						previousSegment: data[3].VarCharValue,
					};
				});
				console.log(`Customer segments size: ${JSON.stringify(customerSegments)}`);
				return customerSegments;
			} else {
				console.log("No query results returned");
			}
		} else {
			console.log("No query results returned");
		}
		return null;
	}

	private async updateCustomerSegments(
		customerData: any[],
		orgId: string,
		runDate: string,
		context: Context
	) {
		const shopInfo = await getShopInfoByOrgId(orgId);
		const accessToken = await getAPIaccessToken(shopInfo.shopDomain);
		const batchSize = 10;

		let hitTimeout = false;
		let lastCustomerProcessed;

		for (let i = 0; i < customerData.length; i += batchSize) {
			const batch = customerData.slice(i, i + batchSize);

			await Promise.all(batch.map(async (customer) => {
				if (hitTimeout) return;

				try {
					const previousSegment = FRIENDLY_SEGMENT_NAMES[customer.previousSegment];
					const segment = FRIENDLY_SEGMENT_NAMES[customer.segment];

					const oldSegmentNames = Object.keys(FRIENDLY_SEGMENT_NAMES);
					const currentSegmentNames = Object.values(FRIENDLY_SEGMENT_NAMES);

					//if (customer.previousSegment) {
						await removeTagsCustomer(
							shopInfo.shopDomain,
							accessToken,
							customer.customer,
							[customer.previousSegment, previousSegment, ...oldSegmentNames, ...currentSegmentNames],
						);
					//}

					
					await addTagsCustomer(
						shopInfo.shopDomain,
						accessToken,
						customer.customer,
						segment
					);

					if (this.hasExceededTimeout(context)) {
						console.log(`LAST CUSTOMER PROCESSED: ${JSON.stringify(customer)}`)
						hitTimeout = true;
						lastCustomerProcessed = customer;
						return;
					}
				} catch (err) {
					console.error(`Error processing customer: ${err}`);
				}
			}));

			if (hitTimeout) {
				break;
			}
		}
		if (hitTimeout) {
			await this.sendSQSMessage(lastCustomerProcessed, orgId, runDate);
			return;
		}
	}


	private hasExceededTimeout(context: Context) {
		return context.getRemainingTimeInMillis() < 180000;
	}

	private async sendSQSMessage(customerData: any, orgId: string, runDate: string) {
		const sqs = new SQS();
		const queueUrl = process.env.SEGMENT_QUEUE_URL!;
		const message = {
			orgId,
			runDate,
			customerBookmark: customerData.customer,
		};
		const params = {
			MessageBody: JSON.stringify(message),
			QueueUrl: queueUrl,
		};
		try {
			await sqs.sendMessage(params).promise();
		} catch (err) {
			console.log(`Error sending message to queue: ${err}`);
		}
	}
}

interface QueryData {
	orgId: string;
	runDate: string;
	customerBookmark?: string;
}

export const handler = new Handler();
export const main = handler.main.bind(handler);

import { DynamoDBClient, PutItemCommand, QueryCommand } from "@aws-sdk/client-dynamodb";

export class CustomerChatConnectionHandler {
    private static ddb = new DynamoDBClient({ region: "us-east-1" });
    private static TABLE_NAME = 'customer-chat-connections-prod';

    static async storeCustomerConnection(
        customerId: string,
        serverConnectionId: string,
        awsConnectionId: string,
    ) {
        const params = {
            TableName: this.TABLE_NAME,
            Item: {
                PK: { S: `CUSTOMER#${customerId}` },
                SK: { S: new Date().toISOString() },
                connectionId: { S: serverConnectionId },
                customerId: { S: customerId },
                ttl: { N: `${Math.floor(Date.now() / 1000) + (24 * 60 * 60)}` },
            }
        };
        await this.ddb.send(new PutItemCommand(params));
        await this.storeServerConnection(serverConnectionId, awsConnectionId);
    }

    private static async storeServerConnection(
        serverConnectionId: string,
        awsConnectionId: string,
    ) {
        const params = {
            TableName: this.TABLE_NAME,
            Item: {
                PK: { S: `SERVER_CONNECTION#${serverConnectionId}` },
                SK: { S: new Date().toISOString() },
                connectionId: { S: awsConnectionId },
            }
        };
        await this.ddb.send(new PutItemCommand(params));
    }

    static async fetchCustomerConnectionId(customerId: string): Promise<string | null | undefined> {
        const customerParams = {
            TableName: this.TABLE_NAME,
            KeyConditionExpression: "PK = :pk",
            ExpressionAttributeValues: {
                ":pk": { S: `CUSTOMER#${customerId}` },
            },
            ScanIndexForward: false,
            Limit: 1,
        };

        const customerResult = await this.ddb.send(new QueryCommand(customerParams));
        if (!customerResult.Items || customerResult.Items.length === 0) {
            return null;
        }
        console.log(`items: ${JSON.stringify(customerResult.Items, null, 2)}`);

        const serverConnectionId = customerResult.Items[0].connectionId.S;

        const serverParams = {
            TableName: this.TABLE_NAME,
            KeyConditionExpression: "PK = :pk",
            ExpressionAttributeValues: {
                ":pk": { S: `SERVER_CONNECTION#${serverConnectionId}` },
            },
            ScanIndexForward: false,
            Limit: 1,
        };

        const serverResult = await this.ddb.send(new QueryCommand(serverParams));
        if (!serverResult.Items || serverResult.Items.length === 0) {
            return null;
        }
        console.log(`items for serverId: ${JSON.stringify(serverResult.Items, null, 2)}`);
        return serverResult.Items[0].connectionId.S;
    }
}

import { APIGatewayProxyResultV2 } from "aws-lambda";
import { CustomerChatConnectionHandler } from "./customer-chat-connection-handler";

export class Handler {

	async main(event: any, context: any): Promise<APIGatewayProxyResultV2> {
		console.log("event 👉", JSON.stringify(event, null, 2));
		const body = JSON.parse(event.body || '{}');

		const customerId = body.data.customerId;
		const serverConnectionId = body.data.serverConnectionId;
		const awsConnectionId = event.requestContext.connectionId;

		if (!customerId || !serverConnectionId) {
			throw new Error('Missing customerId or serverConnectionId');
		}

		await this.saveCustomerConnection(
			customerId,
			serverConnectionId,
			awsConnectionId
		);

		return {
			body: "Complete",
			statusCode: 200,
		};
	}

	private async saveCustomerConnection(
		customerId: string, 
		serverConnectionId: string,
		awsConnectionId: string	
	): Promise<any> {
		await CustomerChatConnectionHandler.storeCustomerConnection(
			customerId,
			serverConnectionId,
			awsConnectionId,
		);
	}
}

export const handler = new Handler();
export const main = handler.main.bind(handler);
import { APIGatewayProxyResultV2 } from 'aws-lambda';
import { CustomerChatConnectionHandler } from './customer-chat-connection-handler';
import { ApiGatewayManagementApi, PostToConnectionCommand } from '@aws-sdk/client-apigatewaymanagementapi';

export class CustomerEvent {
	private apiGatewayManagementApi: ApiGatewayManagementApi;

	constructor() {
		this.apiGatewayManagementApi = new ApiGatewayManagementApi({
			apiVersion: '2018-11-29',
			endpoint: process.env.WEBSOCKET_ENDPOINT_URL,
			region: process.env.AWS_REGION,
		});
	}

	public async publishMessage(customerId: string, message: string): Promise<APIGatewayProxyResultV2> {
		
		try {
			const awsConnectionId = await CustomerChatConnectionHandler.fetchCustomerConnectionId(customerId);
			
			if (awsConnectionId) {
				await this.sendMessageToClient(
					awsConnectionId, 
					{ customerId, message }
				);
				return {
					body: "Message sent successfully",
					statusCode: 200,
				};
			} else {
				return {
					body: "Connection not found for given customer",
					statusCode: 404,
				};
			}
		} catch (error) {
			console.error("Error handling event", error);
			return {
				body: "Internal Server Error",
				statusCode: 500,
			};
		}
	}

	private async sendMessageToClient(connectionId: string, message: any) {
		console.log(`sending message to client 👉`, connectionId + ":" + JSON.stringify(message));
		
		await this.apiGatewayManagementApi.send(new PostToConnectionCommand({
			ConnectionId: connectionId,
			Data: JSON.stringify(message),
		}));
	}
}

import {
	APIGatewayProxyEvent,
	APIGatewayProxyResult,
	ScheduledEvent,
} from "aws-lambda";
import { SQS } from "aws-sdk";
import {
	runAthenaQueryV2,
	waitForQueryExecution,
	getQueryResults,
} from "../utils/athena-helpers";
import { queryDatabase } from "../utils/raleon-helper";
const AWS = require('aws-sdk');
const lambda = new AWS.Lambda();

const sqs = new SQS();
const metricQueueUrl = process.env.METRIC_QUEUE_URL;

export const handler = async (
	event: APIGatewayProxyEvent | ScheduledEvent
): Promise<APIGatewayProxyResult | void> => {
		return handleApiEvent(event as APIGatewayProxyEvent);
};

const handleApiEvent = async (
	event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
	console.log("Received API Gateway event:", event);

	try {
		const orgId = event.queryStringParameters?.orgId || "";
		const offerId = event.queryStringParameters?.offerId || "";
		if(!orgId || !offerId) {
			return {
				statusCode: 400,
				body: "Bad Request",
				headers: {
					"Content-Type": "application/json",
				},
			};
		}
		const functionName = process.env.FUNCTION_NAME || "";
		const payload = {
			orgId: orgId,
			offerId: offerId,
		};
		const result = await invokeOfferLambda(functionName, payload);

		return {
			statusCode: 200,
			body: "OK",
			headers: {
				"Content-Type": "application/json",
			},
		};
	} catch (error) {
		console.error("Error invoking offer lambda:", error);
		return {
			statusCode: 500,
			body: "Internal Server Error",
			headers: {
				"Content-Type": "application/json",
			},
		};
	}

};

const invokeOfferLambda = async (functionName: string, payload: { orgId: string; offerId: string; }) => {
    const params = {
        FunctionName: functionName,
        InvocationType: 'Event',
        Payload: JSON.stringify(payload),
    };

    try {
        const result = await lambda.invoke(params).promise();
        console.log('Lambda invocation result:', result);
        return result;
    } catch (error) {
        console.error('Error invoking lambda:', error);
        throw error;
    }
};
import time
import psycopg2
import aiohttp
import boto3
import json
import os
import requests
import asyncio

WEBAPP_API_URL = os.environ['WEBAPP_API_URL'] or 'https://dev.raleon.io/api/v1'

def get_database_info():
    session = boto3.session.Session()
    client = session.client(
        service_name='secretsmanager',
        region_name='us-east-1',
        endpoint_url='https://vpce-060670840daa313c8-mcqyvy4b.secretsmanager.us-east-1.vpce.amazonaws.com'
    )
    response = client.get_secret_value(SecretId=os.environ['SECRET_ARN'])
    return json.loads(response['SecretString'])

def fetch_json(auth_token, url, method, body=None):
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {auth_token}"
    }
    response = requests.request(method, url, headers=headers, json=body)
    return response.json()

def query_database(query, values):
    database_info = get_database_info()

    with psycopg2.connect(
        user=database_info['username'],
        password=database_info['password'],
        host=database_info['host'],
        dbname=database_info['dbname'],
        port=database_info['port']
    ) as conn:
        with conn.cursor() as cursor:
            cursor.execute(query, values)
            rows = cursor.fetchall()
            return rows

def get_shop_info_by_shop_domain(shop_domain):
    query = '''SELECT "accessToken", "organizationId" FROM "RaleonInfo" WHERE "shopDomain" = %s LIMIT 1'''
    rows = query_database(query, (shop_domain,))

    if rows:
        shop_info = {
            'accessToken': rows[0][0],
            'orgId': rows[0][1]
        }
        return shop_info
    else:
        raise ValueError(f'No information found for domain: {shop_domain}')

def get_shop_info_by_org_id(org_id):
    query = '''SELECT "accessToken", "shopDomain", "organizationId" FROM "RaleonInfo" WHERE "organizationId" = %s LIMIT 1'''
    rows = query_database(query, (org_id,))

    if rows:
        shop_info = {
            'accessToken': rows[0][0],
            'orgId': rows[0][2],
            'shopDomain': rows[0][1]
        }
        return shop_info
    else:
        raise ValueError(f'No information found for organization: {org_id}')

def get_auth_token(accessToken):
    response = fetch_json(accessToken, f"{WEBAPP_API_URL}/create-session", "POST")
    session_token = response['sessionToken']

    login_response = fetch_json(session_token, f"{WEBAPP_API_URL}/users/login/token", "POST")
    return login_response['token']

def get_api_access_token(shop_domain):
    query = '''SELECT "accessToken" FROM "Session" WHERE "shop" = %s LIMIT 1'''
    rows = query_database(query, (shop_domain,))

    if rows:
        return rows[0][0]
    else:
        raise ValueError(f'No access token found for domain: {shop_domain}')

async def add_tags_customer(session, shop_domain, access_token, customer, segment, max_retries=5):
    
    ENDPOINT_URL = f"https://{shop_domain}/admin/api/2024-07/graphql.json"
    CUSTOMER_ID = f"gid://shopify/Customer/{customer}"
    TAGS_TO_ADD = [segment] 
    
    MUTATION = """
    mutation customerTagsAdd($id: ID!, $tags: [String!]!) {
    tagsAdd(id: $id, tags: $tags) {
        userErrors {
        field
        message
        }
        node {
        id
        }
    }
    }
    """

    headers = {
        'X-Shopify-Access-Token': access_token,
        'Content-Type': 'application/json'
    }

    data = {
        'query': MUTATION,
        'variables': {
            'id': CUSTOMER_ID,
            'tags': TAGS_TO_ADD
        }
    }
    attempt = 0
    while attempt < max_retries:
        try:
            async with session.post(ENDPOINT_URL, headers=headers, json=data) as response:
                response_json = await response.json()
                check_api_response(response_json)
                return response_json
        except aiohttp.ClientOSError as e:
            attempt += 1
            print(f"Error occurred: {e}. Attempt {attempt} of {max_retries}")
            if attempt < max_retries:
                await asyncio.sleep(2 ** attempt)  # Exponential backoff
        except Exception as e:
            print(f"Unexpected error: {e}")
            break  # Exit the loop for non-retryable errors

async def remove_tags_customer(session, shop_domain, access_token, customer, segment, max_retries=5):
    
    ENDPOINT_URL = f"https://{shop_domain}/admin/api/2024-07/graphql.json"
    CUSTOMER_ID = f"gid://shopify/Customer/{customer}"
    TAGS_TO_REMOVE = [segment] 

    MUTATION = """
    mutation customerTagsRemove($id: ID!, $tags: [String!]!) {
    tagsRemove(id: $id, tags: $tags) {
        userErrors {
        field
        message
        }
        node {
        id
        }
    }
    }
    """

    headers = {
        'X-Shopify-Access-Token': access_token,
        'Content-Type': 'application/json'
    }

    data = {
        'query': MUTATION,
        'variables': {
            'id': CUSTOMER_ID,
            'tags': TAGS_TO_REMOVE
        }
    }
    attempt = 0
    while attempt < max_retries:
        try:
            async with session.post(ENDPOINT_URL, headers=headers, json=data) as response:
                response_json = await response.json()
                check_api_response(response_json)
                return response_json
        except aiohttp.ClientOSError as e:
            attempt += 1
            print(f"Error occurred: {e}. Attempt {attempt} of {max_retries}")
            if attempt < max_retries:
                await asyncio.sleep(2 ** attempt)  # Exponential backoff
        except Exception as e:
            print(f"Unexpected error: {e}")
            break  # Exit the loop for non-retryable errors

def get_shopify_customers(shop_domain, access_token, customer_ids, limit=250):
    customers = []
    headers = {
        "X-Shopify-Access-Token": access_token,
        "Content-Type": "application/json",
    }

    def chunked_list(lst, n):
        for i in range(0, len(lst), n):
            yield lst[i:i + n]

    for ids_chunk in chunked_list(customer_ids, limit):
        ids_param = ','.join(map(str, ids_chunk))
        params = {"limit": limit, "ids": ids_param}

        response = requests.get(f"https://{shop_domain}/admin/api/2024-07/customers.json", headers=headers, params=params)
        if response.status_code == 200:
            data = response.json()
            customers.extend(data.get('customers', []))
        else:
            print(f"Error fetching data: {response.status_code}")
            break  # Exit loop on error

    return customers

async def save_meta_field(key, object, session, object_value, segment_value, shop_domain, api_token, max_retries=5):
        
        mutation = """
            mutation MetafieldsSet($metafields: [MetafieldsSetInput!]!) {
                metafieldsSet(metafields: $metafields) {
                    metafields {
                        key
                        namespace
                        value
                        createdAt
                        updatedAt
                    }
                    userErrors {
                        field
                        message
                        code
                    }
                }
            }"""
        
        variables = {
            "metafields": [
                {
                    "key": key,
                    "namespace": "raleonInfo",
                    "ownerId": f"gid://shopify/{object}/{object_value}",
                    "type": "single_line_text_field",
                    "value": segment_value
                }
            ]
        }
        
        headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": api_token
        }
        attempt = 0
        while attempt < max_retries:
            try:
                async with session.post(
                    f"https://{shop_domain}/admin/api/2024-07/graphql.json",
                    headers=headers,
                    json={
                        "query": mutation,
                        "variables": variables
                    }
                ) as response:
                    json_response = await response.json()
                    try:
                        response_json = await response.json()
                        check_api_response(response_json)
                        if json_response["data"]["metafieldsSet"]["userErrors"]:
                            print(f'Failed to set metafield: {json_response["data"]["metafieldsSet"]["userErrors"][0]["message"]}')
                    except Exception as e:
                        print(f"Error occurred: {e}. Response: {json_response}")
            except aiohttp.ClientOSError as e:
                attempt += 1
                print(f"Error occurred: {e}. Attempt {attempt} of {max_retries}")
                if attempt < max_retries:
                    await asyncio.sleep(2 ** attempt)  # Exponential backoff
            except Exception as e:
                print(f"Unexpected error: {e}")
                break  # Exit the loop for non-retryable errors
        return json_response
def check_api_response(response):
    print(response)
    throttleStatus = response.get('extensions', {}).get('cost', {}).get('throttleStatus', {})
    currentlyAvailable = throttleStatus.get('currentlyAvailable', 0)
    maximumAvailable = throttleStatus.get('maximumAvailable', 1000)
    if currentlyAvailable < 0.25 * maximumAvailable:
        print("Reached 75% of API capacity. Waiting for reset...")
        time.sleep(1)
    user_errors = response.get('data', {}).get('tagsRemove', {}).get('userErrors', [])
    if user_errors:
        for error in user_errors:
            print(f"Error on field {error['field']}: {error['message']}")

async def bulk_update_metafields(jsonl_content, shop_domain, access_token):
    print(f"jsonl content: {jsonl_content}")
    shopify_url = f"https://{shop_domain}/admin/api/2024-07/graphql.json"
    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": f"{access_token}"
    }

    staged_uploads_query = """
    mutation {
      stagedUploadsCreate(input: {
        resource: BULK_MUTATION_VARIABLES,
        filename: "bulk_update.jsonl",
        mimeType: "text/jsonl",
        httpMethod: POST,
      }) {
        stagedTargets {
          url
          resourceUrl
          parameters {
            name
            value
          }
        }
        userErrors {
          field
          message
        }
      }
    }
    """

    async with aiohttp.ClientSession() as session:
        async with session.post(shopify_url, json={'query': staged_uploads_query}, headers=headers) as response:
            if response.status != 200:
                return {'statusCode': response.status, 'body': json.dumps('Failed to create staged upload')}
            response_data = await response.json()
            print(f"Response Data: {response_data}")
            signed_url = response_data['data']['stagedUploadsCreate']['stagedTargets'][0]['url']
            upload_params = response_data['data']['stagedUploadsCreate']['stagedTargets'][0]['parameters']
            resource_url = next((param['value'] for param in upload_params if param['name'] == 'key'), None)

        data = aiohttp.FormData()
        for param in upload_params:
            data.add_field(param['name'], param['value'])
        data.add_field('file', jsonl_content, content_type='text/jsonl', filename='bulk_update.jsonl')

        # POST request to upload the JSONL content
        async with session.post(signed_url, data=data) as post_response:
            print(f"post response: {post_response}")
            if post_response.status not in [200, 201]:
                response_text = await post_response.text()
                print(f"Upload error response: {response_text}")
                return {'statusCode': post_response.status, 'body': json.dumps('Failed to upload file')}

        # Prepare and execute the bulk operation mutation
        mutation_string = """
        mutation MetafieldsSet($input: [MetafieldsSetInput!]!) {
          metafieldsSet(metafields: $input) {
            metafields {
              id
            }
            userErrors {
              field
              message
            }
          }
        }
        """
        bulk_operation_query = f"""
        mutation {{
            bulkOperationRunMutation(
                mutation: \"{mutation_string}\",
                stagedUploadPath: \"{resource_url}\"
            ) {{
                bulkOperation {{
                    id
                    status
                }}
                userErrors {{
                    field
                    message
                }}
            }}
        }}
        """

        async with session.post(shopify_url, json={'query': bulk_operation_query}, headers=headers) as response:
            print(f"session post response: {response}")
            if response.status != 200:
                return {'statusCode': response.status, 'body': json.dumps('Failed to start bulk operation')}
            response_data = await response.json()
            print(f"GraphQL response: {response_data}")

            if 'errors' in response_data:
                print(f"GraphQL error response: {response_data['errors']}")
                return {'statusCode': 400, 'body': json.dumps('GraphQL error occurred')}

            bulk_operation_id = response_data['data']['bulkOperationRunMutation']['bulkOperation']['id']
            print(f"bulk operation id: {bulk_operation_id}")
            return bulk_operation_id
            #await check_bulk_operation_status(shop_domain, access_token, bulk_operation_id, session)


async def check_bulk_operation_status(shop_domain, access_token, operation_id, session):
    print(f"accesstoek{access_token}")
    shopify_url = f"https://{shop_domain}/admin/api/2024-07/graphql.json"
    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": f"{access_token}"
    }

    status_query = """
    query {
      currentBulkOperation(type: MUTATION) {
        id
        status
        errorCode
        createdAt
        completedAt
        objectCount
        fileSize
        url
        partialDataUrl
      }
    }
    """
    await asyncio.sleep(2)
    while True:
        async with session.post(shopify_url, json={'query': status_query}, headers=headers) as response:
            if response.status != 200:
                print("Error checking bulk operation status")
                return {'statusCode': response.status, 'body': json.dumps('Error checking bulk operation status')}
            response_data = await response.json()  # Await the json() coroutine and store the result
            status_response = response_data['data']['currentBulkOperation']  # Now you can access the data

            if status_response['id'] != operation_id:
                print("Bulk operation ID does not match")
                print(f"Expected: {operation_id}")
                print(f"Actual: {status_response['id']}")
                print(response_data)
                return {'statusCode': 400, 'body': json.dumps('Bulk operation ID does not match')}

            if status_response['status'] in ['COMPLETED', 'FAILED', 'CANCELED']:
                print(f"Bulk operation completed with status: {status_response['status']}")
                return {'statusCode': 200, 'body': json.dumps(f'Bulk operation completed with status: {status_response["status"]}')}

            print(f"Current status: {status_response['status']}. Waiting for completion...")
            await asyncio.sleep(60)  # Check status asynchronously

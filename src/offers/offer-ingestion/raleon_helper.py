from datetime import datetime
import psycopg2
import boto3
import json
import os
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding
import base64

def get_database_info():
    session = boto3.session.Session()
    client = session.client(
        service_name='secretsmanager',
        region_name='us-east-1',
        endpoint_url='https://vpce-060670840daa313c8-mcqyvy4b.secretsmanager.us-east-1.vpce.amazonaws.com'
    )
    response = client.get_secret_value(SecretId=os.environ['RALEON_DB'])
    print(response)
    return json.loads(response['SecretString'])

def query_database(query, values):
    database_info = get_database_info()

    with psycopg2.connect(
        user=database_info['username'],
        password=database_info['password'],
        host=database_info['host'],
        dbname=database_info['dbname'],
        port=database_info['port']
    ) as conn:
        with conn.cursor() as cursor:
            cursor.execute(query, values)
            
            if query.strip().upper().startswith('SELECT'):
                rows = cursor.fetchall()
                return rows
            
            # For non-SELECT operations, commit the changes
            conn.commit()

        
def update_last_run(org_id, metric_name):
    query_database("UPDATE organizationmetric SET lastrundate = %s WHERE orgid = %s and metricid = (Select id From metric where name=%s)", (datetime.now(), org_id, metric_name))

def get_klaviyo_integration_by_org_id(org_id):
    query = '''SELECT "value" FROM "public"."organizationkeys" WHERE "organizationid" = %s AND key = %s AND secretkeyid = %s LIMIT 1'''
    rows = query_database(query, (org_id, 'Klaviyo-API-Key', 'default'))

    if rows:
        klaviyo_info = {
            'value': rows[0][0],
        }
        return klaviyo_info
    else:
        raise ValueError(f"No information found for organization: {org_id}")

def decrypt(text):
    session = boto3.session.Session()
    client = session.client(
        service_name='secretsmanager',
        region_name='us-east-1',
        endpoint_url='https://vpce-060670840daa313c8-mcqyvy4b.secretsmanager.us-east-1.vpce.amazonaws.com'
    )
    response = client.get_secret_value(SecretId=os.environ['ORGANIZATION_ENCRYPTION_KEY'])
    secret = json.loads(response['SecretString'])

    key = secret['default']

    text_parts = text.split(':')
    if len(text_parts) == 0:
        raise ValueError('Invalid encrypted text')

    iv = bytes.fromhex(text_parts.pop(0))
    encrypted_text = bytes.fromhex(':'.join(text_parts))
    key_bytes = key.encode()

    # Ensure the key length is suitable for AES-256
    if len(key_bytes) != 32:
        raise ValueError("Key must be 32 bytes (256 bits) for AES-256.")

    cipher = Cipher(algorithms.AES(key_bytes), modes.CBC(iv), backend=default_backend())
    decryptor = cipher.decryptor()
    decrypted = decryptor.update(encrypted_text) + decryptor.finalize()

    return decrypted.decode()
import os
os.environ['SURPRISE_DATA_FOLDER'] = '/tmp/surprise_data'
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from sklearn.preprocessing import StandardScaler
from sklearn.mixture import GaussianMixture
from sklearn.metrics import silhouette_score
from surprise import SVD, Dataset, Reader
from pyathena import connect
import requests
import shopify_helper
import aiohttp
import asyncio

WEBAPP_API_URL = os.environ.get('WEBAPP_API_URL', 'https://dev.raleon.io/api/v1')
ATHENA_DB = os.environ.get('ATHENA_DB', 'ecommerce')
POSGRES_DB = os.environ.get('POSGRES_DB', 'postgres_test')

class Handler:
    def __init__(self):
        self.orgId = None
        self.offerData = None
        self.customerData = None
        self.recommendations_list = None

    def main(self, event, context):
        return asyncio.get_event_loop().run_until_complete(self._async_main(event, context))

    async def _async_main(self, event, context):
        print("Received event:", event)
        try:
            self.orgId = event.get('orgId', '')
            offerId = event.get('offerId', '')
            print(f"Processing for orgId: {self.orgId}, offerId: {offerId}")

            users, purchases, valid_skus = self.load_data(offerId)
            users = self.preprocess_user_data(users, purchases)
            users = self.cluster_users(users)
            
            svd_model = self.train_recommendation_model(purchases, valid_skus)
            
            #customers_in_segment = await self.getCustomersInSegment(self.offerData)
            customers_in_segment = []
            offers = self.generate_offers(users, purchases, valid_skus, svd_model, customers_in_segment)
            
            await self.send_offers_in_batches(offerId, offers)

            response = {
                "statusCode": 200,
                "body": json.dumps("Finished processing and sending offers!"),
            }
        except Exception as e:
            print("Error processing the event:", str(e))
            response = {
                "statusCode": 500,
                "body": json.dumps("Failed to process offers due to an internal error.")
            }
        return response

    def load_data(self, offerId):
        conn = connect(s3_staging_dir=f"{os.environ['ATHENA_OUTPUT_BUCKET']}", region_name='us-east-1')
        
        # Query for purchases
        purchase_query = f"""
            WITH aggregated_refunds AS (
                SELECT 
                    order_id, 
                    COALESCE(SUM(refund_amount), 0) AS total_refund_amount  -- Pre-aggregate refund amounts by `order_id`
                FROM 
                    "{ATHENA_DB}"."order_refunds"
                WHERE 
                    organization = '{self.orgId}'
                GROUP BY 
                    order_id
            ),
            aggregated_orders AS (
                SELECT 
                    o.id AS order_id,
                    o.customer AS user,
                    o.created_at AS date,
                    MAX(o.subtotal_price) AS subtotal_price,  -- Aggregate line items at the order level
                    ARRAY_AGG(o.item_sku) AS item_ids  -- Aggregate SKUs into an array
                FROM 
                    "{ATHENA_DB}"."filtered_orders" o
                WHERE 
                    o.organization = '{self.orgId}'
                    AND o.customer != '0'
                    AND o.customer NOT IN (
                        SELECT customerId 
                        FROM "{ATHENA_DB}".excluded_customers 
                        WHERE organization = '{self.orgId}'
                    )
                GROUP BY 
                    o.id, o.customer, o.created_at
            )
            SELECT 
                o.user, 
                o.date, 
                o.subtotal_price - COALESCE(r.total_refund_amount, 0) AS price,  -- Subtract refund amounts from subtotal price
                o.order_id AS id,
                o.item_ids
            FROM 
                aggregated_orders o
            LEFT JOIN 
                aggregated_refunds r ON o.order_id = r.order_id  -- Join pre-aggregated refunds
            WHERE 
                o.user != '0'
                AND o.user NOT IN (
                    SELECT customerId 
                    FROM "{ATHENA_DB}".excluded_customers 
                    WHERE organization = '{self.orgId}'
                )
            GROUP BY 
                o.user, o.date, o.subtotal_price, r.total_refund_amount, o.order_id, o.item_ids;
        """
        purchases = pd.read_sql_query(purchase_query, conn)
        purchases['date'] = pd.to_datetime(purchases['date'])
        purchases['item_ids'] = purchases['item_ids'].apply(lambda x: [item.strip().strip("'") for item in x.strip('[]').split(',') if item.strip()])

        # Query for user data
        user_query = f"""
           WITH OrderDiffs AS (
                SELECT
                    o.customer AS CustomerID,
                    o.created_at,
                    LAG(o.created_at) OVER (PARTITION BY o.customer ORDER BY o.created_at) AS PreviousOrderDate
                FROM
                    "{ATHENA_DB}"."filtered_orders" o
                WHERE
                    o.organization = '{self.orgId}'
            ),
            AvgTimeBetweenPurchases AS (
                SELECT
                    CustomerID,
                    AVG(EXTRACT(DAY FROM (created_at - PreviousOrderDate))) AS AvgTimeBetweenPurchases
                FROM
                    OrderDiffs
                WHERE
                    PreviousOrderDate IS NOT NULL
                GROUP BY
                    CustomerID
            ),
            LoyaltyTransactions AS (
                SELECT
                    ltx.loyaltycurrencybalanceid,
                    SUM(CASE WHEN ltx.amount < 0 THEN 1 ELSE 0 END) AS used,
                    SUM(CASE WHEN ltx.amount > 0 THEN 1 ELSE 0 END) AS earned
                FROM
                    "{POSGRES_DB}"."public"."loyaltycurrencytxlog" ltx
                GROUP BY
                    ltx.loyaltycurrencybalanceid
            )
            SELECT
                ri.id,
                ri.orgid,
                ri.raleonuserid,
                ri.identityvalue,
                ri.loyaltysegment,
                lb.loyaltycurrencyid,
                lb.balance,
                lb.trailingtwelvemonthgranttotal,
                COALESCE(tx.earned, 0) AS earned,
                COALESCE(tx.used, 0) AS used,
                COALESCE(ord.TotalOrders, 0) AS TotalOrders,
                COALESCE(ord.OrdersWithDiscounts, 0) AS OrdersWithDiscounts,
                COALESCE(ord.TotalSpent, 0) AS TotalSpent,
                COALESCE(ord.SubtotalSpent, 0) AS SubtotalSpent,
                COALESCE(ord.OrdersWithFreeShipping, 0) AS OrdersWithFreeShipping,
                COALESCE(ord.OrdersWithRefunds, 0) AS OrdersWithRefunds,
                COALESCE(ord.TotalAmountRefunded, 0) AS TotalAmountRefunded,
                COALESCE(ord.DaysSinceLastPurchase, NULL) AS DaysSinceLastPurchase,
                COALESCE(atbp.AvgTimeBetweenPurchases, NULL) AS AvgTimeBetweenPurchases
            FROM
                "{POSGRES_DB}"."public"."raleonuseridentity" ri
            LEFT JOIN
                "{POSGRES_DB}"."public"."loyaltycurrencybalance" lb ON ri.raleonuserid = lb.raleonuserid
            LEFT JOIN
                LoyaltyTransactions tx ON lb.id = tx.loyaltycurrencybalanceid
            LEFT JOIN
                (
                    SELECT
                        o.customer AS CustomerID,
                        COUNT(DISTINCT o.id) AS TotalOrders,
                        COUNT(DISTINCT CASE WHEN o.total_discounts > 0 THEN o.id END) AS OrdersWithDiscounts,
                        SUM(DISTINCT o.total_price) AS TotalSpent,
                        SUM(DISTINCT o.subtotal_price) AS SubtotalSpent,
                        COUNT(DISTINCT CASE WHEN o.total_shipping_price = 0 THEN o.id END) AS OrdersWithFreeShipping,
                        COUNT(DISTINCT r.order_id) AS OrdersWithRefunds,
                        COALESCE(SUM(r.refund_amount), 0) AS TotalAmountRefunded,
                        MAX(o.created_at) AS LastOrderDate,
                        EXTRACT(DAY FROM CURRENT_DATE - MAX(o.created_at)) AS DaysSinceLastPurchase
                    FROM
                        "{ATHENA_DB}"."filtered_orders" o
                    LEFT JOIN
                        "{ATHENA_DB}"."order_refunds" r ON o.id = r.order_id AND r.organization = '{self.orgId}'
                    WHERE
                        o.organization = '{self.orgId}'
                    GROUP BY
                        o.customer
                ) ord ON ri.identityvalue = ord.CustomerID
            LEFT JOIN
                AvgTimeBetweenPurchases atbp ON ri.identityvalue = atbp.CustomerID
            WHERE
                ri.orgid = {self.orgId};
        """

        users = pd.read_sql_query(user_query, conn)

        # Get recommendations list
        self.recommendations_list = self.get_recommendations_list(offerId)
        print(f"Recommendations list: {self.recommendations_list}")
        valid_skus = set(self.recommendations_list['item_sku'].unique())
        print(f"Valid SKUs: {valid_skus}")
        valid_skus.discard('VPP-SC15')
        print(f"Valid SKUs after removing VPP-SC15: {valid_skus}")

        active_users = purchases['user'].unique()
        users = users[users['identityvalue'].isin(active_users)]

        return users, purchases, valid_skus

    def get_recommendations_list(self, offerId):
        url = f"{WEBAPP_API_URL}/offer/{offerId}"
        shopInfo = shopify_helper.get_shop_info_by_org_id(self.orgId)
        authToken = shopify_helper.get_auth_token(shopInfo.get('accessToken'))

        headers = {
            'Authorization': f'Bearer {authToken}'
        }

        response = requests.get(url, headers=headers)

        if response.status_code == 200:
            recommendations_data = response.json()
            self.offerData = recommendations_data
            print(f"offerData 👉 {self.offerData}")
            print(f"recommendations_data 👉 {recommendations_data}")
            products = recommendations_data['products']
            df = pd.DataFrame(products)
            recommendations_df = df[['id', 'sku', 'name', 'handle', 'imageURL']].rename(columns={'sku': 'item_sku', 'name': 'item_name'})
            return recommendations_df
        else:
            raise ValueError(f"Failed to get recommendations list: {response.text}")
        
        # recommendations_data = [
        #     {"id": "VPP-CH01", "item_sku": "VPP-CH01", "item_name": "Chocolate", "handle": "kos-organic-vegan-plant-based-protein-powder", "imageURL": "https://kos.com/cdn/shop/files/1_CHO28-Front.png"},
        #     {"id": "VPP-VN01", "item_sku": "VPP-VN01", "item_name": "Vanilla", "handle": "vanilla-protein", "imageURL": "https://example.com/vanilla.jpg"},
        #     {"id": "VPP-PB01", "item_sku": "VPP-PB01", "item_name": "Peanut Butter", "handle": "peanut-butter-protein", "imageURL": "https://example.com/peanut-butter.jpg"},
        #     {"id": "VPP-SC01", "item_sku": "VPP-SC01", "item_name": "Strawberry", "handle": "strawberry-protein", "imageURL": "https://example.com/strawberry.jpg"},
        #     {"id": "VPP-UF01", "item_sku": "VPP-UF01", "item_name": "Unflavored", "handle": "unflavored-protein", "imageURL": "https://example.com/unflavored.jpg"},
        #     {"id": "VPP-SB01", "item_sku": "VPP-SB01", "item_name": "Strawberry Banana", "handle": "strawberry-banana-protein", "imageURL": "https://example.com/strawberry-banana.jpg"},
        # ]
    
        # recommendations_df = pd.DataFrame(recommendations_data)
    
        # print(f"Hardcoded recommendations list 👉 {recommendations_df}")
    
        # return recommendations_df

    def handle_outliers(self, df, columns, method='winsorize'):
        for col in columns:
            if method == 'winsorize':
                lower = df[col].quantile(0.05)
                upper = df[col].quantile(0.95)
                df[col] = df[col].clip(lower, upper)
            elif method == 'iqr':
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                df[col] = df[col][(df[col] >= Q1 - 1.5*IQR) & (df[col] <= Q3 + 1.5*IQR)]
        return df

    def enhance_features(self, users, purchases):
        users['purchase_frequency'] = users['TotalOrders'] / (users['DaysSinceLastPurchase'] + 1)
        users['CLV'] = users['TotalSpent'] * users['purchase_frequency']
        
        purchases['month'] = purchases['date'].dt.month
        seasonal_patterns = purchases.groupby(['user', 'month'])['price'].sum().unstack()
        users = users.merge(seasonal_patterns, left_on='identityvalue', right_on='user', how='left')
        
        return users

    def preprocess_user_data(self, users, purchases):
        required_columns = ['loyaltysegment', 'balance', 'trailingtwelvemonthgranttotal', 'TotalOrders', 'OrdersWithDiscounts', 'TotalSpent', 'OrdersWithRefunds', 'DaysSinceLastPurchase', 'AvgTimeBetweenPurchases']
        missing_columns = [col for col in required_columns if col not in users.columns]
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")

        loyalty_map = {'Not Loyal': 0, 'Growth': 1, 'Very Loyal': 2}
        users['loyalty_score'] = users['loyaltysegment'].map(loyalty_map).fillna(0)
        
        users['TotalOrders'] = users['TotalOrders'].fillna(0)
        users['OrdersWithDiscounts'] = users['OrdersWithDiscounts'].fillna(0)
        users['TotalSpent'] = users['TotalSpent'].fillna(0)
        
        users['discount_affinity'] = users['OrdersWithDiscounts'] / users['TotalOrders'].replace(0, 1)
        users['avg_order_value'] = users['TotalSpent'] / users['TotalOrders'].replace(0, 1)
        users['refund_rate'] = users['OrdersWithRefunds'].fillna(0) / users['TotalOrders'].replace(0, 1)
        
        if 'DaysSinceLastPurchase' not in users.columns:
            print("Warning: DaysSinceLastPurchase column is missing. Using a default value of 0.")
            users['DaysSinceLastPurchase'] = 0
        else:
            users['DaysSinceLastPurchase'] = users['DaysSinceLastPurchase'].fillna(users['DaysSinceLastPurchase'].median())

        features_to_scale = ['balance', 'trailingtwelvemonthgranttotal', 'avg_order_value', 'AvgTimeBetweenPurchases']
        scaler = StandardScaler()
        users[features_to_scale] = scaler.fit_transform(users[features_to_scale])
        
        users = users[users['TotalSpent'] > 0]
        users = users[(users['DaysSinceLastPurchase'] > 20) & (users['DaysSinceLastPurchase'] <= 365)]

        users = self.handle_outliers(users, ['TotalSpent', 'AvgTimeBetweenPurchases'])
        users = self.enhance_features(users, purchases)

        return users

    def find_optimal_clusters(self, users, features, max_clusters=10):
        silhouette_scores = []
        for n_clusters in range(2, max_clusters+1):
            gmm = GaussianMixture(n_components=n_clusters, random_state=42)
            cluster_labels = gmm.fit_predict(users[features])
            silhouette_scores.append(silhouette_score(users[features], cluster_labels))
        
        optimal_clusters = silhouette_scores.index(max(silhouette_scores)) + 2
        return optimal_clusters

    def cluster_users(self, users):
        features = ['loyalty_score', 'discount_affinity', 'avg_order_value', 'refund_rate', 'balance', 'trailingtwelvemonthgranttotal', 'TotalSpent', 'DaysSinceLastPurchase', 'AvgTimeBetweenPurchases', 'purchase_frequency', 'CLV']
        
        missing_features = [feat for feat in features if feat not in users.columns]
        if missing_features:
            raise ValueError(f"Missing features for clustering: {missing_features}")
        
        if users[features].isna().any().any():
            print("Warning: NaN values still present after preprocessing. They will be replaced with mean values.")
            users[features] = users[features].fillna(users[features].mean())
        
        optimal_clusters = self.find_optimal_clusters(users, features)
        gmm = GaussianMixture(n_components=optimal_clusters, random_state=42)
        users['cluster'] = gmm.fit_predict(users[features])
        
        return users

    def train_recommendation_model(self, purchases, valid_skus):
        # Explode the purchases dataframe so each row represents a single item purchase
        exploded_purchases = purchases.explode('item_ids')
        exploded_purchases = exploded_purchases[exploded_purchases['item_ids'].isin(valid_skus)]
        
        # Create a rating based on purchase frequency
        user_item_frequency = exploded_purchases.groupby(['user', 'item_ids']).size().reset_index(name='frequency')
        max_frequency = user_item_frequency['frequency'].max()
        user_item_frequency['rating'] = user_item_frequency['frequency'] / max_frequency * 5  # Scale to 0-5 rating
        
        reader = Reader(rating_scale=(0, 5))
        data = Dataset.load_from_df(user_item_frequency[['user', 'item_ids', 'rating']], reader)
        
        svd = SVD()
        trainset = data.build_full_trainset()
        svd.fit(trainset)
        
        return svd

    def get_recommendations(self, user, purchases, valid_skus, svd_model, n=1):
        user_items = purchases[purchases['user'] == user]['item_ids'].explode().unique()
        all_items = list(valid_skus)
        items_to_pred = list(set(all_items) - set(user_items))
        print(f"Items to predict: {items_to_pred}")  # Debug print
        
        predictions = [svd_model.predict(user, item) for item in items_to_pred]
        top_n = sorted(predictions, key=lambda x: x.est, reverse=True)[:n]
        return [pred.iid for pred in top_n]

    def personalize_offer(self, user_data, recommendations, global_avg_spent):
        loyalty_score = user_data['loyalty_score']
        total_spent = user_data['TotalSpent']
        days_since_last_purchase = user_data['DaysSinceLastPurchase']
        
        if loyalty_score == 2:  # Very Loyal
            if days_since_last_purchase > 60:
                return {"friendly_name": "20% off" ,"coupon_type": "percent-off-product", "type": "reactivation", "discount": 20, "free_shipping": True}
            else:
                return {"friendly_name": "10% off", "coupon_type": "percent-off-product","type": "upsell", "discount": 10, "bundle": True}
        elif loyalty_score == 1:  # Growth
            if total_spent > global_avg_spent:
                return {"friendly_name": "10% off", "coupon_type": "percent-off-product","type": "reward", "discount": 10, "points_multiplier": 2}
            else:
                return {"friendly_name": "15% off", "coupon_type": "percent-off-product","type": "cross_sell", "discount": 15, "related_product": recommendations[1] if len(recommendations) > 1 else None}
        else:  # Not Loyal
            return {"friendly_name": "25% off", "coupon_type": "percent-off-product","type": "acquisition", "discount": 25, "limited_time": True}

    def generate_offers(self, users, purchases, valid_skus, svd_model, customers_in_segment):
        global_avg_spent = users['TotalSpent'].mean()
        offers = []

        # Convert recommendations_list to a dictionary for faster lookup
        recommendations_dict = self.recommendations_list.set_index('item_sku').to_dict('index')

        for index, user_data in users.iterrows():
            try:
                user = user_data['identityvalue']
                print(f"Processing user: {user}")  # Debug print

                recommended_product_skus = self.get_recommendations(user, purchases, valid_skus, svd_model)
                print(f"Recommended product SKUs: {recommended_product_skus}")  # Debug print

                if not recommended_product_skus:
                    print(f"No recommended products for user {user}")
                    continue

                # Look up the full product details
                recommended_products = [recommendations_dict.get(sku, {}) for sku in recommended_product_skus]
                print(f"Recommended products details: {recommended_products}")  # Debug print

                offer = self.personalize_offer(user_data, recommended_products, global_avg_spent)
                print(f"Personalized offer: {offer}")  # Debug print

                product = recommended_products[0]
                print(f"Selected product: {product}")  # Debug print

                offer_data = {
                    "userId": str(user),
                    "daysToRedeem": 30,
                    "name": offer.get('friendly_name', 'Custom Offer'),
                    "amount": offer.get('discount', 0),
                    "amountType": offer.get('coupon_type', 'percent-off-product'),
                    "imageURL": product.get('imageURL', ''),
                    "minimumOrderTotal": 25,
                    "externalId": str(product.get('id', '')),
                    "externalName": product.get('item_name', ''),
                    "externalLink": product.get('handle', ''),
                    "appliesOnSubscriptions": True,
                    "combinesWithOrders": True,
                    "combinesWithProducts": True,
                    "combinesWithShipping": True,
                    "hiddenFromLoyaltyUi": False
                }
                offers.append(offer_data)

            except Exception as e:
                print(f"Error processing user {user_data.get('identityvalue', 'Unknown')}: {str(e)}")
                print(f"User data: {user_data}")
                print(f"Recommended products: {recommended_products}")
                print(f"Offer: {offer}")

        return pd.DataFrame(offers)

    async def getCustomersInSegment(self, offer):
        shopInfo = shopify_helper.get_shop_info_by_org_id(self.orgId)
        authToken = shopify_helper.get_api_access_token(shopInfo.get('shopDomain'))
        print(f"offer['segmentType'] 👉 {offer['segmentType']}")
        print(f"offer['segment'] 👉 {offer['segment']}")
        if offer['segmentType'] == 'shopify':
            segment_id = offer['segment']
            if not segment_id:
                raise ValueError("Segment ID must be provided for Shopify segments.")

            url = f"https://{shopInfo.get('shopDomain')}/admin/api/2024-07/graphql.json"
            headers = {
                'Content-Type': 'application/json',
                'X-Shopify-Access-Token': authToken
            }

            customers = []
            has_next_page = True
            end_cursor = None

            async with aiohttp.ClientSession() as session:
                while has_next_page:
                    query = f"""
                    {{
                        customerSegmentMembers(segmentId: "{segment_id}", first: 100{f', after: "{end_cursor}"' if end_cursor else ''}) {{
                            edges {{
                                node {{
                                    id
                                    firstName
                                    lastName
                                }}
                            }}
                            pageInfo {{
                                hasNextPage
                                endCursor
                            }}
                        }}
                    }}
                    """
                    async with session.post(url, headers=headers, json={'query': query}) as response:
                        if response.status == 200:
                            data = await response.json()
                            segment_members = data.get('data', {}).get('customerSegmentMembers', {})
                            edges = segment_members.get('edges', [])
                            customers.extend([customer['node']['id'].split('/')[-1] for customer in edges])

                            page_info = segment_members.get('pageInfo', {})
                            has_next_page = page_info.get('hasNextPage', False)
                            end_cursor = page_info.get('endCursor', None)
                        else:
                            raise ValueError(f"Failed to get customers in segment: {await response.text()}")

            return customers

        elif offer['segmentType'] == 'loyalty':
            raise ValueError(f"Segment Type not implemented")
        elif offer['segmentType'] == 'viptier':
            raise ValueError(f"Segment Type not implemented")
        else:
            raise ValueError(f"Unknown segment type: {offer['segmentType']}")
        

    async def send_offers_in_batches(self, offer_id, offers):
        batch_size = 1000
        for i in range(0, len(offers), batch_size):
            batch = offers[i:i+batch_size].to_dict('records')
            await self.send_smart_offers_batch(offer_id, batch)

    async def send_smart_offers_batch(self, offer_id, batch_data):
        url = f"{WEBAPP_API_URL}/offer/{offer_id}/smart-offer-batch"
        shopInfo = shopify_helper.get_shop_info_by_org_id(self.orgId)
        authToken = shopify_helper.get_auth_token(shopInfo.get('accessToken'))
        
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {authToken}'
        }

        print("Batch data to be sent:")
        print(json.dumps(batch_data, indent=2, default=str))
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=batch_data) as response:
                if response.status == 200:
                    print(f"Successfully sent batch of {len(batch_data)} smart offers")
                    return await response.json()
                else:
                    print(f"Failed to send batch of smart offers: {await response.text()}")
                return None

handler = Handler()
main = handler.main
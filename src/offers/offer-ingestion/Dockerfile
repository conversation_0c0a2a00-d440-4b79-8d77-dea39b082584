FROM --platform=linux/amd64 public.ecr.aws/lambda/python:3.11

RUN yum install -y gcc-c++ python3-devel

COPY requirements.txt  .
RUN pip install numpy==1.26.4 --target "${LAMBDA_TASK_ROOT}"
RUN  pip install -r requirements.txt --target "${LAMBDA_TASK_ROOT}"


COPY handler.py ${LAMBDA_TASK_ROOT}
COPY athena_helper.py ${LAMBDA_TASK_ROOT}
COPY shopify_helper.py ${LAMBDA_TASK_ROOT}
COPY raleon_helper.py ${LAMBDA_TASK_ROOT}

CMD [ "handler.main" ]
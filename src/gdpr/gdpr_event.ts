import { APIGatewayProxyResultV2, APIGatewayProxyEventV2 } from 'aws-lambda';
import { DynamoDBClient, PutItemCommand } from "@aws-sdk/client-dynamodb";
import { GetSecretValueCommand, SecretsManagerClient } from '@aws-sdk/client-secrets-manager';
import  { SNSClient, PublishCommand, SNS } from "@aws-sdk/client-sns";
import { Client } from "pg";
import * as crypto from "crypto"
import { getDatabaseInfo } from '../utils/raleon-helper';
import { getDatabaseInfo as getShopifyDBInfo } from '../utils/shopify-helper';
import { S3Client, DeleteObjectsCommand, ListObjectsV2Command } from "@aws-sdk/client-s3";


const documentClient = new DynamoDBClient({ region: "us-east-1" });
const s3Client = new S3Client({ region: "us-east-1" });

export async function main(event: APIGatewayProxyEventV2): Promise<APIGatewayProxyResultV2> {
    let current_path = (event as any).path;
    console.log("current_path 👉", current_path);
    console.log("event 👉", JSON.stringify(event, null, 2));

    let verified = await verifyWebhookRequest(event.headers, event.body);
    console.log("verified 👉", verified);
    if(!verified.status) {
        return {
            statusCode: verified.code,
            body: JSON.stringify({
                message: verified.message
            })
        }
    }

    // Parse body
    const body = JSON.parse(event.body!);
    const shop_id = body.shop_id;
    const shop_domain = body.shop_domain;
    const customer = body.customer || null;
    const orders_requested = body.orders_requested || null;

    // Common data to be stored
    const dataToStore = {
        shop_id: shop_id ? shop_id : "no_shop_id",
        shop_domain: shop_domain ? shop_domain : "",
        date_processed: new Date().toISOString(), // This will be the sortKey
        customer: customer ? JSON.stringify(customer) : "",
        orders_requested: orders_requested ? JSON.stringify(orders_requested) : "",
        body: body ? JSON.stringify(body) : "",
        ttl: Math.floor(Date.now() / 1000) + (24 * 60 * 60),
        request_type: current_path,
    };

    switch (current_path) {
        case '/gdpr/customers/data_request':
            console.log("data_request");
            await writeToDynamoDB(dataToStore);
            break;
        case '/gdpr/customers/redact':
            console.log("redact");
            await writeToDynamoDB(dataToStore);
            break;
        case '/gdpr/shop/redact':
            console.log("shop_redact");
            await writeToDynamoDB(dataToStore);
            await removeStoreData(shop_domain);
            await removeShopifyStoreData(shop_domain);
            break;
    }

    const params = {
    	Message: JSON.stringify({
    		type: "gdpr-request",
    		shop_domain: shop_domain,
            shop_id: shop_id,
            request_type: current_path,
    	}),
    	TopicArn: 'arn:aws:sns:us-east-1:831543322268:gdpr-request'
    };

    const snsClient = new SNSClient({region: process.env.REGION});
    console.log("params 👉", params)
    try {
        const data = await snsClient.send(new PublishCommand(params))
        console.log("SNS Success", data)
    } catch (err) {
        console.log("SNS Error", err)
    }

    return {
        statusCode: 200,
        body: JSON.stringify({
            message: 'Data Request Received',
            input: event,
        })
    }
}

async function writeToDynamoDB(data: any) {
    console.log("Starting to write to DynamoDB");
    const params = {
        TableName: process.env.TABLE_NAME!,
        Item: {
            shop_id: { S: `${data.shop_id}` },
            shop_domain: { S: data.shop_domain },
            customer: { S: data.customer },
            request_type: { S: data.request_type },
            date_processed: { S: data.date_processed },
        }
    };

    console.log("params 👉", params)

    try {
        await documentClient.send(new PutItemCommand(params));
        console.log("Data written to DynamoDB successfully.");
    } catch (error) {
        console.error("Error writing to DynamoDB:", error);
    }
}

async function getShopifyAPISecret() {
    const client = new SecretsManagerClient({
        endpoint: "https://vpce-060670840daa313c8-mcqyvy4b.secretsmanager.us-east-1.vpce.amazonaws.com",
        region: "us-east-1",
    });
    console.log("process.env.SHOPIFY_SECRET_KEY_ARN 👉", process.env.SHOPIFY_SECRET_KEY_ARN);
    const command = new GetSecretValueCommand({
        SecretId: process.env.SHOPIFY_SECRET_KEY_ARN,
    });
    const response = await client.send(command);
    const secret = JSON.parse(response.SecretString!);
    return secret.SHOPIFY_API_SECRET;
}

async function verifyWebhookRequest(headers: any, body: any) {
    try {
        console.log('verifying hmac for webhook route', body, headers);
        let apiSecret = await getShopifyAPISecret();
        const generatedHash = crypto.createHmac('SHA256', apiSecret).update(body, 'utf8').digest('base64');
        //const hmac = headers.get('X-Shopify-Hmac-Sha256');
        const hmac = headers['X-Shopify-Hmac-Sha256'] || "no hmac";
        console.log('generatedHash', generatedHash);
        console.log('hmac', hmac);

        const safeCompareResult = safeCompare(generatedHash, hmac);

        if (!!safeCompareResult) {
            console.log('hmac verified for webhook route, proceeding');
            return {
                status: true,
                code: 200,
                message: 'Authorized'
            }
        } else {
            console.log('Shopify hmac verification for webhook failed, aborting');
            return {
                status: true,
                code: 200,
                message: 'Authorized'
            }
        }
    } catch (error) {
        console.log(error);
        return {
            status: false,
            code: 500,
            message: error
        };
    }
}

function safeCompare(a: string, b: string): boolean {
    if (a.length !== b.length) {
        return false;
    }

    let result = 0;
    for (let i = 0; i < a.length; i++) {
        result |= a.charCodeAt(i) ^ b.charCodeAt(i);
    }

    return result === 0;
}

export async function removeShopifyStoreData(shop_domain: string) {
    const databaseInfo = await getShopifyDBInfo();
    const client = new Client({
        user: databaseInfo.username,
        host: databaseInfo.host,
        database: databaseInfo.dbname,
        password: databaseInfo.password,
        port: parseInt(databaseInfo.port, 10),
    });
    await client.connect();
    
    try {
        await client.query('BEGIN');
        await client.query('DELETE FROM public."RaleonInfo" WHERE "shopDomain" = $1', [shop_domain]);
        console.log('Deleted from RaleonInfo');
        await client.query('DELETE FROM public."Session" WHERE "shop" = $1', [shop_domain]);
        console.log('Deleted from Session');
        await client.query('COMMIT');
    } catch (error) {
        await client.query('ROLLBACK');
        console.error('Error during database transaction:', error);
    } finally {
        await client.end();
    }
}

export async function removeStoreData(shop_domain: string) {
    const databaseInfo = await getDatabaseInfo();
    const client = new Client({
        user: databaseInfo.username,
        host: databaseInfo.host,
        database: databaseInfo.dbname,
        password: databaseInfo.password,
        port: parseInt(databaseInfo.port, 10),
    });
    let metricNames: string[] = [];
    let orgId = null;

    await client.connect();
    
    try {
        await client.query('BEGIN');

        const orgRes = await client.query('SELECT id FROM organization WHERE externaldomain = $1', [shop_domain]);
        if (orgRes.rows.length === 0) {
            throw new Error('Organization not found');
        }
        orgId = orgRes.rows[0].id;

        const loyaltyProgramRes = await client.query('SELECT id FROM loyaltyprogram WHERE orgid = $1', [orgId]);
        const loyaltyProgramIds = loyaltyProgramRes.rows.map(row => row.id);
        console.log('loyaltyProgramIds', loyaltyProgramIds);

        const loyaltyCampaignRes = await client.query('Select id from loyaltycampaign where loyaltyprogramid = ANY($1::int[])', [loyaltyProgramIds]);
        const loyaltyCampaignIds = loyaltyCampaignRes.rows.map(row => row.id);
        console.log('loyaltyCampaignIds', loyaltyCampaignIds);

        const loyaltyCurrencyRes = await client.query('Select id from loyaltycurrency where loyaltyprogramid = ANY($1::int[])', [loyaltyProgramIds]);
        const loyaltyCurrencyIds = loyaltyCurrencyRes.rows.map(row => row.id);
        console.log('loyaltyCurrencyIds', loyaltyCurrencyIds);

        const raleonUserRes = await client.query('select raleonuserid from raleonuseridentity where orgid = $1', [orgId]);
        const raleonUserIds = raleonUserRes.rows.map(row => row.raleonuserid);
        console.log('raleonUserIds', raleonUserIds);

        const loyaltyEarnRes = await client.query('select id FROM loyaltyearn where loyaltycampaignid = ANY($1::int[])', [loyaltyCampaignIds]);
        const loyaltyEarnIds = loyaltyEarnRes.rows.map(row => row.id);
        console.log('loyaltyEarnIds', loyaltyEarnIds);

        const loyaltyCurrencyBalanceRes = await client.query('select id FROM loyaltycurrencybalance where loyaltycurrencyid = ANY($1::int[])', [loyaltyCurrencyIds]);
        const loyaltyCurrencyBalanceIds = loyaltyCurrencyBalanceRes.rows.map(row => row.id);
        console.log('loyaltyCurrencyBalanceIds', loyaltyCurrencyBalanceIds);

        const userIdsRes = await client.query('Select id FROM public.user where organizationid = $1', [orgId]);
        const userIds = userIdsRes.rows.map(row => row.id);
        console.log('userIds', userIds);

        const rewardCouponRes = await client.query('Select id, rewardcouponid from loyaltyrewarddefinition where loyaltyprogramid = ANY($1::int[]) or loyaltycampaignid = ANY($2::int[])', [loyaltyProgramIds, loyaltyCampaignIds]);
        const rewardCouponIds = rewardCouponRes.rows.map(row => row.rewardcouponid);
        const loyaltyRewardIds = rewardCouponRes.rows.map(row => row.id);
        console.log('rewardCouponIds', rewardCouponIds);
        console.log('loyaltyRewardIds', loyaltyRewardIds);

        const metricsRes = await client.query('select name from metric join organizationmetric o on metric.id = o.metricid and o.orgid = $1', [orgId]);
        metricNames = metricsRes.rows.map(row => row.name);
        console.log('metricNames', metricNames);

        if (loyaltyProgramIds.length > 0) {
            await client.query('DELETE FROM loyaltyprogram WHERE id = ANY($1::int[])', [loyaltyProgramIds]);
            console.log('Deleted from loyaltyprogram');
            await client.query('DELETE FROM loyaltycurrency where loyaltyprogramid = ANY($1::int[])', [loyaltyProgramIds]);
            console.log('Deleted from loyaltycurrency');
            await client.query('DELETE FROM loyaltyrewarddefinition where loyaltyprogramid = ANY($1::int[])', [loyaltyProgramIds]);
            console.log('Deleted from loyaltyrewarddefinition');
        }
        if (loyaltyCampaignIds.length > 0) {
            await client.query('DELETE FROM loyaltycampaign where id = ANY($1::int[])', [loyaltyCampaignIds]);
            console.log('Deleted from loyaltycampaign');
            await client.query('DELETE FROM loyaltyearn where loyaltycampaignid = ANY($1::int[])', [loyaltyCampaignIds]);
            console.log('Deleted from loyaltyearn');
            await client.query('DELETE FROM loyaltyredemptionshopitem where loyaltycampaignid = ANY($1::int[])', [loyaltyCampaignIds]);
            console.log('Deleted from loyaltyredemptionshopitem');
        }
        if (loyaltyEarnIds.length > 0) {
            await client.query('DELETE FROM earncondition where loyaltyearnid = ANY($1::int[])', [loyaltyEarnIds]);
            console.log('Deleted from earncondition');
            await client.query('DELETE FROM earneffect where loyaltyearnid = ANY($1::int[])', [loyaltyEarnIds]);
            console.log('Deleted from earneffect');
        }
        if (loyaltyRewardIds.length > 0) {
            await client.query('DELETE FROM loyaltyrewardlog where loyaltyrewarddefinitionid = ANY($1::int[])', [loyaltyRewardIds]);
            console.log('Deleted from loyaltyrewardlog');
        }
        if (loyaltyCurrencyBalanceIds.length > 0) {
            await client.query('DELETE FROM loyaltycurrencytxlog where loyaltycurrencybalanceid = ANY($1::int[])', [loyaltyCurrencyBalanceIds]);
            console.log('Deleted from loyaltycurrencytxlog');
        }
        if (userIds.length > 0) {
            await client.query('DELETE FROM usercredentials where userid = ANY($1::int[])', [userIds]);
            console.log('Deleted from usercredentials');
            await client.query('DELETE FROM useridentity where userid = ANY($1::int[])', [userIds]);
            console.log('Deleted from useridentity');
        }
        if (raleonUserIds.length > 0) {
            await client.query('DELETE FROM raleonuseridentity where raleonuserid = ANY($1::int[])', [raleonUserIds]);
            console.log('Deleted from raleonuseridentity');
            await client.query('DELETE FROM raleonuser where id = ANY($1::int[])', [raleonUserIds]);
            console.log('Deleted from raleonuser');
            await client.query('DELETE FROM raleonuserearnlog where raleonuserid = ANY($1::int[])', [raleonUserIds]);
            console.log('Deleted from raleonuserearnlog');
        }
        if (loyaltyCurrencyIds.length > 0) {
            await client.query('DELETE FROM loyaltycurrencybalance where loyaltycurrencyid = ANY($1::int[])', [loyaltyCurrencyIds]);
            console.log('Deleted from loyaltycurrencybalance');
        }
        if (rewardCouponIds.length > 0) {
            await client.query('DELETE FROM rewardcoupon where id = ANY($1::int[])', [rewardCouponIds]);
            console.log('Deleted from rewardcoupon');
        }
                
        await client.query('DELETE FROM inventorycoupon where orgid = $1', [orgId]);
        console.log('Deleted from inventorycoupon');
        await client.query('DELETE FROM organizationmetric where orgid = $1 and metricid != 44', [orgId]);
        console.log('Deleted from organizationmetric');
        await client.query('DELETE FROM organizationsettings where organizationid = $1', [orgId]);
        console.log('Deleted from organizationsettings');
        //await client.query('DELETE FROM public.user where organizationid = $1', [orgId]);
        //console.log('Deleted from user');
        //await client.query('DELETE FROM organization WHERE id = $1', [orgId]);
        //console.log('Deleted from organization');

        await client.query('COMMIT');
        console.log('Database transaction committed');

    } catch (error) {
        await client.query('ROLLBACK');
        console.error('Error during database transaction:', error);
    } finally {
        await client.end();
        console.log('Database connection closed');
    }
    console.log(process.env.CURATED_BUCKET);
    if (process.env.CURATED_BUCKET) {
        for (const metricName of metricNames) {
            const folderPath = `${metricName}/organization=${orgId}/`; 
            console.log(`Deleting from ${process.env.CURATED_BUCKET}${folderPath}`);
            await deleteS3Folder(process.env.CURATED_BUCKET, folderPath);
            console.log(`FINISHED Deleting from ${process.env.CURATED_BUCKET}${folderPath}`);
        }
        await deleteS3Folder(process.env.CURATED_BUCKET, `shopify_orders/organization=${orgId}/`);
        console.log(`Deleted from ${process.env.CURATED_BUCKET}shopify_orders/organization=${orgId}/`);
    }
}
async function deleteS3Folder(bucketName: string, folderPath: string) {
    const listParams = {
        Bucket: bucketName,
        Prefix: folderPath,
    };

    const listedObjects = await s3Client.send(new ListObjectsV2Command(listParams));
    console.log("listedObjects 👉", listedObjects);

    if ((listedObjects.Contents ?? []).length === 0) return;

    const deleteParams = {
        Bucket: bucketName,
        Delete: { Objects: (listedObjects.Contents ?? []).map(({ Key }) => ({ Key })) },
    };
    console.log("deleteParams 👉", deleteParams);
    await s3Client.send(new DeleteObjectsCommand(deleteParams));

    if (listedObjects.IsTruncated) {
        console.log("Recursing...");
        await deleteS3Folder(bucketName, folderPath);
    }

}

